﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.3.32804.467
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{FAE84792-E6E3-462E-9DCA-C80E2CB53CE4}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		NuGet.Config = NuGet.Config
		README.md = README.md
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AccountsProduction.AccountsBuilder.Api", "AccountsProduction.AccountsBuilder.Api\AccountsProduction.AccountsBuilder.Api.csproj", "{********-1111-1111-1111-********1111}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AccountsProduction.AccountsBuilder.Application", "AccountsProduction.AccountsBuilder.Application\AccountsProduction.AccountsBuilder.Application.csproj", "{6364B096-B7CC-4C01-9D28-13A4AC7880C2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AccountsProduction.AccountsBuilder.Domain", "AccountsProduction.AccountsBuilder.Domain\AccountsProduction.AccountsBuilder.Domain.csproj", "{4660D300-7EE0-4E0E-80F2-9A0D5752F234}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AccountsProduction.AccountsBuilder.Infrastructure", "AccountsProduction.AccountsBuilder.Infrastructure\AccountsProduction.AccountsBuilder.Infrastructure.csproj", "{********-CF2A-4A47-84DB-C30122E1845E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AccountsProduction.AccountsBuilder.AggregatorFunc", "AccountsProduction.AccountsBuilder.AggregatorFunction\AccountsProduction.AccountsBuilder.AggregatorFunc.csproj", "{69F9E852-41EA-40F8-B70E-3842D05FD833}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AccountsProduction.AccountsBuilder.AggregatorFunc.Application", "AccountsProduction.AccountsBuilder.AggregatorFunc.Application\AccountsProduction.AccountsBuilder.AggregatorFunc.Application.csproj", "{C7A8AD9D-E94E-4608-908C-07F294807B2B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AccountsProduction.AccountsBuilder.Application.Common", "AccountsProduction.AccountsBuilder.Application.Common\AccountsProduction.AccountsBuilder.Application.Common.csproj", "{DEF60811-D3CD-4A59-9005-DF4F2BE00425}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AccountsProduction.AccountsBuilder.UnitTests", "AccountsProduction.AccountsBuilder.UnitTests\AccountsProduction.AccountsBuilder.UnitTests.csproj", "{********-2222-2222-2222-********2222}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{********-1111-1111-1111-********1111}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-1111-1111-1111-********1111}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-1111-1111-1111-********1111}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-1111-1111-1111-********1111}.Release|Any CPU.Build.0 = Release|Any CPU
		{6364B096-B7CC-4C01-9D28-13A4AC7880C2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6364B096-B7CC-4C01-9D28-13A4AC7880C2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6364B096-B7CC-4C01-9D28-13A4AC7880C2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6364B096-B7CC-4C01-9D28-13A4AC7880C2}.Release|Any CPU.Build.0 = Release|Any CPU
		{4660D300-7EE0-4E0E-80F2-9A0D5752F234}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4660D300-7EE0-4E0E-80F2-9A0D5752F234}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4660D300-7EE0-4E0E-80F2-9A0D5752F234}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4660D300-7EE0-4E0E-80F2-9A0D5752F234}.Release|Any CPU.Build.0 = Release|Any CPU
		{********-CF2A-4A47-84DB-C30122E1845E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-CF2A-4A47-84DB-C30122E1845E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-CF2A-4A47-84DB-C30122E1845E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-CF2A-4A47-84DB-C30122E1845E}.Release|Any CPU.Build.0 = Release|Any CPU
		{69F9E852-41EA-40F8-B70E-3842D05FD833}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{69F9E852-41EA-40F8-B70E-3842D05FD833}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{69F9E852-41EA-40F8-B70E-3842D05FD833}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{69F9E852-41EA-40F8-B70E-3842D05FD833}.Release|Any CPU.Build.0 = Release|Any CPU
		{C7A8AD9D-E94E-4608-908C-07F294807B2B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C7A8AD9D-E94E-4608-908C-07F294807B2B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C7A8AD9D-E94E-4608-908C-07F294807B2B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C7A8AD9D-E94E-4608-908C-07F294807B2B}.Release|Any CPU.Build.0 = Release|Any CPU
		{DEF60811-D3CD-4A59-9005-DF4F2BE00425}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DEF60811-D3CD-4A59-9005-DF4F2BE00425}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DEF60811-D3CD-4A59-9005-DF4F2BE00425}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DEF60811-D3CD-4A59-9005-DF4F2BE00425}.Release|Any CPU.Build.0 = Release|Any CPU
		{********-2222-2222-2222-********2222}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-2222-2222-2222-********2222}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-2222-2222-2222-********2222}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-2222-2222-2222-********2222}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A38E17E6-1E3D-4B23-9034-E8FC078F3696}
	EndGlobalSection
EndGlobal
