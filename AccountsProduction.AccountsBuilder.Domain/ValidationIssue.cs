﻿namespace AccountsProduction.AccountsBuilder.Domain
{
    public class ValidationIssue
    {
        public string Name { get; set; }
        public string ErrorCategory { get; set; }
        public string Type { get; set; }
        public string DisplayName { get; set; }
        public string Breadcrumb { get; set; }
        public string Description { get; set; }
        public string Target { get; set; }
        public string ErrorCode { get; set; }

        public static ValidationIssue BuildValidationIssue(string errorCode, string name, string displayName, string breadcrumb,
            string description, string type, string target, string errorCategoryType)
        {
            return new ValidationIssue
            {
                ErrorCode = errorCode,
                Target = target,
                Name = name,
                ErrorCategory = errorCategoryType,
                Type = type,
                DisplayName = displayName,
                Breadcrumb = breadcrumb,
                Description = description
            };
        }
    }

    public static class ErrorCategoryType
    {
        public const string Mandatory = "Mandatory";
        public const string Advisory = "Advisory";
    }

    public static class ValidationRuleType
    {
        public const string Missing = "Missing";
        public const string Invalid = "Invalid";
    }

    public static class Target
    {
        public const string IssueLog = "IssueLog";
        public const string SectionValidation = "SectionValidation";
    }
}