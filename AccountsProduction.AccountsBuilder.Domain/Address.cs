﻿using Newtonsoft.Json.Linq;

namespace AccountsProduction.AccountsBuilder.Domain
{
    public class Address
    {
        public string Line1 { get; set; }

        public string Line2 { get; set; }

        public string Town { get; set; }

        public string County { get; set; }

        public string PostCode { get; set; }

        public void UpdateRecord(JObject payload)
        {
            if (payload.ContainsKey(AddressAttributiveName.Line1))
            {
                Line1 = payload.GetValue(AddressAttributiveName.Line1)?.ToObject<string>();
            }

            if (payload.ContainsKey(AddressAttributiveName.Line2))
            {
                Line2 = payload.GetValue(AddressAttributiveName.Line2)?.ToObject<string>();
            }

            if (payload.ContainsKey(AddressAttributiveName.Town))
            {
                Town = payload.GetValue(AddressAttributiveName.Town)?.ToObject<string>();
            }

            if (payload.ContainsKey(AddressAttributiveName.County))
            {
                County = payload.GetValue(AddressAttributiveName.County)?.ToObject<string>();
            }

            if (payload.ContainsKey(AddressAttributiveName.PostCode))
            {
                PostCode = payload.GetValue(AddressAttributiveName.PostCode)?.ToObject<string>();
            }
        }
    }
    internal static class AddressAttributiveName
    {
        public static readonly string Line1 = "line1";
        public static readonly string Line2 = "line2";
        public static readonly string Town = "town";
        public static readonly string PostCode = "postcode";
        public static readonly string County = "county";
    }
}
