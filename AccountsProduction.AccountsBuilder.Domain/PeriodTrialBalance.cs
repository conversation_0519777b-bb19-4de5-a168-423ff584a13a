﻿using Amazon.DynamoDBv2.DataModel;
using Iris.Elements.DynamoDb.Converters;

namespace AccountsProduction.AccountsBuilder.Domain
{
    public class PeriodTrialBalance
    {
        public int AccountCode { get; set; }

        public decimal Amount { get; set; }

        public int? SubAccountCode { get; set; }

        [DynamoDBProperty(typeof(DateTimeConverter))]
        public DateTime Year { get; set; }

        public string Description { get; set; }

        public Guid? PeriodId { get; set; }

        public int? DirectorInvolvementId { get; set; }

        public Guid? SectorId { get; set; }

        [DynamoDBProperty(typeof(DateTimeConverter))]
        public DateTime? SectorCreatedDate { get; set; }

        public List<Guid> CharityOperationIds { get; set; } = [];
    }
}
