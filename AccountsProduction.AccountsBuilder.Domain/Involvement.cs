﻿namespace AccountsProduction.AccountsBuilder.Domain
{
    public class Involvement
    {
        public string InvolvementType { get; set; }

        public long InvolvementId { get; set; }

        public Guid InvolvementClientGuid { get; set; }

        public string InvolvedClientType { get; set; }

        public string InvolvementClientName { get; set; }

        public string InvolvementTitle { get; set; }

        public string InvolvementSurname { get; set; }

        public string InvolvementFirstName { get; set; }

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public DateTime? InvolvedDateOfDeath { get; set; }

        public int PdoCode { get; set; }

        public bool IsDeleted { get; set; } = false;
    }
}
