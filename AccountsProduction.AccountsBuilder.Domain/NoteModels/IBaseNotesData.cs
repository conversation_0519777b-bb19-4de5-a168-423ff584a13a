﻿namespace AccountsProduction.AccountsBuilder.Domain.NoteModels
{
    public interface IBaseNotesData
    {
        AverageNumberOfEmployees? AverageNumberOfEmployees { get; set; }

        string? OffBalanceSheetArrangements { get; set; }

        string? GuaranteesAndOtherFinancialCommitments { get; set; }

        MembersLiabilityText? MembersLiabilityText { get; set; }

        AdditionalNote? AdditionalNote1 { get; set; }

        AdditionalNote? AdditionalNote2 { get; set; }

        string? ControllingPartyNote { get; set; }

        string? RelatedPartyTransactions { get; set; }

        string? LoansAndOtherDebtsDueToMembers { get; set; }

        OperatingProfitLoss? OperatingProfitLoss { get; set; }

        string? IntangibleAssetsRevaluation { get; set; }

        AdvancesCreditAndGuaranteesGrantedToDirectors? AdvancesCreditAndGuaranteesGrantedToDirectorsExtended { get; set; }

        TangibleFixedAssetsNotes? TangibleFixedAssetsNotes { get; set; }
    }
}
