﻿namespace AccountsProduction.AccountsBuilder.Domain.NoteModels
{
    public interface IUnincorporatedNotesData
    {
        MembersLiabilityText MembersLiabilityText { get; set; }

        AdditionalNote AdditionalNote1 { get; set; }

        AdditionalNote AdditionalNote2 { get; set; }

        TangibleFixedAssetsNotes TangibleFixedAssetsNotes { get; set; }
    }

    public class UnincorporatedNotesData : IUnincorporatedNotesData
    {
        MembersLiabilityText IUnincorporatedNotesData.MembersLiabilityText { get; set; }

        AdditionalNote IUnincorporatedNotesData.AdditionalNote1 { get; set; }

        AdditionalNote IUnincorporatedNotesData.AdditionalNote2 { get; set; }

        TangibleFixedAssetsNotes IUnincorporatedNotesData.TangibleFixedAssetsNotes { get; set; }
    }
}
