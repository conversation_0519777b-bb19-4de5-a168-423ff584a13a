﻿using Amazon.DynamoDBv2.DataModel;
using Iris.Elements.DynamoDb.Converters;

namespace AccountsProduction.AccountsBuilder.Domain.NoteModels
{
    public class Notes
    {
        [DynamoDBProperty(typeof(GuidConverter))]
        public Guid ClientId { get; set; }

        [DynamoDBProperty(typeof(GuidConverter))]
        public Guid PeriodId { get; set; }

        [DynamoDBProperty(typeof(GuidConverter))]
        public Guid? PreviousPeriodId { get; set; }

        [DynamoDBProperty(typeof(GuidConverter))]
        public Guid TenantId { get; set; }

        [DynamoDBProperty(typeof(GuidConverter))]
        public Guid CorrelationId { get; set; }

        public NotesData CurrentPeriod { get; set; }

        public NotesData PreviousPeriod { get; set; }

        [DynamoDBIgnore]
        public AverageNumberOfEmployees AverageNumberOfEmployees => GetAverageNumberOfEmployees();

        [DynamoDBIgnore]
        public string OffBalanceSheetArrangements => GetOffBalanceSheetArrangements();

        [DynamoDBIgnore]
        public string AdvancesCreditAndGuaranteesGrantedToDirectors => CurrentPeriod?.AdvancesCreditAndGuaranteesGrantedToDirectors;

        [DynamoDBIgnore]
        public AdvancesCreditAndGuaranteesGrantedToDirectors AdvancesCreditAndGuaranteesGrantedToDirectorsExtended => GetAdvancesCreditAndGuaranteesGrantedToDirectors();

        [DynamoDBIgnore]
        public string GuaranteesAndOtherFinancialCommitments => GetGuaranteesAndOtherFinancialCommitments();

        [DynamoDBIgnore]
        public string RelatedPartyTransactions => GetRelatedPartyTransactions();

        [DynamoDBIgnore]
        public string LoansAndOtherDebtsDueToMembers => GetLoansAndOtherDebtsDueToMembers();

        [DynamoDBIgnore]
        public MembersLiabilityText MembersLiabilityText => GetMembersLiabilityText();

        [DynamoDBIgnore]
        public AdditionalNote AdditionalNote1 => GetAdditionalNote1();

        [DynamoDBIgnore]
        public AdditionalNote AdditionalNote2 => GetAdditionalNote2();

        [DynamoDBIgnore]
        public string ControllingPartyNote => GetControllingPartyNote();

        [DynamoDBIgnore]
        public string IntangibleAssetsRevaluation => GetIntangibleAssetsRevaluation();

        [DynamoDBIgnore]
        public OperatingProfitLoss OperatingProfitLoss => GetOperatingProfitLoss();

        [DynamoDBIgnore]
        public TangibleFixedAssetsNotes TangibleFixedAssetsNotes => GetTangibleFixedAssetsNotes();

        public bool? IsSuccessful { get; set; }

        public string Error { get; set; }

        [DynamoDBProperty(typeof(DateTimeConverter))]
        public DateTime EntityModificationTime { get; set; }

        public void UpdateModificationTime()
        {
            EntityModificationTime = DateTime.UtcNow;
        }

        private AverageNumberOfEmployees GetAverageNumberOfEmployees()
        {
            if (CurrentPeriod != null)
            {
                return new AverageNumberOfEmployees
                {
                    CurrentPeriod = CurrentPeriod.AverageNumberOfEmployees.CurrentPeriod,
                    PreviousPeriod = CurrentPeriod.AverageNumberOfEmployees.PreviousPeriod
                };
            }

            return new AverageNumberOfEmployees
            {
                PreviousPeriod = PreviousPeriod?.AverageNumberOfEmployees?.CurrentPeriod
            };
        }

        private string GetOffBalanceSheetArrangements()
        {
            return CurrentPeriod != null ? CurrentPeriod.OffBalanceSheetArrangements : PreviousPeriod?.OffBalanceSheetArrangements;
        }

        private string GetGuaranteesAndOtherFinancialCommitments()
        {
            return CurrentPeriod != null ? CurrentPeriod.GuaranteesAndOtherFinancialCommitments : PreviousPeriod?.GuaranteesAndOtherFinancialCommitments;
        }

        private string GetRelatedPartyTransactions()
        {
            return CurrentPeriod != null ? CurrentPeriod.RelatedPartyTransactions : PreviousPeriod?.RelatedPartyTransactions;
        }

        private string GetLoansAndOtherDebtsDueToMembers()
        {
            return CurrentPeriod != null ? CurrentPeriod.LoansAndOtherDebtsDueToMembers : PreviousPeriod?.LoansAndOtherDebtsDueToMembers;
        }

        private string GetControllingPartyNote()
        {
            return CurrentPeriod != null ? CurrentPeriod.ControllingPartyNote : PreviousPeriod?.ControllingPartyNote;
        }

        private string GetIntangibleAssetsRevaluation()
        {
            return CurrentPeriod != null ? CurrentPeriod.IntangibleAssetsRevaluation : PreviousPeriod?.IntangibleAssetsRevaluation;
        }

        private MembersLiabilityText GetMembersLiabilityText()
        {
            if (CurrentPeriod != null)
            {
                return new MembersLiabilityText
                {
                    NoteTitle = CurrentPeriod.MembersLiabilityText.NoteTitle,
                    NoteText = CurrentPeriod.MembersLiabilityText.NoteText
                };
            }

            return new MembersLiabilityText
            {
                NoteTitle = PreviousPeriod?.MembersLiabilityText?.NoteTitle,
                NoteText = PreviousPeriod?.MembersLiabilityText?.NoteText
            };
        }

        private AdditionalNote GetAdditionalNote1()
        {
            if (CurrentPeriod != null)
            {
                return new AdditionalNote
                {
                    NoteTitle = CurrentPeriod.AdditionalNote1.NoteTitle,
                    NoteText = CurrentPeriod.AdditionalNote1.NoteText
                };
            }

            return new AdditionalNote
            {
                NoteTitle = PreviousPeriod?.AdditionalNote1?.NoteTitle,
                NoteText = PreviousPeriod?.AdditionalNote1?.NoteText
            };
        }

        private AdditionalNote GetAdditionalNote2()
        {
            if (CurrentPeriod != null)
            {
                return new AdditionalNote
                {
                    NoteTitle = CurrentPeriod.AdditionalNote2.NoteTitle,
                    NoteText = CurrentPeriod.AdditionalNote2.NoteText
                };
            }

            return new AdditionalNote
            {
                NoteTitle = PreviousPeriod?.AdditionalNote2?.NoteTitle,
                NoteText = PreviousPeriod?.AdditionalNote2?.NoteText
            };
        }

        private OperatingProfitLoss GetOperatingProfitLoss()
        {
            if (CurrentPeriod?.OperatingProfitLoss == null && PreviousPeriod?.OperatingProfitLoss == null)
            {
                return null;
            }

            OperatingProfitLoss operatingProfitLoss;


            if (CurrentPeriod != null)
            {
                operatingProfitLoss = new OperatingProfitLoss
                {
                    IsEnabled = CurrentPeriod.OperatingProfitLoss?.IsEnabled ?? false,
                    Items = GetOperatingProfitLossItems(CurrentPeriod)
                };

                return operatingProfitLoss;
            }

            operatingProfitLoss = new OperatingProfitLoss
            {
                IsEnabled = PreviousPeriod?.OperatingProfitLoss?.IsEnabled ?? false,
                Items = GetOperatingProfitLossItems(PreviousPeriod)
            };

            return operatingProfitLoss;

            List<OperatingProfitLossItem> GetOperatingProfitLossItems(NotesData notesData)
            {
                if (notesData?.OperatingProfitLoss?.Items?.Any() ?? false)
                {
                    return notesData.OperatingProfitLoss.Items.Select(src => new OperatingProfitLossItem
                    {
                        Index = src.Index,
                        Description = src.Description,
                        Value = src.Value
                    }).ToList();
                }

                return new List<OperatingProfitLossItem>();
            }
        }

        private AdvancesCreditAndGuaranteesGrantedToDirectors GetAdvancesCreditAndGuaranteesGrantedToDirectors()
        {
            if (CurrentPeriod?.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended == null && PreviousPeriod?.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended == null)
            {
                return null;
            }

            AdvancesCreditAndGuaranteesGrantedToDirectors advancesCreditAndGuaranteesGrantedToDirectors;

            if (CurrentPeriod != null)
            {
                advancesCreditAndGuaranteesGrantedToDirectors = new AdvancesCreditAndGuaranteesGrantedToDirectors
                {
                    Guarantees = CurrentPeriod.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Guarantees,
                    Items = GetAdvancesCreditAndGuaranteesGrantedToDirectorItems(CurrentPeriod)
                };

                return advancesCreditAndGuaranteesGrantedToDirectors;
            }

            advancesCreditAndGuaranteesGrantedToDirectors = new AdvancesCreditAndGuaranteesGrantedToDirectors
            {
                Guarantees = PreviousPeriod?.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Guarantees,
                Items = GetAdvancesCreditAndGuaranteesGrantedToDirectorItemsFromPreviousPeriod(PreviousPeriod)
            };

            return advancesCreditAndGuaranteesGrantedToDirectors;

            List<AdvancesCreditAndGuaranteesGrantedToDirectorItem> GetAdvancesCreditAndGuaranteesGrantedToDirectorItems(NotesData notesData)
            {
                if (notesData?.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended?.Items?.Any() ?? false)
                {
                    return notesData.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items.Select(src => new AdvancesCreditAndGuaranteesGrantedToDirectorItem
                    {
                        Index = src.Index,
                        InvolvementClientGuid = src.InvolvementClientGuid,
                        DirectorName = src.DirectorName,
                        BalanceOutstandingAtStartOfYear = src.BalanceOutstandingAtStartOfYear,
                        AmountsAdvanced = src.AmountsAdvanced,
                        AmountsRepaid = src.AmountsRepaid,
                        AmountsWrittenOff = src.AmountsWrittenOff,
                        AmountsWaived = src.AmountsWaived,
                        BalanceOutstandingAtEndOfYear = src.BalanceOutstandingAtEndOfYear,
                        AdvanceCreditConditions = src.AdvanceCreditConditions
                    }).ToList();
                }
                return new List<AdvancesCreditAndGuaranteesGrantedToDirectorItem>();
            }

            List<AdvancesCreditAndGuaranteesGrantedToDirectorItem> GetAdvancesCreditAndGuaranteesGrantedToDirectorItemsFromPreviousPeriod(NotesData notesData)
            {
                if (notesData?.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended?.Items?.Exists(x => x.BalanceOutstandingAtEndOfYear.HasValue && x.BalanceOutstandingAtEndOfYear != 0) ?? false)
                {
                    return notesData.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items.Where(x => x.BalanceOutstandingAtEndOfYear.HasValue && x.BalanceOutstandingAtEndOfYear != 0).Select(src => new AdvancesCreditAndGuaranteesGrantedToDirectorItem
                    {
                        Index = src.Index,
                        InvolvementClientGuid = src.InvolvementClientGuid,
                        DirectorName = src.DirectorName,
                        // Opening balance of current period is the closing balance of previous 
                        BalanceOutstandingAtStartOfYear = src.BalanceOutstandingAtEndOfYear
                    }).ToList();
                }
                return new List<AdvancesCreditAndGuaranteesGrantedToDirectorItem>();
            }

        }

        private TangibleFixedAssetsNotes GetTangibleFixedAssetsNotes()
        {
            return new TangibleFixedAssetsNotes
            {
                ValuationInCurrentReportingPeriod = this.GetValuationInCurrentReportingPeriod(),
                HistoricalCostBreakdown = this.GetHistoricalCostBreakdown(),
                AnalysisOfCostOrValuation = this.GetAnalysisOfCostOrValuation()
            };
        }

        private ValuationInCurrentReportingPeriod GetValuationInCurrentReportingPeriod()
        {
            var independentValuerInvolvedValue = false;

            if (CurrentPeriod?.TangibleFixedAssetsNotes?.ValuationInCurrentReportingPeriod?.IndependentValuerInvolved != null)
            {
                independentValuerInvolvedValue = CurrentPeriod.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.IndependentValuerInvolved;
            }
            else
            {
                independentValuerInvolvedValue = PreviousPeriod?.TangibleFixedAssetsNotes?.ValuationInCurrentReportingPeriod?.IndependentValuerInvolved ??
                    independentValuerInvolvedValue;
            }

            return new ValuationInCurrentReportingPeriod
            {
                ValuationDetails = CurrentPeriod?.TangibleFixedAssetsNotes?.ValuationInCurrentReportingPeriod?.ValuationDetails ??
                    PreviousPeriod?.TangibleFixedAssetsNotes?.ValuationInCurrentReportingPeriod?.ValuationDetails,
                IndependentValuerInvolved = independentValuerInvolvedValue,
                RevaluationBasis = CurrentPeriod?.TangibleFixedAssetsNotes?.ValuationInCurrentReportingPeriod?.RevaluationBasis ??
                    PreviousPeriod?.TangibleFixedAssetsNotes?.ValuationInCurrentReportingPeriod?.RevaluationBasis,
                DateOfRevaluation = CurrentPeriod?.TangibleFixedAssetsNotes?.ValuationInCurrentReportingPeriod?.DateOfRevaluation ??
                    DateTime.MinValue
            };
        }

        private HistoricalCostBreakdown GetHistoricalCostBreakdown()
        {

            var currentPeriodHistoricalCostBreakdown = CurrentPeriod?.TangibleFixedAssetsNotes?.HistoricalCostBreakdown;
            var perviousPeriodHistoricalCostBreakdown = PreviousPeriod?.TangibleFixedAssetsNotes?.HistoricalCostBreakdown;
            return new HistoricalCostBreakdown
            {
                RevaluedAssetClass = currentPeriodHistoricalCostBreakdown?.RevaluedAssetClass ?? perviousPeriodHistoricalCostBreakdown?.RevaluedAssetClass,
                RevaluedClassPronoun = currentPeriodHistoricalCostBreakdown?.RevaluedClassPronoun ?? perviousPeriodHistoricalCostBreakdown?.RevaluedClassPronoun,
                CurrentReportingPeriodCost = currentPeriodHistoricalCostBreakdown?.CurrentReportingPeriodCost ?? perviousPeriodHistoricalCostBreakdown?.CurrentReportingPeriodCost,
                CurrentReportingPeriodAccumulatedDepreciation = currentPeriodHistoricalCostBreakdown?.CurrentReportingPeriodAccumulatedDepreciation ?? perviousPeriodHistoricalCostBreakdown?.CurrentReportingPeriodAccumulatedDepreciation,
            };

        }

        private AnalysisOfCostOrValuation GetAnalysisOfCostOrValuation()
        {
            return CurrentPeriod?.TangibleFixedAssetsNotes?.AnalysisOfCostOrValuation != null ?
                    CurrentPeriod?.TangibleFixedAssetsNotes?.AnalysisOfCostOrValuation :
                    PreviousPeriod?.TangibleFixedAssetsNotes?.AnalysisOfCostOrValuation;
        }
    }
}