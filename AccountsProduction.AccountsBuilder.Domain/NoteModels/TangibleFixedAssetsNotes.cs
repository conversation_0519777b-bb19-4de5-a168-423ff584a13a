﻿namespace AccountsProduction.AccountsBuilder.Domain.NoteModels
{
    public class TangibleFixedAssetsNotes
    {
        public ValuationInCurrentReportingPeriod ValuationInCurrentReportingPeriod { get; set; }
        public HistoricalCostBreakdown HistoricalCostBreakdown { get; set; }

        public AnalysisOfCostOrValuation AnalysisOfCostOrValuation { get; set; }
    }

    public class ValuationInCurrentReportingPeriod
    {
        public string? ValuationDetails { get; set; }
        public bool IndependentValuerInvolved { get; set; }
        public string? RevaluationBasis { get; set; }
        public DateTime DateOfRevaluation { get; set; }
    }


    public class HistoricalCostBreakdown
    {
        public string? RevaluedAssetClass { get; set; }
        public string? RevaluedClassPronoun { get; set; }
        public decimal? CurrentReportingPeriodCost { get; set; }
        public decimal? CurrentReportingPeriodAccumulatedDepreciation { get; set; }
    }

    public class AnalysisOfCostOrValuation
    {
        public List<AnalysisOfCostOrValuationItem> AnalysisOfCostOrValuationItems { get; set; }
        public decimal? CostLandAndBuildings { get; set; }
        public decimal? CostPlantAndMachineryEtc { get; set; }
        public decimal? TotalLandAndBuildings { get; set; }
        public decimal? TotalPlantAndMachineryEtc { get; set; }
    }
    public class AnalysisOfCostOrValuationItem
    {
        public int Index { get; set; }
        public int Year { get; set; }
        public decimal? LandAndBuildings { get; set; }
        public decimal? PlantAndMachineryEtc { get; set; }
    }
}
