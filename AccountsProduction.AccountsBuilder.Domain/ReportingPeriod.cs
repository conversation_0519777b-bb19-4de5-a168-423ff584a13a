﻿using Amazon.DynamoDBv2.DataModel;
using Iris.Elements.DynamoDb.Converters;

namespace AccountsProduction.AccountsBuilder.Domain
{
    public class ReportingPeriod
    {
        public Guid Id { get; set; }

        [DynamoDBProperty(typeof(DateTimeConverter))]
        public DateTime EndDate { get; set; }

        [DynamoDBProperty(typeof(DateTimeConverter))]
        public DateTime? StartDate { get; set; }
    }
}
