﻿namespace AccountsProduction.AccountsBuilder.Domain.DataScreens
{
    public class DataScreenValue
    {
        public Guid TenantId { get; set; }
        public Guid ClientId { get; set; }
        public Guid PeriodId { get; set; }
        public Guid? PreviousPeriodId { get; set; }
        public Guid CorrelationId { get; set; } = Guid.NewGuid();
        public List<PeriodScreenValue> CurrentPeriod { get; set; } = new List<PeriodScreenValue>();
        public List<PeriodScreenValue> PreviousPeriod { get; set; } = new List<PeriodScreenValue>();
        public string Error { get; set; }
        public bool? IsSuccessful { get; set; }
    }
}
