﻿using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;

namespace AccountsProduction.AccountsBuilder.Domain.DataScreens
{
    [ExcludeFromCodeCoverage]
    public class ScreenField
    {
        public string Name { get; set; }

        [DynamoDBProperty(Converter = typeof(PolymorphicConverter))]
        public object? Value { get; set; }
    }

    [ExcludeFromCodeCoverage]
    public class PolymorphicConverter : IPropertyConverter
    {
        public DynamoDBEntry ToEntry(object value)
        {
            if (value == null)
            {
                return new DynamoDBNull();
            }

            var typeName = value.GetType().FullName;

            return typeName switch
            {
                "System.Int32" => new Primitive(value.ToString(), true),
                "System.Int64" => new Primitive(value.ToString(), true),
                "System.Single" => new Primitive(value.ToString(), true),
                "System.Double" => new Primitive(value.ToString(), true),
                "System.Decimal" => new Primitive(value.ToString(), true),
                "System.Boolean" => new DynamoDBBool((bool)value),
                "System.String" => new Primitive(value.ToString(), false),
                "System.Text.Json.JsonElement" => HandleJsonElement((JsonElement)value),
                _ => throw new ArgumentException($"Unsupported type: {typeName}")
            };
        }

        public object? FromEntry(DynamoDBEntry entry)
        {
            if (entry is DynamoDBNull)
            {
                return null;
            }

            if (entry is DynamoDBBool boolEntry)
            {
                return boolEntry.AsBoolean();
            }

            if (entry is Primitive primitive)
            {
                return primitive.Type switch
                {
                    DynamoDBEntryType.Numeric => primitive.AsDouble(),
                    DynamoDBEntryType.String => primitive.AsString(),
                    _ => throw new ArgumentException($"Unsupported DynamoDBEntry type: {primitive.Type}")
                };
            }

            throw new ArgumentException($"Unsupported DynamoDBEntry type: {entry.GetType()}");
        }

        private static DynamoDBEntry HandleJsonElement(JsonElement jsonElement)
        {
            return jsonElement.ValueKind switch
            {
                JsonValueKind.Null => new DynamoDBNull(),
                JsonValueKind.Number => new Primitive(jsonElement.GetRawText(), true),
                JsonValueKind.String => new Primitive(jsonElement.GetRawText()),
                JsonValueKind.True => new DynamoDBBool(true),
                JsonValueKind.False => new DynamoDBBool(false),
                _ => throw new ArgumentException($"Unsupported System.Text.Json.JsonElement value kind: {jsonElement.ValueKind}")
            };
        }
    }
};
