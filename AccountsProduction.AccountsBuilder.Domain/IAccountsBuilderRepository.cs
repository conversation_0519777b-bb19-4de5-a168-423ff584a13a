﻿namespace AccountsProduction.AccountsBuilder.Domain
{
    public interface IAccountsBuilderRepository
    {
        Task<AccountsBuilderModels.AccountsBuilder> Get(Guid clientId, Guid periodId, CancellationToken cancellationToken);
        Task<List<AccountsBuilderModels.AccountsBuilder>> GetAll(Guid clientId, CancellationToken cancellationToken);
        Task<List<AccountsBuilderModels.AccountsBuilder>> GetByTenantId(Guid tenantId, CancellationToken cancellationToken);
        Task<AccountsBuilderModels.AccountsBuilder> Get(Guid processId, CancellationToken cancellationToken);
        Task Save(AccountsBuilderModels.AccountsBuilder entity, CancellationToken cancellationToken);
    }
}
