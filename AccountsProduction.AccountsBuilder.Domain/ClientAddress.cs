﻿using Newtonsoft.Json.Linq;

namespace AccountsProduction.AccountsBuilder.Domain
{
    public class ClientAddress
    {
        public Address MainAddress { get; set; }
        public Address RegisteredAddress { get; set; }

        public void UpdateRecord(JObject payload)
        {
            if (payload.ContainsKey(ClientAddressAttributiveName.MainAddress))
            {
                if (MainAddress is null)
                {
                    MainAddress = new Address();
                }

                var addressPayload = payload.GetValue(ClientAddressAttributiveName.MainAddress)!.ToObject<JObject>()!;

                MainAddress.UpdateRecord(addressPayload);
            }

            if (payload.ContainsKey(ClientAddressAttributiveName.RegisteredAddress))
            {
                if (RegisteredAddress is null)
                {
                    RegisteredAddress = new Address();
                }

                var addressPayload = payload.GetValue(ClientAttributiveName.RegisteredAddress)!.ToObject<JObject>()!;

                RegisteredAddress.UpdateRecord(addressPayload);
            }
        }
    }

    internal static class ClientAddressAttributiveName
    {
        public static readonly string MainAddress = "mainAddress";
        public static readonly string RegisteredAddress = "registeredAddress";
    }
}
