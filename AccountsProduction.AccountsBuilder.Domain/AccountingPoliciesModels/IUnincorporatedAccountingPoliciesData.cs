﻿using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels.TangibleFixedAsset;

namespace AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels
{
    public interface IUnincorporatedAccountingPoliciesData
    {
        TangibleFixedAssets TangibleFixedAssets { get; set; }
    }
    public class UnincorporatedAccountingPoliciesData : IUnincorporatedAccountingPoliciesData
    {
        TangibleFixedAssets IUnincorporatedAccountingPoliciesData.TangibleFixedAssets { get; set; }
    }
}
