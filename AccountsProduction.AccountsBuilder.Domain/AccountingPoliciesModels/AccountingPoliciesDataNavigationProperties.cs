﻿using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels
{
    [ExcludeFromCodeCoverage(Justification = "Only navigation properties")]
    public static class AccountingPoliciesDataNavigationProperties
    {
        #region Tangible assets navigation properties
        public static AssetsAdjustment GetTangibleAssetsImprovementsToProperty(this AccountingPoliciesData accountingPoliciesData)
        {
            return accountingPoliciesData?.TangibleFixedAssets?.PlantAndMachinery?.ImprovementsToProperty;
        }

        public static AssetsAdjustment GetTangibleAssetsPlantAndMachinery(this AccountingPoliciesData accountingPoliciesData)
        {
            return accountingPoliciesData?.TangibleFixedAssets?.PlantAndMachinery?.PlantAndMachinery;
        }

        public static AssetsAdjustment GetTangibleAssetsFixturesAndFittings(this AccountingPoliciesData accountingPoliciesData)
        {
            return accountingPoliciesData?.TangibleFixedAssets?.PlantAndMachinery?.FixturesAndFittings;
        }

        public static AssetsAdjustment GetTangibleAssetsMotorVehicles(this AccountingPoliciesData accountingPoliciesData)
        {
            return accountingPoliciesData?.TangibleFixedAssets?.PlantAndMachinery?.MotorVehicles;
        }

        public static AssetsAdjustment GetTangibleAssetsComputerEquipment(this AccountingPoliciesData accountingPoliciesData)
        {
            return accountingPoliciesData?.TangibleFixedAssets?.PlantAndMachinery?.ComputerEquipment;
        }

        public static AssetsAdjustment GetTangibleAssetsLongLeaseholdProperty(this AccountingPoliciesData accountingPoliciesData)
        {
            return accountingPoliciesData?.TangibleFixedAssets?.LandAndBuildings?.LongLeaseholdProperty;
        }

        public static AssetsAdjustment GetTangibleAssetsShortLeaseholdProperty(this AccountingPoliciesData accountingPoliciesData)
        {
            return accountingPoliciesData?.TangibleFixedAssets?.LandAndBuildings?.ShortLeaseholdProperty;
        }

        public static AssetsAdjustment GetTangibleAssetsFreeholdProperty(this AccountingPoliciesData accountingPoliciesData)
        {
            return accountingPoliciesData?.TangibleFixedAssets?.LandAndBuildings?.FreeholdProperty;
        }

        public static string GetPlantAndMachineryClassNameCustomization(this AccountingPoliciesData accountingPoliciesData)
        {
            return accountingPoliciesData?.TangibleFixedAssets?.PlantAndMachinery?.ClassNameCustomization;
        }

        public static string GetLandAndBuildingsClassNameCustomization(this AccountingPoliciesData accountingPoliciesData)
        {
            return accountingPoliciesData?.TangibleFixedAssets?.LandAndBuildings?.ClassNameCustomization;
        }
        #endregion
        #region Intangible assets navigation properties
        public static AssetsAdjustment GetIntangibleAssetsComputerSoftware(this AccountingPoliciesData accountingPoliciesData)
        {
            return accountingPoliciesData?.IntangibleAssets?.ComputerSoftware;
        }

        public static AssetsAdjustment GetIntangibleAssetsDevelopmentCosts(this AccountingPoliciesData accountingPoliciesData)
        {
            return accountingPoliciesData?.IntangibleAssets?.DevelopmentCosts;
        }

        public static AssetsAdjustment GetIntangibleAssetsPatentsAndLicenses(this AccountingPoliciesData accountingPoliciesData)
        {
            return accountingPoliciesData?.IntangibleAssets?.PatentsAndLicenses;
        }

        public static AssetsAdjustment GetIntangibleAssetsGoodwill(this AccountingPoliciesData accountingPoliciesData)
        {
            return accountingPoliciesData?.IntangibleAssets?.Goodwill;
        }

        #endregion
    }
}
