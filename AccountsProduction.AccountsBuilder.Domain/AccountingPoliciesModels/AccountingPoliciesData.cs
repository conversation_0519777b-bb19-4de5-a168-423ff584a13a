﻿using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels.TangibleFixedAsset;

namespace AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels
{
    public class AccountingPoliciesData : IAccountingPoliciesData
    {
        public ExemptionsFinancialStatements ExemptionsFinancialStatements { get; set; }
        public TangibleFixedAssets TangibleFixedAssets { get; set; }
        public IntangibleAssets IntangibleAssets { get; set; }
        public string ChangesInAccountingPolicies { get; set; }
        public string FinancialInstrumentsAccountingPolicy { get; set; }
        public string GovernmentGrantsAccountingPolicy { get; set; }
        public string MembersTransactionsWithTheLlpText { get; set; }
        public bool? PresentationCurrency { get; set; }
        public bool? ResearchAndDevelopment { get; set; }
        public bool? ForeignCurrencies { get; set; }
        public bool? GoodwillMaterial { get; set; }
    }
}
