using AccountsProduction.AccountsBuilder.Domain.ProfitShareModels;
using Amazon.DynamoDBv2.DataModel;
using Iris.Elements.DynamoDb.Converters;

namespace AccountsProduction.AccountsBuilder.Domain;

public class ProfitShareData
{
    public ProfitShareData()
    {
        ProfitShares = new List<ProfitShare>();
    }
    public bool IsSuccessful { get; set; }
    public string Error { get; set; }

    [DynamoDBProperty(typeof(DateTimeConverter))]
    public DateTime EntityModificationTime { get; set; }

    public List<ProfitShare> ProfitShares { get; set; }

    public decimal UnallocatedAmount { get; set; }

    public void UpdateModificationTime()
    {
        EntityModificationTime = DateTime.UtcNow;
    }
}