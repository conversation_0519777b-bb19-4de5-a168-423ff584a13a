﻿namespace AccountsProduction.AccountsBuilder.Domain.FinancialDataModels
{
    public interface IUnincorporatedFinancial : IBaseFinancial
    {
        public FinancialDataCategory Sales { get; set; }

        public FinancialDataCategory CostOfSales { get; set; }

        public FinancialDataCategory OtherIncome { get; set; }

        public FinancialDataCategory Expenses { get; set; }

        public FinancialDataCategory FinanceCosts { get; set; }

        public FinancialDataCategory PartnerAppropriations { get; set; }

        public FinancialDataCategory Depreciation { get; set; }

        public FinancialDataCategory IntangibleAssets { get; set; }

        public FinancialDataCategory TangibleFixedAssets { get; set; }

        public FinancialDataCategory FixedAssetInvestments { get; set; }

        public FinancialDataCategory InvestmentProperty { get; set; }

        public FinancialDataCategory Stock { get; set; }

        public FinancialDataCategory CurrentAssetInvestments { get; set; }

        public FinancialDataCategory Debtors { get; set; }

        public FinancialDataCategory CashAtBankAndInHand { get; set; }

        public FinancialDataCategory CreditorsAmountsFallingDueWithinOneYear { get; set; }

        public FinancialDataCategory CreditorsAmountsFallingAfterMoreThanOneYear { get; set; }

        public FinancialDataCategory ProvisionsForLiabilities { get; set; }

        public FinancialDataCategory CapitalAccount { get; set; }

        public FinancialDataCategory PartnersCapitalAccounts { get; set; }

        public FinancialDataCategory PartnersCurrentAccounts { get; set; }

        public FinancialDataCategory OtherReserves { get; set; }

        public FinancialDataCategory HerdBasis { get; set; }
    }
}