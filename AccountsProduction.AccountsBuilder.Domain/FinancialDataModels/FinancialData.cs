﻿namespace AccountsProduction.AccountsBuilder.Domain.FinancialDataModels
{
    public class FinancialData
    {
        public FinancialData()
        {
            Financials = new List<Financial>();
        }

        public int Retry { get; set; }
        public int Status { get; set; }

        public string Error { get; set; }
        public string? ErrorCode { get; set; }
        public Status StatusCode { get; set; }
        public List<Financial> Financials { get; set; }

        public DateTime EntityModificationTime { get; set; }
        public void UpdateModificationTime()
        {
            EntityModificationTime = DateTime.UtcNow;
        }
        public void SetStatusSuccessful()
        {
            StatusCode = Domain.Status.Successful;
        }

        public void SetStatusFail()
        {
            StatusCode = Domain.Status.Fail;
        }
        public void UpdateErrorCode(string? errorCode)
        {
            ErrorCode = errorCode;
        }

        public void IncreaseRetry()
        {
            Retry++;
        }

        public void ResetRetry()
        {
            Retry = 0;
        }
    }
}
