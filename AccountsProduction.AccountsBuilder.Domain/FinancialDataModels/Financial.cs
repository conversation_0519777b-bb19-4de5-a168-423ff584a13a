﻿using AccountsProduction.AccountsBuilder.Domain.Converters;
using Amazon.DynamoDBv2.DataModel;

namespace AccountsProduction.AccountsBuilder.Domain.FinancialDataModels
{
    public class Financial : IFRS105Financial, IUnincorporatedFinancial, IFRS1021AFinancial, IFRS102Financial,IIFRSFinancial
    {
        public DateTime Period { get; set; }

        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory Turnover { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory OtherIncome { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CostOfRawMaterialsAndConsumables { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory StaffCosts { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory DepreciationAndOtherAmountsWrittenOffAssets { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory OtherCharges { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory Tax { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CrossCheck { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CalledUpShareCapitalNotPaid { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory FixedAssets { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CurrentAssets { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory PrepaymentsAndAccruedIncome { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CreditorsAmountsFallingDueWithinOneYear { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory NetCurrentAssetsOrLiabilities { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory TotalAssetsLessCurrentLiabilities { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CreditorsAmountsFallingAfterMoreThanOneYear { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory ProvisionsForLiabilities { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory AccrualsAndDeferredIncome { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory NetAssets { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CapitalAndReserves { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory Sales { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CostOfSales { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory Expenses { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory FinanceCosts { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory PartnerAppropriations { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory Depreciation { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory IntangibleAssets { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory TangibleFixedAssets { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory FixedAssetInvestments { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory InvestmentProperty { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory Stock { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CurrentAssetInvestments { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory Debtors { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CashAtBankAndInHand { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CapitalAccount { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory PartnersCapitalAccounts { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory PartnersCurrentAccounts { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory OtherReserves { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory GrossProfitLoss { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory DistributionExpenses { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory AdministrativeExpenses { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]

        public FinancialDataCategory OtherOperatingExpenses { get; set; }

        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory OtherOperatingIncome { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory GainLossG50OnRevaluation { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory OperatingProfitLoss { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory ExceptionalItems { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory IncomeFromSharesInGroupUndertakings { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory IncomeFromInterestInAssociatedUndertakings { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory IncomeFromOtherParticipatingInterests { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory IncomeFromFixedAssetInvestments { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory InterestReceivableAndSimilarIncome { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory OtherFinanceIncome { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory AmountsWrittenOffInvestments { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory GainLossG282OnRevaluation { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory InterestPayableAndSimilarExpenses { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory OtherFinanceCosts { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory ProfitLossOnOrdinaryActivitiesBeforeTaxation { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory Taxation { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory ProfitLossForTheFinancialYear { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory PensionSchemeAssetsLiabilities { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory HealthcareObligations { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CalledUpShareCapital { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory SharePremiumReserve { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory RevaluationReserve { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CapitalRedemptionReserve { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory OtherReserves1 { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory OtherReserves2 { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory FairValueReserve { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory ProfitLossReserve { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory Goodwill { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory ProfitLossAvailableForDiscretionaryDivision { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory MembersRemunerationAsExpense { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory NonControllingInterestsPL { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory NonControllingInterestsBS { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory TotalMembersInterests { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory MembersOtherInterests { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory MembersCapital { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory OtherDebtsDueToMembers { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory LoansAndOtherDebtsDueToMembers { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory HerdBasis { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory WagesAndSalaries { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory SocialSecurityCosts { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory OtherPensionCosts { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory DirectorsRemuneration { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory DirectorsContributionsToDbSchemes { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory DirectorsContributionsToDcSchemes { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory DirectorsPensionsPaid { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory DirectorsCompensationForLossOfOffice { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory ShareCapitalMovements { get; set; }
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory Revenue { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory DistributionCosts { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory GainLossOnRevaluationOfAssets { get; set; } = null!;

        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory GrossProfit { get; set; } 
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory OperatingProfitBeforeExceptionalItems { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory OperatingProfit { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory FinanceIncome { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory ProfitBeforeIncomeTax { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory IncomeTax { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory ProfitForTheFinancialYear { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory IntangibleAssetsOwned { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory PropertyPlantAndEquipmentOwned { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory InvestmentPropertyOwned { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory IntangibleAssetsROU { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory PropertyPlantAndEquipmentROU { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory InvestmentPropertyROU { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory InvestmentInAssociates { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory NCAInvestments { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory LoansAndOtherFinancialAssets { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory PensionAsset { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory RetirementHealthcareBenefitsAsset { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory NCATradeAndOtherReceivables { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory NCAContractAssets { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory NCATaxReceivable { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory NCADeferredTax { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory Inventories { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CATradeAndOtherReceivables { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CAContractAssets { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CATaxReceivable { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CAInvestments { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CashAndCashEquivalents { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory Prepayments { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CLTradeAndOtherPayables { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CLContractLiabilities { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CLFinancialLiabilitiesBorrowings { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CLTaxPayable { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory CLProvisions { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory NCLTradeAndOtherPayables { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory NCLContractLiabilities { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory NCLFinancialLiabilitiesBorrowings { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory NCLTaxPayable { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory NCLDeferredTax { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory PensionLiabilities { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory RetirementHealthcareBenefitsLiability { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory SharePremium { get; set; } = null!;
        [DynamoDBProperty(typeof(FinancialDataCategoryConverter))]
        public FinancialDataCategory RetainedEarnings { get; set; } = null!;

    }
}
