﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AccountsProduction.AccountsBuilder.Domain.FinancialDataModels
{
    public interface IBaseIFRSFinancial
    {
        public DateTime Period { get; set; }

        public FinancialDataCategory Revenue { get; set; }
        public FinancialDataCategory CostOfSales { get; set; }
        public FinancialDataCategory GrossProfit { get; set; }
        public FinancialDataCategory OtherOperatingIncome { get; set; }
        public FinancialDataCategory GainLossOnRevaluationOfAssets { get; set; }
        public FinancialDataCategory DistributionCosts { get; set; } 
        public FinancialDataCategory AdministrativeExpenses { get; set; }
        public FinancialDataCategory OtherOperatingExpenses { get; set; }
        public FinancialDataCategory OperatingProfitBeforeExceptionalItems { get; set; }
        public FinancialDataCategory ExceptionalItems { get; set; }
        public FinancialDataCategory OperatingProfit { get; set; }
        public FinancialDataCategory FinanceCosts { get; set; }
        public FinancialDataCategory FinanceIncome { get; set; }
        public FinancialDataCategory ProfitBeforeIncomeTax { get; set; }
        public FinancialDataCategory IncomeTax { get; set; }
        public FinancialDataCategory ProfitForTheFinancialYear { get; set; }
        public FinancialDataCategory Goodwill { get; set; }
        public FinancialDataCategory IntangibleAssetsOwned { get; set; }
        public FinancialDataCategory PropertyPlantAndEquipmentOwned { get; set; }
        public FinancialDataCategory InvestmentPropertyOwned { get; set; }
        public FinancialDataCategory IntangibleAssetsROU { get; set; }
        public FinancialDataCategory PropertyPlantAndEquipmentROU{get;set;}
        public FinancialDataCategory InvestmentPropertyROU { get; set; }
        public FinancialDataCategory InvestmentInAssociates { get; set; }
        public FinancialDataCategory NCAInvestments { get; set; }
        public FinancialDataCategory LoansAndOtherFinancialAssets { get; set; }
        public FinancialDataCategory PensionAsset { get; set; }
        public FinancialDataCategory RetirementHealthcareBenefitsAsset { get; set; }
        public FinancialDataCategory NCATradeAndOtherReceivables { get; set; }
        public FinancialDataCategory NCAContractAssets { get; set; }
        public FinancialDataCategory NCATaxReceivable { get; set; }
        public FinancialDataCategory NCADeferredTax { get; set; }
        public FinancialDataCategory Inventories { get; set; }
        public FinancialDataCategory CATradeAndOtherReceivables { get; set; }
        public FinancialDataCategory CAContractAssets { get; set; }

        public FinancialDataCategory CATaxReceivable { get; set; }
        public FinancialDataCategory CAInvestments { get; set; }

        public FinancialDataCategory CashAndCashEquivalents { get; set; }
        public FinancialDataCategory Prepayments { get; set; }
        public FinancialDataCategory CLTradeAndOtherPayables { get; set; }
        public FinancialDataCategory CLContractLiabilities { get; set; }
        public FinancialDataCategory CLFinancialLiabilitiesBorrowings { get; set; }

        public FinancialDataCategory CLTaxPayable { get; set; }
        public FinancialDataCategory CLProvisions { get; set; }
        public FinancialDataCategory NCLTradeAndOtherPayables { get; set; }

        public FinancialDataCategory NCLContractLiabilities { get; set; }
        public FinancialDataCategory NCLFinancialLiabilitiesBorrowings { get; set; }

        public FinancialDataCategory NCLTaxPayable { get; set; }

        public FinancialDataCategory NCLDeferredTax { get; set; }
        public FinancialDataCategory PensionLiabilities { get; set; }
        public FinancialDataCategory RetirementHealthcareBenefitsLiability { get; set; }

        public FinancialDataCategory CalledUpShareCapital { get; set; }
        public FinancialDataCategory SharePremium { get; set; }

        public FinancialDataCategory RevaluationReserve { get; set; }
        public FinancialDataCategory CapitalRedemptionReserve { get; set; }
        public FinancialDataCategory OtherReserves1 { get; set; }

        public FinancialDataCategory OtherReserves2 { get; set; }
        public FinancialDataCategory FairValueReserve { get; set; }
        public FinancialDataCategory RetainedEarnings { get; set; }
        public FinancialDataCategory WagesAndSalaries { get; set; }
        public FinancialDataCategory SocialSecurityCosts { get; set; }
        public FinancialDataCategory OtherPensionCosts { get; set; }
        public FinancialDataCategory DirectorsRemuneration { get; set; }
        public FinancialDataCategory DirectorsContributionsToDbSchemes { get; set; }
        public FinancialDataCategory DirectorsContributionsToDcSchemes { get; set; }
        public FinancialDataCategory DirectorsPensionsPaid { get; set; }
        public FinancialDataCategory DirectorsCompensationForLossOfOffice { get; set; }
        public FinancialDataCategory ShareCapitalMovements { get; set; }

    }
}
