﻿namespace AccountsProduction.AccountsBuilder.Domain.FinancialDataModels
{
    public interface IBaseFRS102Financial
    {
        public DateTime Period { get; set; }
        public FinancialDataCategory CostOfSales { get; set; }
        public FinancialDataCategory GrossProfitLoss { get; set; }
        public FinancialDataCategory DistributionExpenses { get; set; }
        public FinancialDataCategory AdministrativeExpenses { get; set; }
        public FinancialDataCategory OtherOperatingIncome { get; set; }
        public FinancialDataCategory GainLossG50OnRevaluation { get; set; }
        public FinancialDataCategory OperatingProfitLoss { get; set; }
        public FinancialDataCategory ExceptionalItems { get; set; }
        public FinancialDataCategory IncomeFromSharesInGroupUndertakings { get; set; }
        public FinancialDataCategory IncomeFromInterestInAssociatedUndertakings { get; set; }
        public FinancialDataCategory IncomeFromOtherParticipatingInterests { get; set; }
        public FinancialDataCategory IncomeFromFixedAssetInvestments { get; set; }
        public FinancialDataCategory InterestReceivableAndSimilarIncome { get; set; }
        public FinancialDataCategory OtherFinanceIncome { get; set; }
        public FinancialDataCategory AmountsWrittenOffInvestments { get; set; }
        public FinancialDataCategory GainLossG282OnRevaluation { get; set; }
        public FinancialDataCategory InterestPayableAndSimilarExpenses { get; set; }
        public FinancialDataCategory OtherFinanceCosts { get; set; }
        public FinancialDataCategory ProfitLossOnOrdinaryActivitiesBeforeTaxation { get; set; }
        public FinancialDataCategory Taxation { get; set; }
        public FinancialDataCategory ProfitLossForTheFinancialYear { get; set; }
        public FinancialDataCategory CalledUpShareCapitalNotPaid { get; set; }
        public FinancialDataCategory IntangibleAssets { get; set; }
        public FinancialDataCategory TangibleFixedAssets { get; set; }
        public FinancialDataCategory FixedAssetInvestments { get; set; }
        public FinancialDataCategory InvestmentProperty { get; set; }
        public FinancialDataCategory Stock { get; set; }
        public FinancialDataCategory Debtors { get; set; }
        public FinancialDataCategory CashAtBankAndInHand { get; set; }
        public FinancialDataCategory PrepaymentsAndAccruedIncome { get; set; }
        public FinancialDataCategory CreditorsAmountsFallingDueWithinOneYear { get; set; }
        public FinancialDataCategory CreditorsAmountsFallingAfterMoreThanOneYear { get; set; }
        public FinancialDataCategory ProvisionsForLiabilities { get; set; }
        public FinancialDataCategory PensionSchemeAssetsLiabilities { get; set; }
        public FinancialDataCategory HealthcareObligations { get; set; }
        public FinancialDataCategory AccrualsAndDeferredIncome { get; set; }
        public FinancialDataCategory CalledUpShareCapital { get; set; }
        public FinancialDataCategory SharePremiumReserve { get; set; }
        public FinancialDataCategory RevaluationReserve { get; set; }
        public FinancialDataCategory CapitalRedemptionReserve { get; set; }
        public FinancialDataCategory OtherReserves1 { get; set; }
        public FinancialDataCategory OtherReserves2 { get; set; }
        public FinancialDataCategory FairValueReserve { get; set; }
        public FinancialDataCategory ProfitLossReserve { get; set; }
        public FinancialDataCategory Goodwill { get; set; }
        public FinancialDataCategory CurrentAssetInvestments { get; set; }
        public FinancialDataCategory ProfitLossAvailableForDiscretionaryDivision { get; set; }
        public FinancialDataCategory NonControllingInterestsPL { get; set; }
        public FinancialDataCategory NonControllingInterestsBS { get; set; }
        public FinancialDataCategory MembersRemunerationAsExpense { get; set; }
        public FinancialDataCategory LoansAndOtherDebtsDueToMembers { get; set; }
        public FinancialDataCategory OtherDebtsDueToMembers { get; set; }
        public FinancialDataCategory MembersCapital { get; set; }
        public FinancialDataCategory HerdBasis { get; set; }
        public FinancialDataCategory WagesAndSalaries { get; set; }
        public FinancialDataCategory SocialSecurityCosts { get; set; }
        public FinancialDataCategory OtherPensionCosts { get; set; }
        public FinancialDataCategory DirectorsRemuneration { get; set; }
        public FinancialDataCategory DirectorsContributionsToDbSchemes { get; set; }
        public FinancialDataCategory DirectorsContributionsToDcSchemes { get; set; }
        public FinancialDataCategory DirectorsPensionsPaid { get; set; }
        public FinancialDataCategory DirectorsCompensationForLossOfOffice { get; set; }
        public FinancialDataCategory ShareCapitalMovements { get; set; }
    }
}