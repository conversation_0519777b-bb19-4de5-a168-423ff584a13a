﻿namespace AccountsProduction.AccountsBuilder.Domain
{
    public class FinancialDataDrilldown
    {
        public string Description { get; set; }
        public decimal Amount { get; set; }
        public int AccountCode { get; set; }
        public int? SubAccountCode { get; set; }
        public int? DirectorCode { get; set; }
        public Guid? SectorId { get; set; }
        public DateTime? SectorCreatedDate { get; set; }
    }
}
