﻿using Amazon.DynamoDBv2.DataModel;

namespace AccountsProduction.AccountsBuilder.Domain
{
    public class LicenseData
    {
        private const string WatermarkTextForTrialLicense = "IRIS Elements Accounts Production Trial Version";

        public LicenseData()
        {

        }

        public LicenseData(bool isTrial)
        {
            IsTrial = isTrial;
        }

        public bool IsTrial { get; set; }

        [DynamoDBIgnore]
        public string WatermarkText { get => IsTrial ? WatermarkTextForTrialLicense : null; }
    }
}
