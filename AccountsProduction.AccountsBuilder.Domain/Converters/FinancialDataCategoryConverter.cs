﻿using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;

namespace AccountsProduction.AccountsBuilder.Domain.Converters
{
    public class FinancialDataCategoryConverter : IPropertyConverter
    {
        private const string accountCodeKey = "AccountCode";
        private const string amountKey = "Amount";
        private const string subaccountCodeKey = "SubaccountCode";
        private const string descriptionKey = "Description";
        private const string directorCodeK<PERSON> = "DirectorCode";
        private const string valueKey = "Value";
        private const string drilldownKey = "DrilldownData";
        private const string sectorIdKey = "SectorId";
        private const string sectorCreatedDateKey = "SectorCreatedDate";

        public DynamoDBEntry ToEntry(object value)
        {
            FinancialDataCategory category = value as FinancialDataCategory;
            if (category == null) throw new ArgumentOutOfRangeException(nameof(value), "Property cannot be converted as it is not based upon a financialDataCategory type.");

            Document entry = new Document();
            var drilldownList = new DynamoDBList();
            entry.Add(new KeyValuePair<string, DynamoDBEntry>(valueKey, category.Value));
            foreach (var item in category.DrilldownData)
            {
                var drilldownDocument = new Document
                {
                    new KeyValuePair<string, DynamoDBEntry>(accountCodeKey, item.AccountCode),
                    new KeyValuePair<string, DynamoDBEntry>(amountKey, item.Amount),
                    new KeyValuePair<string, DynamoDBEntry>(descriptionKey, item.Description)
                };
                if (item.DirectorCode == null)
                {
                    drilldownDocument.Add(new KeyValuePair<string, DynamoDBEntry>(directorCodeKey, DynamoDBNull.Null));
                }
                else
                {
                    drilldownDocument.Add(new KeyValuePair<string, DynamoDBEntry>(directorCodeKey, item.DirectorCode));
                }

                if (item.SubAccountCode == null)
                {
                    drilldownDocument.Add(new KeyValuePair<string, DynamoDBEntry>(subaccountCodeKey, DynamoDBNull.Null));
                }
                else
                {
                    drilldownDocument.Add(new KeyValuePair<string, DynamoDBEntry>(subaccountCodeKey, item.SubAccountCode));
                }

                if (item.SectorId == null)
                {
                    drilldownDocument.Add(new KeyValuePair<string, DynamoDBEntry>(sectorIdKey, DynamoDBNull.Null));
                }
                else
                {
                    drilldownDocument.Add(new KeyValuePair<string, DynamoDBEntry>(sectorIdKey, item.SectorId));
                }

                if (item.SectorCreatedDate == null)
                {
                    drilldownDocument.Add(new KeyValuePair<string, DynamoDBEntry>(sectorCreatedDateKey, DynamoDBNull.Null));
                }
                else
                {
                    drilldownDocument.Add(new KeyValuePair<string, DynamoDBEntry>(sectorCreatedDateKey, item.SectorCreatedDate));
                }

                drilldownList.Add(drilldownDocument);
            }
            entry.Add(new KeyValuePair<string, DynamoDBEntry>(drilldownKey, drilldownList));
            return entry;
        }

        public object FromEntry(DynamoDBEntry entry)
        {
            if (entry is Primitive primitive && primitive.Type == DynamoDBEntryType.String &&
               primitive.Value is string)
            {
                return new FinancialDataCategory
                {
                    Value = (string)primitive.Value
                };
            }

            if (!(entry is Document document))
            {
                return null;
            }

            var category = new FinancialDataCategory
            {
                Value = document.FirstOrDefault(v => v.Key == valueKey).Value
            };
            var drillDownDbEntry = document.FirstOrDefault(v => v.Key == drilldownKey).Value;
            if (!(drillDownDbEntry is DynamoDBList dbList))
            {
                return category;
            }

            category.DrilldownData = new List<FinancialDataDrilldown>();
            foreach (var item in dbList.Entries)
            {
                if (!(item is Document drillDownDocument))
                {
                    continue;
                }

                var dataDrilldown = new FinancialDataDrilldown
                {
                    AccountCode = (int)drillDownDocument[accountCodeKey],
                    Amount = (decimal)drillDownDocument[amountKey],
                    Description = drillDownDocument[descriptionKey]
                };

                PopulateDirectorCodeFor(drillDownDocument, dataDrilldown);

                PopulateSubAccountCodeFor(drillDownDocument, dataDrilldown);

                PopulateSectorIdFor(drillDownDocument, dataDrilldown);

                PopulateSectorCreatedDateFor(drillDownDocument, dataDrilldown);

                category.DrilldownData.Add(dataDrilldown);
            }

            return category;
        }

        private static void PopulateSubAccountCodeFor(Document drillDownDocument, FinancialDataDrilldown dataDrilldown)
        {
            if (drillDownDocument.Keys.Any(k => k.Equals(subaccountCodeKey)))
            {
                dataDrilldown.SubAccountCode = drillDownDocument[subaccountCodeKey].AsDynamoDBNull() == null ? (int?)drillDownDocument[subaccountCodeKey] : null;
            }
        }

        private static void PopulateDirectorCodeFor(Document drillDownDocument, FinancialDataDrilldown dataDrilldown)
        {
            if (drillDownDocument.Keys.Any(k => k.Equals(directorCodeKey)))
            {
                dataDrilldown.DirectorCode = drillDownDocument[directorCodeKey].AsDynamoDBNull() == null ? (int?)drillDownDocument[directorCodeKey] : null;
            }
        }

        private static void PopulateSectorIdFor(Document drillDownDocument, FinancialDataDrilldown dataDrilldown)
        {
            if (drillDownDocument.Keys.Any(k => k.Equals(sectorIdKey)))
            {
                dataDrilldown.SectorId = drillDownDocument[sectorIdKey].AsDynamoDBNull() == null ? (Guid?)drillDownDocument[sectorIdKey] : null;
            }
        }

        private static void PopulateSectorCreatedDateFor(Document drillDownDocument, FinancialDataDrilldown dataDrilldown)
        {
            if (drillDownDocument.Keys.Any(k => k.Equals(sectorCreatedDateKey)))
            {
                dataDrilldown.SectorCreatedDate = drillDownDocument[sectorCreatedDateKey].AsDynamoDBNull() == null ? (DateTime?)drillDownDocument[sectorCreatedDateKey] : null;
            }
        }
    }
}