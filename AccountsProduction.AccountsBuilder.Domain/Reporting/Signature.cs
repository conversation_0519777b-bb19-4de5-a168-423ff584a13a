﻿namespace AccountsProduction.AccountsBuilder.Reporting.Domain
{
    public class Signature
    {
        public int Id { get; set; }

        public Guid ClientId { get; set; }

        public Guid AccountPeriodId { get; set; }

        public string? SignatoryTitle { get; set; }

        public string? SignatoryFirstName { get; set; }

        public string? SignatorySurname { get; set; }

        public string? SignatureType { get; set; }

        public DateTime? SignatureDate { get; set; }

        public int DisplayOrder { get; set; }

        public virtual ReportingPeriod? ReportingPeriod { get; set; }
        public Guid InvolvementUUID { get; set; }

        public string InvolvementType { get; set; } = null!;
    }
}
