﻿namespace AccountsProduction.AccountsBuilder.Reporting.Domain
{
    public class Client
    {
        public Client()
        {
            ReportingPeriod = new HashSet<ReportingPeriod>();
            Member = new HashSet<Member>();
        }

        public Guid Id { get; set; }

        public Guid TenantId { get; set; }

        public virtual Tenant? Tenant { get; set; }

        public virtual ICollection<Member> Member { get; set; }

        public virtual ICollection<ReportingPeriod> ReportingPeriod { get; set; }

        public string? CompanyName { get; set; }

        public string? CompanyRegistrationNumber { get; set; }

        public string? CompanyType { get; set; }

        public string? CompanySubtype { get; set; }

        public string? CompanyCategory { get; set; }

        public string? RegisteredOfficeAddressLine1 { get; set; }

        public string? RegisteredOfficeAddressLine2 { get; set; }

        public string? RegisteredOfficeAddressTown { get; set; }

        public string? RegisteredOfficeAddressCounty { get; set; }

        public string? RegisteredOfficeAddressPostcode { get; set; }

        public string? MainAddressLine1 { get; set; }

        public string? MainAddressLine2 { get; set; }

        public string? MainAddressTown { get; set; }

        public string? MainAddressCounty { get; set; }

        public string? MainAddressPostcode { get; set; }

        public string? TradingName { get; set; }
    }
}
