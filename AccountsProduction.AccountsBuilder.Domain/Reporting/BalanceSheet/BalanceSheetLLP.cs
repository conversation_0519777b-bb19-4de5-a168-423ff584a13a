﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet.Contract;

namespace AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet
{
    public class BalanceSheetLLP : IBalanceSheetBase
    {
        public decimal TotalMembersInterests { get; set; }
        public decimal MembersOtherInterests { get; set; }
        public decimal MembersCapital { get; set; }
        public decimal OtherDebtsDueToMembers { get; set; }
        public decimal LoansAndOtherDebtsDueToMembers { get; set; }
        public virtual ReportingPeriod? ReportingPeriod { get; set; }
        public Guid ClientId { get; set; }
        public Guid AccountPeriodId { get; set; }
    }
}
