﻿namespace AccountsProduction.AccountsBuilder.Domain.Reporting.Other
{
    public class DplSummaryCalcs
    {
        public int Id { get; set; }
        public Guid ClientId { get; set; }
        public Guid AccountPeriodId { get; set; }
        public string DPLCalcType { get; set; }
        public decimal DPLValue { get; set; }
        public virtual AccountsBuilder.Reporting.Domain.ReportingPeriod? ReportingPeriod { get; set; }
    }
}
