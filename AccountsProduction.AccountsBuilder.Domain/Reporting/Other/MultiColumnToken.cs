﻿namespace AccountsProduction.AccountsBuilder.Domain.Reporting.Other
{
    public class MultiColumnToken
    {
        public int Id { get; set; }
        public Guid ClientId { get; set; }
        public Guid AccountPeriodId { get; set; }
        public string? AssignedToken { get; set; }
        public decimal? TokenCount { get; set; }
        public string? Token1 { get; set; }
        public string? Token2 { get; set; }
        public string? Token3 { get; set; }
        public string? Token4 { get; set; }
        public string? Token5 { get; set; }
        public string? Token6 { get; set; }
        public string? Token7 { get; set; }
        public string? Token8 { get; set; }
        public string? Token9 { get; set; }
        public virtual AccountsBuilder.Reporting.Domain.ReportingPeriod? ReportingPeriod { get; set; }
    }
}
