﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.ProfitAndLoss.Contract;

namespace AccountsProduction.AccountsBuilder.Reporting.Domain.ProfitAndLoss
{
    public class ProfitAndLossCompaniesAct : ProfitAndLossBase
    {
        public decimal Turnover { get; set; }

        public decimal CostOfSales { get; set; }

        public decimal GrossProfitLoss { get; set; }

        public decimal DistributionExpenses { get; set; }

        public decimal AdministrativeExpenses { get; set; }

        public decimal OtherOperatingIncome { get; set; }

        public decimal GainLossOnRevaluation1 { get; set; }

        public decimal OperatingProfitLoss { get; set; }

        public decimal ExceptionalItems { get; set; }

        public decimal IncomeFromSharesInGroupUndertakings { get; set; }

        public decimal IncomeFromInterestInAssociatedUndertakings { get; set; }

        public decimal IncomeFromOtherParticipatingInterests { get; set; }

        public decimal IncomeFromFixedAssetInvestments { get; set; }

        public decimal InterestReceivableAndSimilarIncome { get; set; }

        public decimal OtherFinanceIncome { get; set; }

        public decimal AmountsWrittenOffInvestments { get; set; }

        public decimal GainLossOnRevaluation2 { get; set; }

        public decimal InterestPayableAndSimilarExpenses { get; set; }

        public decimal OtherFinanceCosts { get; set; }

        public decimal ProfitLossOnOrdinaryActivitiesBeforeTaxation { get; set; }

        public decimal Taxation { get; set; }

        public decimal ProfitLossForTheFinancialYear { get; set; }

        public decimal ProfitLossAvailableForDiscretionaryDivision { get; set; }

        public decimal NonControllingInterests { get; set; }

        public decimal MembersRemunerationAsExpense { get; set; }
    }
}
