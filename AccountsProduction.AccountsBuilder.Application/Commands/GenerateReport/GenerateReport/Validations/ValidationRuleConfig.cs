﻿using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels;
using AccountsProduction.AccountsBuilder.Domain.NoteModels;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations
{
    public class ValidationRuleConfig
    {
        public string ErrorCode { get; set; }
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public string Breadcrumb { get; set; }
        public string Description { get; set; }
        public string ErrorCategory { get; set; }
        public string Type { get; set; }
        public string Target { get; set; }
    }

    public static class Validator
    {
        public static ValidationIssue ValidateForNull(this string fieldValue, ValidationRuleConfig ruleConfig)
        {
            return !string.IsNullOrEmpty(fieldValue)
                ? null
                : MapToValidationIssue(ruleConfig);
        }

        public static ValidationIssue ValidateForNull(this AssetsAdjustment objectValue, ValidationRuleConfig ruleConfig)
        {
            if (objectValue != null && (objectValue.StraightLineBasis != null || objectValue.ReducingBalanceBasis != null || !string.IsNullOrEmpty(objectValue.AlternativeBasis)))
            {
                return null;
            }

            return MapToValidationIssue(ruleConfig);
        }

        public static ValidationIssue ValidateForNull(this AdvancesCreditAndGuaranteesGrantedToDirectors objectValue, ValidationRuleConfig ruleConfig)
        {
            if (objectValue?.Items?.Any(x => x.BalanceOutstandingAtStartOfYear.HasValue || x.AmountsAdvanced.HasValue || x.AmountsRepaid.HasValue || x.AmountsWrittenOff.HasValue || x.AmountsWaived.HasValue) ?? false)
            {
                return null;
            }

            return MapToValidationIssue(ruleConfig);
        }

        public static ValidationIssue MapToValidationIssue(this ValidationRuleConfig ruleConfig)
        {
            return ValidationIssue.BuildValidationIssue(ruleConfig.ErrorCode, ruleConfig.Name, ruleConfig.DisplayName, ruleConfig.Breadcrumb,
                ruleConfig.Description, ruleConfig.Type, ruleConfig.Target, ruleConfig.ErrorCategory);
        }

        public static bool IsInvolvementActive(Involvement rel, string involvementType, DateTime comparativeDate)
        {
            return rel.InvolvementType == involvementType
                   && rel.StartDate?.Date < comparativeDate
                   && !rel.EndDate.HasValue;
        }
    }
}