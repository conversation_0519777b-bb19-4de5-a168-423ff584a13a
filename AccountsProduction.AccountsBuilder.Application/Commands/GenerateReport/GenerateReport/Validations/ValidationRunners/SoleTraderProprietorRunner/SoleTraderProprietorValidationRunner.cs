﻿using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.SoleTraderProprietorRunner
{
    public class SoleTraderProprietorValidationRunner : ValidationRunner
    {
        protected override Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>> ValidationRules =>
            new Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>>
            {
                {
                    SoleTraderProprietorValidations.Proprietor, accountsBuilder =>
                    {
                        var proprietors = accountsBuilder.InvolvementsData.Involvements.Where(involvement => involvement.InvolvementType == ProprietorInvolvementType);
                        if (!proprietors.Any())
                        {
                            return SoleTraderProprietorValidations.ProprietorRuleConfig.MapToValidationIssue();
                        }

                        return null;
                    }
                }
            };

        private const string ProprietorInvolvementType = "Proprietor/Partner";
    }
}