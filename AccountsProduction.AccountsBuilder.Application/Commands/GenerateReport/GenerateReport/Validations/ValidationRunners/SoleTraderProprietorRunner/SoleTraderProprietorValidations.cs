﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.SoleTraderProprietorRunner;

public static class SoleTraderProprietorValidations
{
    public const string Proprietor = "Proprietor";

    public static readonly ValidationRuleConfig ProprietorRuleConfig = new ValidationRuleConfig
    {
        Description = "A sole trader must have a proprietor to approve accounts.",
        Name = Proprietor,
        Breadcrumb = ClientManagementBreadcrumbs.RelationShipTab.ToString(),
        DisplayName = "Proprietor",
        ErrorCategory = ErrorCategoryType.Mandatory,
        Type = ValidationRuleType.Missing,
        Target = Target.IssueLog,
        ErrorCode = ValidationCodes.Proprietor
    };
}