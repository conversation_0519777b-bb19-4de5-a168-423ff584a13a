﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.PartnershipPartnersRunner;

public static class PartnershipPartnerValidations
{
    public const string Partner = "Partner";
    public const string ActivePartner = "ActivePartner";
    public const string UnallocatedProfit = "UnallocatedProfit";

    public const string UnallocatedProfitDescription = "All profit should be allocated to individual partners prior to approval of the accounts.";
    public const string UnallocatedProfitDisplayName = "Unallocated profit";

    public static readonly ValidationRuleConfig PartnersRuleConfig = new ValidationRuleConfig
    {
        Description = "A Partnership must have at least two partners to be able to approve accounts.",
        Name = Partner,
        Breadcrumb = ClientManagementBreadcrumbs.RelationShipTab.ToString(),
        DisplayName = "Partners",
        ErrorCategory = ErrorCategoryType.Mandatory,
        Type = ValidationRuleType.Missing,
        Target = Target.IssueLog,
        ErrorCode = ValidationCodes.Partner
    };

    public static readonly ValidationRuleConfig ActivePartnersRuleConfig = new ValidationRuleConfig
    {
        Description = "The partners must be active at the time of the approval of the accounts.",
        Name = ActivePartner,
        Breadcrumb = ClientManagementBreadcrumbs.RelationShipTab.ToString(),
        DisplayName = "Partners",
        ErrorCategory = ErrorCategoryType.Mandatory,
        Type = ValidationRuleType.Invalid,
        Target = Target.IssueLog,
        ErrorCode = ValidationCodes.PartnerActive
    };

    public static readonly ValidationRuleConfig UnallocatedProfitLossRuleConfig = new ValidationRuleConfig
    {
        Description = UnallocatedProfitDescription,
        Name = UnallocatedProfit,
        Breadcrumb = ProfitSharesBreadcrumbs.ProfitSharesRule.ToString(),
        DisplayName = UnallocatedProfitDisplayName,
        ErrorCategory = ErrorCategoryType.Advisory,
        Target = Target.IssueLog,
        ErrorCode = ValidationCodes.UnallocatedProfit
    };

    public static readonly ValidationRuleConfig UnallocatedProfitLossSevereRuleConfig = new ValidationRuleConfig
    {
        Description = UnallocatedProfitDescription,
        Name = UnallocatedProfit,
        Breadcrumb = ProfitSharesBreadcrumbs.ProfitSharesRule.ToString(),
        DisplayName = UnallocatedProfitDisplayName,
        ErrorCategory = ErrorCategoryType.Mandatory,
        Target = Target.IssueLog,
        ErrorCode = ValidationCodes.UnallocatedProfit
    };
}