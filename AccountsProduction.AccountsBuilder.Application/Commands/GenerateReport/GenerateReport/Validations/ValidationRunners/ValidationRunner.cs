﻿using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners
{
    public abstract class ValidationRunner
    {
        protected abstract Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>> ValidationRules { get; }

        public IList<ValidationIssue> Validate(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            return ValidationRules.Select(keyValuePair => keyValuePair.Value.Invoke(accountsBuilder)).Where(result => result != null).ToList();
        }

        public static bool HasAccountsInSearchedGroups(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder, IList<GroupAccountSubAccountInterval> accountRanges)
        {
            return accountsBuilder.TrialBalance.TrialBalances
                .Any(trialBalance => accountRanges.Any(ar =>
                    ar.IsAccountCodeSubAccountCodeInRange(trialBalance.AccountCode, trialBalance.SubAccountCode) &&
                    trialBalance.Amount != 0));
        }
    }
}