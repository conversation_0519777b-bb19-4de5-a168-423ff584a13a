﻿using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.IntangibleAssetsRunner
{
    public class IntangibleAssetsValidationRunner : ValidationRunner
    {
        private const int Group421 = 421;
        private const int Account502 = 502;
        private const int Account503 = 503;
        private const int Account504 = 504;
        private const int Account505 = 505;
        private readonly IGroupAccountSubAccountIntervalRepository _repository;

        public IntangibleAssetsValidationRunner(IGroupAccountSubAccountIntervalRepository repository)
        {
            _repository = repository;
        }

        protected override Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>> ValidationRules =>
            new Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>>
            {
                {
                    IntangibleAssetsValidations.IntangibleAssets,
                    ValidateIntangibleAssets
                },
                {
                    IntangibleAssetsValidations.IntangibleAssetsGoodwill,
                    ValidateValidateIntangibleAssetsGoodwill
                },
                {
                    IntangibleAssetsValidations.IntangibleAssetsPatentsAndLicenses,
                    ValidateValidateIntangibleAssetsPatentsAndLicenses
                },
                {
                    IntangibleAssetsValidations.IntangibleAssetsDevelopmentCosts,
                    ValidateValidateIntangibleAssetsDevelopmentCosts
                },
                {
                    IntangibleAssetsValidations.IntangibleAssetsComputerSoftware,
                    ValidateValidateIntangibleAssetsComputerSoftware
                }
            };

        private ValidationIssue ValidateIntangibleAssets(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var accountRanges = _repository.GetCachedGroupAccountSubAccountIntervalList().Where(g => g.GroupNo.In(Group421));

            var hasAccounts = HasAccountsInSearchedGroups(accountsBuilder, accountRanges.ToList());

            if (hasAccounts)
            {
                return null;
            }

            var ruleConfig = IntangibleAssetsValidations.IntangibleAssetsConfig;
            return ValidationIssue.BuildValidationIssue(ruleConfig.ErrorCode, ruleConfig.Name, ruleConfig.DisplayName, ruleConfig.Breadcrumb,
                ruleConfig.Description, ruleConfig.Type, ruleConfig.Target, ruleConfig.ErrorCategory);
        }

        private ValidationIssue ValidateValidateIntangibleAssetsGoodwill(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            if (accountsBuilder.TrialBalance.TrialBalances.Any(x => x.AccountCode == Account502))
            {
                return Validator.ValidateForNull(accountsBuilder.AccountingPolicies?.IntangibleAssetsGoodwill, IntangibleAssetsValidations.GoodwillConfig);
            }

            return null;
        }

        private ValidationIssue ValidateValidateIntangibleAssetsPatentsAndLicenses(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            if (accountsBuilder.TrialBalance.TrialBalances.Any(x => x.AccountCode == Account503))
            {
                return Validator.ValidateForNull(accountsBuilder.AccountingPolicies?.IntangibleAssetsPatentsAndLicenses, IntangibleAssetsValidations.PatentsAndLicensesConfig);
            }

            return null;
        }

        private ValidationIssue ValidateValidateIntangibleAssetsDevelopmentCosts(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            if (accountsBuilder.TrialBalance.TrialBalances.Any(x => x.AccountCode == Account504))
            {
                return Validator.ValidateForNull(accountsBuilder.AccountingPolicies?.IntangibleAssetsDevelopmentCosts, IntangibleAssetsValidations.DevelopmentCostsConfig);
            }

            return null;
        }

        private ValidationIssue ValidateValidateIntangibleAssetsComputerSoftware(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            if (accountsBuilder.TrialBalance.TrialBalances.Any(x => x.AccountCode == Account505))
            {
                return Validator.ValidateForNull(accountsBuilder.AccountingPolicies?.IntangibleAssetsComputerSoftware, IntangibleAssetsValidations.ComputerSoftwareConfig);
            }

            return null;
        }
    }
}
