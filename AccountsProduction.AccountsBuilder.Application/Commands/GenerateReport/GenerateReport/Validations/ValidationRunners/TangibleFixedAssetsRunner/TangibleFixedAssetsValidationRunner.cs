using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.TangibleFixedAssetsRunner
{
    public class TangibleFixedAssetsValidationRunner : ValidationRunner
    {
        private const int Group430 = 430;
        private const int Group441 = 441;
        private const int Group451 = 451;
        private const int Account512 = 512;
        private const int Account513 = 513;
        private const int Account514 = 514;
        private const int Account522 = 522;
        private const int Account523 = 523;
        private const int Account524 = 524;
        private const int Account525 = 525;
        private const int Account526 = 526;

        private readonly IGroupAccountSubAccountIntervalRepository _repository;

        public TangibleFixedAssetsValidationRunner(IGroupAccountSubAccountIntervalRepository repository)
        {
            _repository = repository;
        }

        protected override Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>>
            ValidationRules =>
            new Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>>
            {
                {
                    TangibleFixedAssetsValidations.TangibleFixedAssets,
                    ValidateTangibleFixedAssets
                },
                {
                    TangibleFixedAssetsValidations.TangibleFixedAssetsLandAndBuildings,
                    ValidateLandAndBuildings
                },
                {
                    TangibleFixedAssetsValidations.TangibleFixedAssetsPlantAndMachineries,
                    ValidatePlantAndMachineries
                },
                {
                    TangibleFixedAssetsValidations.TangibleFixedAssetsFreeholdProperty,
                    ValidateFreeholdProperty
                },
                {
                    TangibleFixedAssetsValidations.TangibleFixedAssetsShortLeaseholdProperty,
                    ValidateShortLeaseholdProperty
                },
                {
                    TangibleFixedAssetsValidations.TangibleFixedAssetsLongLeaseholdProperty,
                    ValidateLongLeaseholdProperty
                },
                {
                    TangibleFixedAssetsValidations.TangibleFixedAssetsImprovementsToProperty,
                    ValidateImprovementsToProperty
                },
                {
                    TangibleFixedAssetsValidations.TangibleFixedAssetsPlantAndMachinery,
                    ValidatePlantAndMachinery
                },
                {
                    TangibleFixedAssetsValidations.TangibleFixedAssetsFixturesAndFittings,
                    ValidateFixturesAndFittings
                },
                {
                    TangibleFixedAssetsValidations.TangibleFixedAssetsMotorVehicles,
                    ValidateMotorVehicles
                },
                {
                    TangibleFixedAssetsValidations.TangibleFixedAssetsComputerEquipment,
                    ValidateComputerEquipment
                },
            };

        private ValidationIssue ValidateImprovementsToProperty(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            if (accountsBuilder.TrialBalance.TrialBalances.Any(x => x.AccountCode == Account522))
            {
                return Validator.ValidateForNull(accountsBuilder.AccountingPolicies?.TangibleAssetsImprovementsToProperty, TangibleFixedAssetsValidations.TangibleFixedAssetsImprovementsToPropertyConfig);
            }

            return null;
        }

        private ValidationIssue ValidatePlantAndMachinery(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            if (accountsBuilder.TrialBalance.TrialBalances.Any(x => x.AccountCode == Account523))
            {
                return Validator.ValidateForNull(accountsBuilder.AccountingPolicies?.TangibleAssetsPlantAndMachinery, TangibleFixedAssetsValidations.TangibleFixedAssetsPlantAndMachineryConfig);
            }

            return null;
        }

        private ValidationIssue ValidateFixturesAndFittings(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            if (accountsBuilder.TrialBalance.TrialBalances.Any(x => x.AccountCode == Account524))
            {
                return Validator.ValidateForNull(accountsBuilder.AccountingPolicies?.TangibleAssetsFixturesAndFittings, TangibleFixedAssetsValidations.TangibleFixedAssetsFixturesAndFittingsConfig);
            }

            return null;
        }

        private ValidationIssue ValidateMotorVehicles(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            if (accountsBuilder.TrialBalance.TrialBalances.Any(x => x.AccountCode == Account525))
            {
                return Validator.ValidateForNull(accountsBuilder.AccountingPolicies?.TangibleAssetsMotorVehicles, TangibleFixedAssetsValidations.TangibleFixedAssetsMotorVehiclesConfig);
            }

            return null;
        }

        private ValidationIssue ValidateComputerEquipment(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            if (accountsBuilder.TrialBalance.TrialBalances.Any(x => x.AccountCode == Account526))
            {
                return Validator.ValidateForNull(accountsBuilder.AccountingPolicies?.TangibleAssetsComputerEquipment, TangibleFixedAssetsValidations.TangibleFixedAssetsComputerEquipmentConfig);
            }

            return null;
        }

        private ValidationIssue ValidateLongLeaseholdProperty(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            if (accountsBuilder.TrialBalance.TrialBalances.Any(x => x.AccountCode == Account514))
            {
                return Validator.ValidateForNull(accountsBuilder.AccountingPolicies?.TangibleAssetsLongLeaseholdProperty, TangibleFixedAssetsValidations.LongLeaseholdPropertyConfig);
            }

            return null;
        }

        private ValidationIssue ValidateShortLeaseholdProperty(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            if (accountsBuilder.TrialBalance.TrialBalances.Any(x => x.AccountCode == Account513))
            {
                return Validator.ValidateForNull(accountsBuilder.AccountingPolicies?.TangibleAssetsShortLeaseholdProperty, TangibleFixedAssetsValidations.ShortLeaseholdPropertyConfig);
            }

            return null;
        }

        private ValidationIssue ValidateFreeholdProperty(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            if (accountsBuilder.TrialBalance.TrialBalances.Any(x => x.AccountCode == Account512))
            {
                return Validator.ValidateForNull(accountsBuilder.AccountingPolicies?.TangibleAssetsFreeholdProperty, TangibleFixedAssetsValidations.FreeholdPropertyConfig);
            }

            return null;
        }

        private ValidationIssue ValidatePlantAndMachineries(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var accountRanges =
                _repository.GetCachedGroupAccountSubAccountIntervalList().Where(g =>
                g.GroupNo.In(Group451));

            var hasAccounts = HasAccountsInSearchedGroups(accountsBuilder, accountRanges.ToList());

            if (hasAccounts) return null;

            var ruleConfig = TangibleFixedAssetsValidations.PlantAndMachineriesConfig;
            return ValidationIssue.BuildValidationIssue(ruleConfig.ErrorCode, ruleConfig.Name, ruleConfig.DisplayName, ruleConfig.Breadcrumb,
                ruleConfig.Description, ruleConfig.Type, ruleConfig.Target, ruleConfig.ErrorCategory);
        }

        private ValidationIssue ValidateLandAndBuildings(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var accountRanges =
                _repository.GetCachedGroupAccountSubAccountIntervalList().Where(g =>
                    g.GroupNo.In(Group441));

            var hasAccounts = HasAccountsInSearchedGroups(accountsBuilder, accountRanges.ToList());

            if (hasAccounts) return null;

            var ruleConfig = TangibleFixedAssetsValidations.LandAndBuildingsConfig;
            return ValidationIssue.BuildValidationIssue(ruleConfig.ErrorCode, ruleConfig.Name, ruleConfig.DisplayName, ruleConfig.Breadcrumb,
                ruleConfig.Description, ruleConfig.Type, ruleConfig.Target, ruleConfig.ErrorCategory);
        }

        private ValidationIssue ValidateTangibleFixedAssets(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var accountRanges =
                _repository.GetCachedGroupAccountSubAccountIntervalList().Where(g =>
                    g.GroupNo.In(Group430));

            var hasAccounts = HasAccountsInSearchedGroups(accountsBuilder, accountRanges.ToList());

            if (hasAccounts) return null;

            var ruleConfig = TangibleFixedAssetsValidations.TangibleFixedAssetsConfig;
            return ValidationIssue.BuildValidationIssue(ruleConfig.ErrorCode, ruleConfig.Name, ruleConfig.DisplayName, ruleConfig.Breadcrumb,
                ruleConfig.Description, ruleConfig.Type, ruleConfig.Target, ruleConfig.ErrorCategory);
        }
    }
}