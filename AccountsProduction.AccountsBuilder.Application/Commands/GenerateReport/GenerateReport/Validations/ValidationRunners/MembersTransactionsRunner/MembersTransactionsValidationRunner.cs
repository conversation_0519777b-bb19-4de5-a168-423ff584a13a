﻿using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.MembersTransactionsRunner
{
    public class MembersTransactionsValidationRunner : ValidationRunner
    {
        protected override Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>> ValidationRules
        {
            get
            {
                return new Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>>
                {
                    {
                        MembersTransactionsValidation.MembersTransactions,
                        accountsBuilder => Validator.ValidateForNull(accountsBuilder.AccountingPolicies?.MembersTransactionsWithTheLlpText, MembersTransactionsValidation.MembersTransactionsConfig)
                    }
                };
            }
        }
    }
}
