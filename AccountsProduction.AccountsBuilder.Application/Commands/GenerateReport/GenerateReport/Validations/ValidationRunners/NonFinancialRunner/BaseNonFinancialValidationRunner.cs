﻿using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NonFinancialRunner
{
    public class BaseNonFinancialValidationRunner : ValidationRunner
    {
        protected override Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>> ValidationRules
        {
            get
            {
                return new Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>>
                {
                    {
                        NonFinancialValidations.BusinessName,
                        accountsBuilder => Validator.ValidateForNull(accountsBuilder.NonFinancialData.CompanyName, NonFinancialValidations.CompanyNameRuleConfig)
                    },
                    {
                        NonFinancialValidations.BusinessType,
                        accountsBuilder => Validator.ValidateForNull(accountsBuilder.NonFinancialData.BusinessType, NonFinancialValidations.BusinessTypeRuleConfig)
                    },
                    {
                        NonFinancialValidations.AddressLineOne,
                        accountsBuilder =>
                        {
                            var registeredAddressLine = accountsBuilder.NonFinancialData.ClientAddresses?
                                                    .RegisteredAddress?.Line1;
                            var mainAddressLine = accountsBuilder.NonFinancialData.ClientAddresses?
                                                    .MainAddress?.Line1;

                            if (registeredAddressLine != null)
                            {
                                return  Validator.ValidateForNull(registeredAddressLine, NonFinancialValidations.AddressLineOneRuleConfig);
                            } else if (mainAddressLine != null)
                            {
                                return  Validator.ValidateForNull(mainAddressLine, NonFinancialValidations.AddressLineOneRuleConfig);
                            }

                            return Validator.ValidateForNull(String.Empty, NonFinancialValidations.AddressLineOneRuleConfig);
                        }
                    },
                    {
                        NonFinancialValidations.CityTown,
                        accountsBuilder =>
                        {
                            var registeredTown = accountsBuilder.NonFinancialData.ClientAddresses?.RegisteredAddress?.Town;
                            var mainTown = accountsBuilder.NonFinancialData.ClientAddresses?.MainAddress?.Town;
                            if (registeredTown != null)
                            {
                                return Validator.ValidateForNull(registeredTown, NonFinancialValidations.CityRuleConfig);
                            } else if (mainTown != null)
                            {
                                return Validator.ValidateForNull(mainTown, NonFinancialValidations.CityRuleConfig);
                            }

                            return Validator.ValidateForNull(String.Empty, NonFinancialValidations.CityRuleConfig);
                        }
                    }
                };
            }
        }
    }
}
