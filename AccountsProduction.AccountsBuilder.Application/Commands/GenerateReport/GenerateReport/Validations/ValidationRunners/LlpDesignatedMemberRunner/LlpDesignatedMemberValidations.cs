﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.PartnershipPartnersRunner;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.LlpDesignatedMemberRunner
{
    public static class LlpDesignatedMemberValidations
    {
        public const string DesignatedMember = "DesignatedMember";
        public const string ActiveDesignatedMember = "ActiveDesignatedMember";
        public const string UnallocatedProfit = "UnallocatedProfit";

        public static readonly ValidationRuleConfig DesignatedMemberRuleConfig = new ValidationRuleConfig
        {
            Description = "A company must have two designated members to be able to approve and submit financial statements.",
            Name = DesignatedMember,
            Breadcrumb = ClientManagementBreadcrumbs.RelationShipTab.ToString(),
            DisplayName = "Designated Member",
            ErrorCategory = ErrorCategoryType.Mandatory,
            Type = ValidationRuleType.Missing,
            Target = Target.IssueLog,
            ErrorCode = ValidationCodes.DesignatedMember
        };

        public static readonly ValidationRuleConfig ActiveDesignatedMemberRuleConfig = new ValidationRuleConfig
        {
            Description = "A company must have two active designated members at the time of the approval and submission of financial statements.",
            Name = ActiveDesignatedMember,
            Breadcrumb = ClientManagementBreadcrumbs.RelationShipTab.ToString(),
            DisplayName = "Designated Member",
            ErrorCategory = ErrorCategoryType.Mandatory,
            Type = ValidationRuleType.Invalid,
            Target = Target.IssueLog,
            ErrorCode = ValidationCodes.DesignatedMemberActive
        };

        public static readonly ValidationRuleConfig UnallocatedProfitLossRuleConfig = new ValidationRuleConfig
        {
            Description = PartnershipPartnerValidations.UnallocatedProfitDescription,
            Name = UnallocatedProfit,
            Breadcrumb = ProfitSharesBreadcrumbs.ProfitSharesRule.ToString(),
            DisplayName = PartnershipPartnerValidations.UnallocatedProfitDisplayName,
            ErrorCategory = ErrorCategoryType.Advisory,
            Target = Target.IssueLog,
            ErrorCode = ValidationCodes.UnallocatedProfit
        };

        public static readonly ValidationRuleConfig UnallocatedProfitLossSevereRuleConfig = new ValidationRuleConfig
        {
            Description = PartnershipPartnerValidations.UnallocatedProfitDescription,
            Name = UnallocatedProfit,
            Breadcrumb = ProfitSharesBreadcrumbs.ProfitSharesRule.ToString(),
            DisplayName = PartnershipPartnerValidations.UnallocatedProfitDisplayName,
            ErrorCategory = ErrorCategoryType.Mandatory,
            Target = Target.IssueLog,
            ErrorCode = ValidationCodes.UnallocatedProfit
        };
    }
}