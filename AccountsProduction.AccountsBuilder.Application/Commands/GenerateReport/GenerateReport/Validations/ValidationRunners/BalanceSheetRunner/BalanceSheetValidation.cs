﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.BalanceSheetRunner
{
    public static class BalanceSheetValidation
    {
        public const string BalanceSheet = "BalanceSheet";

        public static readonly ValidationRuleConfig BalanceSheetConfig =
            new ValidationRuleConfig
            {
                Breadcrumb = BalanceSheetBreadcrumbs.BalanceSheetRule.ToString(),
                Description = "Balance sheet should be hidden.",
                Name = BalanceSheet,
                Type = ValidationRuleType.Missing,
                Target = Target.SectionValidation,
                ErrorCategory = ErrorCategoryType.Advisory,
                DisplayName = "Balance Sheet",
                ErrorCode = ValidationCodes.BalanceSheet
            };
    }
}