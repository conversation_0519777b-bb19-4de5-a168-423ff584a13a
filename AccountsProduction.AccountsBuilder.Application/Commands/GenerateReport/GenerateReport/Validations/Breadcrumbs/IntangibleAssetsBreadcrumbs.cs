﻿using AccountsProduction.AccountsBuilder.Application.Common;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs
{
    public static class IntangibleAssetsBreadcrumbs
    {
        private const string AccountsBuilderName = "Accounts Builder";
        private const string AccountingPolicies = "Accounting Policies";
        private const string IntangibleAssets = "Intangible Assets";
        private const string Goodwill = "Goodwill";
        private const string PatentsAndLicenses = "Patents And Licenses";
        private const string DevelopmentCosts = "Development Costs";
        private const string ComputerSoftware = "Computer Software";

        public static Breadcrumb IntangibleAssetsRule =>
            new Breadcrumb
            {
                Value = AccountsBuilderName,
                Child = new Breadcrumb
                {
                    Value = BreadcrumbSection.Frs1021A,
                    Child = new Breadcrumb
                    {
                        Value = AccountingPolicies,
                        Child = new Breadcrumb
                        {
                            Value = IntangibleAssets,
                            Child = null
                        }
                    }
                }
            };

        public static Breadcrumb IntangibleAssetsGoodwill =>
            new Breadcrumb
            {
                Value = IntangibleAssetsRule.ToString(),
                Child = new Breadcrumb
                {
                    Value = Goodwill,
                    Child = null
                }
            };

        public static Breadcrumb IntangibleAssetsPatentsAndLicenses =>
            new Breadcrumb
            {
                Value = IntangibleAssetsRule.ToString(),
                Child = new Breadcrumb
                {
                    Value = PatentsAndLicenses,
                    Child = null
                }
            };

        public static Breadcrumb IntangibleAssetsDevelopmentCosts =>
            new Breadcrumb
            {
                Value = IntangibleAssetsRule.ToString(),
                Child = new Breadcrumb
                {
                    Value = DevelopmentCosts,
                    Child = null
                }
            };

        public static Breadcrumb IntangibleAssetsComputerSoftware =>
            new Breadcrumb
            {
                Value = IntangibleAssetsRule.ToString(),
                Child = new Breadcrumb
                {
                    Value = ComputerSoftware,
                    Child = null
                }
            };
    }
}