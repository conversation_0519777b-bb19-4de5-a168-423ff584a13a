﻿using AccountsProduction.AccountsBuilder.Application.Common.Helper;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs
{
    public static class NotesBreadcrumbs
    {
        private const string AccountsBuilderName = "Accounts Builder";

        private const string NotesToTheFinancialStatements = "Notes to the financial statements";
        private const string AverageNumberOfEmployees = "Average number of employees";

        public static Breadcrumb AverageNumberOfEmployeesRule(string reportStandardType) =>
            new Breadcrumb
            {
                Value = AccountsBuilderName,
                Child = new Breadcrumb
                {
                    Value = BreadcrumbSectionHelper.ReportingBreadcrumbSection(reportStandardType),
                    Child = new Breadcrumb
                    {
                        Value = NotesToTheFinancialStatements,
                        Child = new Breadcrumb
                        {
                            Value = AverageNumberOfEmployees,
                            Child = null
                        }
                    }
                }
            };
    }
}