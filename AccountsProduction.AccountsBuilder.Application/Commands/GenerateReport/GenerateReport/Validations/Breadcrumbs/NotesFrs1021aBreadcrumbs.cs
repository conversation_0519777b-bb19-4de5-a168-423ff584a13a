﻿using AccountsProduction.AccountsBuilder.Application.Common;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs
{
    public static class NotesFrs1021aBreadcrumbs
    {
        private const string AccountsBuilderName = "Accounts Builder";
        private const string NotesToTheFinancialStatements = "Notes to the financial statements";
        public const string IntangibleAssets = "Intangible Assets";
        private const string Revaluation = "Revaluation";
        private const string TangibleFixedAssetsNotes = "Tangible Fixed Assets Notes";
        private const string AdvancesCreditGuaranteesGrantedToDirectors = "Advances, credits and guarantees granted to Directors";
        private const string HistoricBreakdown = "Historical Cost Breakdown";
        private const string AnalysisOfCostAndValuation = "Analysis Of Cost Or Valuation";

        public static Breadcrumb IntangibleAssetsRevaluation =>
          new Breadcrumb
          {
              Value = AccountsBuilderName,
              Child = new Breadcrumb
              {
                  Value = BreadcrumbSection.Frs1021A,
                  Child = new Breadcrumb
                  {
                      Value = NotesToTheFinancialStatements,
                      Child = new Breadcrumb
                      {
                          Value = IntangibleAssets,
                          Child = new Breadcrumb
                          {
                              Value = Revaluation,
                              Child = null
                          }
                      }
                  }
              }
          };

        public static Breadcrumb TangibleFixedAssetsNotesSection =>
          new Breadcrumb
          {
              Value = AccountsBuilderName,
              Child = new Breadcrumb
              {
                  Value = BreadcrumbSection.Frs1021A,
                  Child = new Breadcrumb
                  {
                      Value = NotesToTheFinancialStatements,
                      Child = new Breadcrumb
                      {
                          Value = TangibleFixedAssetsNotes,
                          Child = null
                      }
                  }
              }
          };

        public static Breadcrumb TangibleFixedAssetsHistoricBreakdownNotesSection =>
        new Breadcrumb
        {
            Value = AccountsBuilderName,
            Child = new Breadcrumb
            {
                Value = BreadcrumbSection.Frs1021A,
                Child = new Breadcrumb
                {
                    Value = NotesToTheFinancialStatements,
                    Child = new Breadcrumb
                    {
                        Value = TangibleFixedAssetsNotes,
                        Child = new Breadcrumb
                        {
                            Value = HistoricBreakdown,
                            Child = null
                        }
                    }
                }
            }
        };

        public static Breadcrumb TangibleFixedAssetsAnalysisOfCostAndValuationSection =>
        new Breadcrumb
        {
            Value = AccountsBuilderName,
            Child = new Breadcrumb
            {
                Value = BreadcrumbSection.Frs1021A,
                Child = new Breadcrumb
                {
                    Value = NotesToTheFinancialStatements,
                    Child = new Breadcrumb
                    {
                        Value = TangibleFixedAssetsNotes,
                        Child = new Breadcrumb
                        {
                            Value = AnalysisOfCostAndValuation,
                            Child = null
                        }
                    }
                }
            }
        };

        public static Breadcrumb AdvancesCreditGuarantees =>
         new Breadcrumb
         {
             Value = AccountsBuilderName,
             Child = new Breadcrumb
             {
                 Value = BreadcrumbSection.Frs1021A,
                 Child = new Breadcrumb
                 {
                     Value = NotesToTheFinancialStatements,
                     Child = new Breadcrumb
                     {
                         Value = AdvancesCreditGuaranteesGrantedToDirectors,
                         Child = new Breadcrumb
                         {
                             Value = "[Select Director]",
                             Child = null
                         }
                     }
                 }
             }
         };
    }
}