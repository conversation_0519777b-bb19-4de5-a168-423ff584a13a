﻿using AccountsProduction.AccountsBuilder.Application.Common;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs
{
    public static class TangibleFixedAssetsBreadcrumbs
    {
        private const string AccountsBuilderName = "Accounts Builder";
        private const string AccountingPoliciesName = "Accounting Policies";
        private const string TangibleFixedAssetsName = "Tangible Fixed Assets";
        private const string LandAndBuildingsName = "Land and Buildings";
        private const string PlantAndMachineriesName = "Plant and Machinery";
        private const string FreeholdPropertyName = "Freehold Property";
        private const string ShortLeaseholdPropertyName = "Short leasehold property";
        private const string LongLeaseholdPropertyName = "Long leasehold property";
        private const string ImprovementsToPropertyName = "Improvements To Property";
        private const string PlantAndMachineryName = "Plant And Machinery";
        private const string FixturesAndFittingsName = "Fixtures And Fittings";
        private const string MotorVehiclesName = "Motor Vehicles";
        private const string ComputerEquipmentName = "Computer Equipment";

        public static Breadcrumb TangibleFixedAssetsRule => new Breadcrumb
        {
            Value = AccountsBuilderName,
            Child = new Breadcrumb
            {
                Value = BreadcrumbSection.Frs1021A,
                Child = new Breadcrumb
                {
                    Value = AccountingPoliciesName,
                    Child = new Breadcrumb
                    {
                        Value = TangibleFixedAssetsName,
                        Child = null
                    }
                }
            }
        };

        public static Breadcrumb LandAndBuildingsRule => new Breadcrumb
        {
            Value = TangibleFixedAssetsRule.ToString(),
            Child = new Breadcrumb
            {
                Value = LandAndBuildingsName,
                Child = null
            }
        };

        public static Breadcrumb PlantAndMachineriesRule => new Breadcrumb
        {
            Value = TangibleFixedAssetsRule.ToString(),
            Child = new Breadcrumb
            {
                Value = PlantAndMachineriesName,
                Child = null
            }
        };

        public static Breadcrumb FreeholdPropertyRule => new Breadcrumb
        {
            Value = LandAndBuildingsRule.ToString(),
            Child = new Breadcrumb
            {
                Value = FreeholdPropertyName,
                Child = null
            }
        };

        public static Breadcrumb ShortLeaseholdPropertyRule => new Breadcrumb
        {
            Value = LandAndBuildingsRule.ToString(),
            Child = new Breadcrumb
            {
                Value = ShortLeaseholdPropertyName,
                Child = null
            }
        };

        public static Breadcrumb LongLeaseholdPropertyRule => new Breadcrumb
        {
            Value = LandAndBuildingsRule.ToString(),
            Child = new Breadcrumb
            {
                Value = LongLeaseholdPropertyName,
                Child = null
            }
        };

        public static Breadcrumb ImprovementsToPropertyRule => new Breadcrumb
        {
            Value = PlantAndMachineriesRule.ToString(),
            Child = new Breadcrumb
            {
                Value = ImprovementsToPropertyName,
                Child = null
            }
        };

        public static Breadcrumb PlantAndMachineryRule => new Breadcrumb
        {
            Value = PlantAndMachineriesRule.ToString(),
            Child = new Breadcrumb
            {
                Value = PlantAndMachineryName,
                Child = null
            }
        };

        public static Breadcrumb FixturesAndFittingsRule => new Breadcrumb
        {
            Value = PlantAndMachineriesRule.ToString(),
            Child = new Breadcrumb
            {
                Value = FixturesAndFittingsName,
                Child = null
            }
        };

        public static Breadcrumb MotorVehiclesRule => new Breadcrumb
        {
            Value = PlantAndMachineriesRule.ToString(),
            Child = new Breadcrumb
            {
                Value = MotorVehiclesName,
                Child = null
            }
        };

        public static Breadcrumb ComputerEquipmentRule => new Breadcrumb
        {
            Value = PlantAndMachineriesRule.ToString(),
            Child = new Breadcrumb
            {
                Value = ComputerEquipmentName,
                Child = null
            }
        };
    }
}