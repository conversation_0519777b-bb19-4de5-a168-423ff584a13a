﻿using AccountsProduction.AccountsBuilder.Application.Common;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs
{
    public static class NotesFrs105Breadcrumbs
    {
        private const string AccountsBuilderName = "Accounts Builder";
        private const string NotesToTheFinancialStatements = "Notes to the financial statements";
        private const string AdvancesCreditGuarantees = "Advances, credits and guarantees granted to Directors";

        public static Breadcrumb AdvancesCreditGuaranteesRule =>
            new Breadcrumb
            {
                Value = AccountsBuilderName,
                Child = new Breadcrumb
                {
                    Value = BreadcrumbSection.Frs105,
                    Child = new Breadcrumb
                    {
                        Value = NotesToTheFinancialStatements,
                        Child = new Breadcrumb
                        {
                            Value = AdvancesCreditGuarantees,
                            Child = null
                        }
                    }
                }
            };
    }
}