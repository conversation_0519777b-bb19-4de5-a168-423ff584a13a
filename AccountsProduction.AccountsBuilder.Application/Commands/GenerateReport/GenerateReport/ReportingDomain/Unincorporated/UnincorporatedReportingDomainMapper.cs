﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.AccountPeriod;
using Iris.AccountsProduction.AccountsBuilder.Messages.DataScreenValue;
using Iris.Platform.WebApi.Infrastructure.Middleware;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.Unincorporated
{
    public class UnincorporatedReportingDomainMapper : BaseReportingDomainMapper, IMap
    {
        public string ReportStandardType => AccountsBuilder.Application.Common.ReportStandardType.UNINCORPORATED;
        private readonly IMapper _mapper;

        public UnincorporatedReportingDomainMapper(UserContext userContext, IMapper mapper) : base(mapper)
        {
            _mapper = mapper;
        }

        public BaseReportingMessage Map(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var (profitAndLossData, balanceSheetData, otherData) = MapFinancialData(accountsBuilder.FinancialData?.Financials ?? new(), accountsBuilder.TrialBalance?.ReportingPeriods ?? new());

            var requestMessage = new UnincorporatedReportingMessage
            {
                ReportType = AccountsBuilder.Application.Common.ReportStandardType.UNINCORPORATED,
                ClientId = accountsBuilder.ClientId,
                PeriodId = accountsBuilder.PeriodId,
                TenantId = accountsBuilder.TenantId,
                WatermarkText = accountsBuilder.LicenseData.WatermarkText,
                ProfitAndLossData = profitAndLossData,
                Signatures = MapReportingSignatory(accountsBuilder),
                ClientData = _mapper.Map<NonFinancialData, ClientDocumentMessageDto>(accountsBuilder.NonFinancialData),
                Involvements = _mapper.Map<List<ClientInvolvementDto>>(accountsBuilder.InvolvementsData.Involvements),
                ReportingPeriods = MapReportingPeriods(accountsBuilder.TrialBalance.ReportingPeriods),
                BalanceSheetData = balanceSheetData,
                DataScreenValue = _mapper.Map<DataScreenValueMessage>(accountsBuilder.DataScreenValue),
                PracticeDetails = _mapper.Map<PracticeDetailsDto>(accountsBuilder.PracticeDetails),
                EntitySetup = _mapper.Map<EntitySetupDto>(accountsBuilder.EntitySetup),
                ProfitShareData = _mapper.Map<ProfitShareDataDto>(accountsBuilder.ProfitShareData),
                AccountPeriod = _mapper.Map<AccountPeriodMessage>(accountsBuilder.AccountPeriod)
            };

            return requestMessage;
        }
    }
}