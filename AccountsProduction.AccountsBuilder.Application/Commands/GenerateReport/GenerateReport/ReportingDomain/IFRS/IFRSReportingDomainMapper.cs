﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AutoMapper;
using Iris.Platform.WebApi.Infrastructure.Middleware;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.IFRS;

public class IFRSReportingDomainMapper : BaseReportingDomainMapper, IMap
{
    public string ReportStandardType => AccountsBuilder.Application.Common.ReportStandardType.IFRS;

    public IFRSReportingDomainMapper(UserContext userContext, IMapper mapper) : base(mapper)
    {
    }

    public BaseReportingMessage Map(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
    {
        var (IFRSIncomeStatementData, IFRSStatementOfFinancialPositionData, otherData) = MapIFRSFinancialData(accountsBuilder.FinancialData?.Financials ?? new(), accountsBuilder.TrialBalance?.ReportingPeriods ?? new());

        var requestMessage = new FRS1021AAndFRS102SharedReportingMessage
        {
            ReportType = AccountsBuilder.Application.Common.ReportStandardType.IFRS
        };
        AssignIFRSSharedProperties(ref requestMessage, accountsBuilder, IFRSIncomeStatementData, IFRSStatementOfFinancialPositionData, otherData);
        return requestMessage;
    }
}
