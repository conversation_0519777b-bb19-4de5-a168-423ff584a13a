﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AutoMapper;
using Iris.Platform.WebApi.Infrastructure.Middleware;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.Charity;

public class CharityReportingDomainMapper : BaseReportingDomainMapper, IMap
{
    public string ReportStandardType => AccountsBuilder.Application.Common.ReportStandardType.CHARITY_SORP_FRS102;

    public CharityReportingDomainMapper(UserContext userContext, IMapper mapper) : base(mapper)
    {
    }

    public BaseReportingMessage Map(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
    {
        var (profitAndLossData, balanceSheetData, otherData) = MapFinancialData(accountsBuilder.FinancialData?.Financials ?? new(), accountsBuilder.TrialBalance?.ReportingPeriods ?? new());

        var requestMessage = new FRS1021AAndFRS102SharedReportingMessage
        {
            ReportType = AccountsBuilder.Application.Common.ReportStandardType.CHARITY_SORP_FRS102
        };

        AssignSharedProperties(ref requestMessage, accountsBuilder, profitAndLossData, balanceSheetData, otherData);

        return requestMessage;
    }
}