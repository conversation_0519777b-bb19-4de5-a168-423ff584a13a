﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AutoMapper;
using Iris.Platform.WebApi.Infrastructure.Middleware;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.FRS1021A
{
    public class FRS1021AReportingDomainMapper : BaseReportingDomainMapper, IMap
    {
        public string ReportStandardType => AccountsBuilder.Application.Common.ReportStandardType.FRS102_1A;

        public FRS1021AReportingDomainMapper(UserContext userContext, IMapper mapper) : base(mapper)
        {
        }

        public BaseReportingMessage Map(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var (profitAndLossData, balanceSheetData, otherData) = MapFinancialData(accountsBuilder.FinancialData?.Financials ?? new(), accountsBuilder.TrialBalance?.ReportingPeriods ?? new());

            var requestMessage = new FRS1021AAndFRS102SharedReportingMessage
            {
                ReportType = AccountsBuilder.Application.Common.ReportStandardType.FRS102_1A
            };

            AssignSharedProperties(ref requestMessage, accountsBuilder, profitAndLossData, balanceSheetData, otherData);

            if (requestMessage.Notes?.TangibleFixedAssetsNotes?.ValuationInCurrentReportingPeriod?.DateOfRevaluation ==
                DateTime.MinValue)
            {
                requestMessage.Notes.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.DateOfRevaluation =
                    DateOfRevaluationSetDefaultDate(requestMessage.Notes.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.DateOfRevaluation,
                    requestMessage.ReportingPeriods, requestMessage.PeriodId);
            }

            return requestMessage;
        }

        private DateTime DateOfRevaluationSetDefaultDate(DateTime dateOfRevaluation, List<ReportingPeriodDto> reportingPeriods,
            Guid currentPeriodId)
        {

            return reportingPeriods.FirstOrDefault(x => x.Id == currentPeriodId)?.EndDate ?? dateOfRevaluation;
        }
    }
}