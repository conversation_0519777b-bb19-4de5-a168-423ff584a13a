﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.AccountPeriod;
using Iris.AccountsProduction.AccountsBuilder.Messages.DataScreenValue;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;
using Iris.Platform.WebApi.Infrastructure.Middleware;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.FRS105
{
    public class FRS105ReportingDomainMapper : BaseReportingDomainMapper, IMap
    {
        public string ReportStandardType => AccountsBuilder.Application.Common.ReportStandardType.FRS105;
        private readonly IMapper _mapper;

        public FRS105ReportingDomainMapper(UserContext userContext, IMapper mapper) : base(mapper)
        {
            _mapper = mapper;
        }

        public BaseReportingMessage Map(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var mappedFinancials = MapFinancialData(accountsBuilder.FinancialData?.Financials ?? new(), accountsBuilder.TrialBalance?.ReportingPeriods ?? new());

            var requestMessage = new FRS105ReportingMessage
            {
                ReportType = AccountsBuilder.Application.Common.ReportStandardType.FRS105,
                ClientId = accountsBuilder.ClientId,
                PeriodId = accountsBuilder.PeriodId,
                TenantId = accountsBuilder.TenantId,
                WatermarkText = accountsBuilder.LicenseData.WatermarkText,
                ProfitAndLossData = mappedFinancials.ProfitAndLossData,
                Signatures = MapReportingSignatory(accountsBuilder),
                ClientData = _mapper.Map<NonFinancialData, ClientDocumentMessageDto>(accountsBuilder.NonFinancialData),
                Involvements = _mapper.Map<List<ClientInvolvementDto>>(accountsBuilder.InvolvementsData?.Involvements),
                ReportingPeriods = MapReportingPeriods(accountsBuilder.TrialBalance.ReportingPeriods),
                BalanceSheetData = mappedFinancials.BalanceSheetData,
                Notes = _mapper.Map<NotesResponseDataMessage>(accountsBuilder.Notes),
                DataScreenValue = _mapper.Map<DataScreenValueMessage>(accountsBuilder.DataScreenValue),
                PracticeDetails = _mapper.Map<PracticeDetailsDto>(accountsBuilder.PracticeDetails),
                EntitySetup = _mapper.Map<EntitySetupDto>(accountsBuilder.EntitySetup),
                ReportVersion = accountsBuilder.ReportingStandard?.Version,
                ProfitShareData = _mapper.Map<ProfitShareDataDto>(accountsBuilder.ProfitShareData),
                AccountPeriod = _mapper.Map<AccountPeriodMessage>(accountsBuilder.AccountPeriod)
            };

            return requestMessage;
        }
    }
}