﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AutoMapper;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.FRS102
{

    public class FRS102ReportingDomainMapper : BaseReportingDomainMapper, IMap
    {
        public string ReportStandardType => AccountsBuilder.Application.Common.ReportStandardType.FRS102;

        public FRS102ReportingDomainMapper(UserContext userContext, IMapper mapper) : base(mapper)
        {
        }

        public BaseReportingMessage Map(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var (profitAndLossData, balanceSheetData, otherData) = MapFinancialData(accountsBuilder.FinancialData?.Financials ?? new(), accountsBuilder.TrialBalance?.ReportingPeriods ?? new());

            var requestMessage = new FRS1021AAndFRS102SharedReportingMessage
            {
                ReportType = AccountsBuilder.Application.Common.ReportStandardType.FRS102
            };

            AssignSharedProperties(ref requestMessage, accountsBuilder, profitAndLossData, balanceSheetData, otherData);

            return requestMessage;
        }
    }
}