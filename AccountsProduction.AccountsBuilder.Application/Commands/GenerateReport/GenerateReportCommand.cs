﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Address;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.PracticeDetails;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Application.Common.Helper;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Application.IntegrationEvents;
using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels;
using AccountsProduction.AccountsBuilder.Domain.NoteModels;
using AutoMapper;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Application.Commands.GenerateReport
{
    public class GenerateReportCommand : IRequest<Unit>
    {
        public Guid ClientId { get; set; }
        public Guid PeriodId { get; set; }
        public Guid? PreviousPeriodId { get; set; }
        public ReportingStandardDto ReportingStandard { get; set; } = null!;
    }

    public class GenerateReportCommandHandler : IRequestHandler<GenerateReportCommand, Unit>
    {
        private readonly ILogger<GenerateReportCommandHandler> _logger;
        private readonly IMapper _mapper;
        private readonly UserContext _userContext;
        private readonly IAccountsBuilderRepository _repository;
        private readonly IGenerateReportDataService _generateReportDataService;
        private readonly IEnumerable<IMap> _reportMappers;
        private readonly IValidationHandler _validationHandler;
        private readonly IMediator _mediator;
        private readonly IDomainEventService _domainEventService;

        public GenerateReportCommandHandler(
            UserContext userContext,
            IMapper mapper,
            ILogger<GenerateReportCommandHandler> logger,
            IGenerateReportDataService generateReportDataService,
            IAccountsBuilderRepository repository,
            IAccountsBuilderService accountsBuilderReportingService,
            IValidationHandler validationHandler,
            IEnumerable<IMap> reportMappers,
            IMediator mediator,
            IDomainEventService domainEventService)
        {
            _logger = logger;
            _repository = repository;
            _userContext = userContext;
            _mapper = mapper;
            _generateReportDataService = generateReportDataService;
            _validationHandler = validationHandler;
            _reportMappers = reportMappers;
            _mediator = mediator;
            _domainEventService = domainEventService;
        }

        public async Task<Unit> Handle(GenerateReportCommand request, CancellationToken cancellationToken)
        {
            try
            {
                await Retry.RunAndHandleConcurrency(async () => await ProcessRequest(request, cancellationToken), _logger);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Failed to process genrating report");
                throw;
            }
            return Unit.Value;
        }

        private async Task ProcessRequest(GenerateReportCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Start processing new accounts builder request at eventTimestamp {datetime}.", DateTime.UtcNow);

            var accountsBuilder = await _repository.Get(request.ClientId, request.PeriodId, cancellationToken);

            var reportingStandard = _mapper.Map<ReportingStandard>(request.ReportingStandard);

            var licenseData = new LicenseData(LicenseHelper.IsAccountsProdTrialLicense(_userContext));

            if (HasFinancialDataFailed(accountsBuilder))
            {
                accountsBuilder.SetStatus(accountsBuilder.FinancialData.StatusCode);
                accountsBuilder.UpdateErrorCode(accountsBuilder.FinancialData.ErrorCode);
                accountsBuilder.UpdateLicenseData(licenseData);
                accountsBuilder.UpdateReportingStandard(reportingStandard);

                await _repository.Save(accountsBuilder, cancellationToken);

                return;
            }

            var hasTrialBalanceChanges = false;
            if (HasEmptyData(accountsBuilder))
            {
                if (accountsBuilder is null)
                {
                    accountsBuilder = new Domain.AccountsBuilderModels.AccountsBuilder(_userContext.TenantId, request.ClientId, request.PeriodId);
                }
                await AddExternalData(accountsBuilder);
                hasTrialBalanceChanges = true;
            }

            accountsBuilder.UpdateLicenseData(licenseData);

            accountsBuilder.UpdateReportingStandard(reportingStandard);

            if ((accountsBuilder.LastSuccessfullTimeUtc == DateTime.MinValue))
            {
                await UpdateNotesFromPreviousPeriod(accountsBuilder, request.PreviousPeriodId, cancellationToken);
            }

            var validationData = await _validationHandler.Validate(accountsBuilder, accountsBuilder.ReportingStandard.Type);
            accountsBuilder.UpdateValidations(validationData);

            await SendToReporting(accountsBuilder, cancellationToken);

            accountsBuilder.SetClean();
            accountsBuilder.UpdateLastSuccessfullTime();
            accountsBuilder.UpdateLastSuccessfullProcessId();
            accountsBuilder.SetStatus(accountsBuilder.FinancialData.StatusCode);
            accountsBuilder.UpdateErrorCode(accountsBuilder.FinancialData.ErrorCode);
            await _repository.Save(accountsBuilder, cancellationToken);

            if (hasTrialBalanceChanges)
            {
                var notificationTrialBalanceChanged = new NotificationTrialBalanceChanged(accountsBuilder.ClientId, accountsBuilder.PeriodId, accountsBuilder.ProcessId, accountsBuilder.EntitySetup.ReportingStandard, _mapper.Map<TrialBalanceDto>(accountsBuilder.TrialBalance));
                await _domainEventService.Publish(notificationTrialBalanceChanged, CancellationToken.None);
            }

            _logger.LogInformation("Finished processing new accounts builder request  at eventTimestamp {dateTime}.", DateTime.UtcNow);

        }

        private bool HasEmptyData(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            return (accountsBuilder is null || accountsBuilder.EntitySetup is null || !accountsBuilder.TrialBalance.TrialBalances.Any() || string.IsNullOrEmpty(accountsBuilder.NonFinancialData?.CompanyName) || string.IsNullOrEmpty(accountsBuilder.NonFinancialData?.BusinessType));
        }

        private async Task SendToReporting(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Sending account builder message");

            var reportMapper = _reportMappers.First(c => c.ReportStandardType == accountsBuilder.ReportingStandard.Type);
            var message = reportMapper.Map(accountsBuilder);
            var reportingMessage = _mapper.Map<AccountsBuilderReportingMessageDto>(message);

            await _mediator.Send(new AccountsBuilder.Reporting.Application.Commands.ProcessAccountsBuilderDataCommand
            {
                Message = reportingMessage
            }, cancellationToken);

        }

        private bool HasFinancialDataFailed(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            return accountsBuilder?.FinancialData is { StatusCode: Status.Fail };
        }

        private async Task UpdateNotesFromPreviousPeriod(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder, Guid? previousPeriodId, CancellationToken cancellationToken)
        {
            var (nots, accountingPolicies) = (new Notes(), new AccountingPolicies());

            if (previousPeriodId.HasValue)
            {
                (nots, accountingPolicies) = await GetPreviousNotes(accountsBuilder.ClientId, accountsBuilder.PeriodId, previousPeriodId.Value, cancellationToken);
            }

            accountsBuilder.AddNotes(nots);
            accountsBuilder.AddAccountingPolicies(accountingPolicies);
        }

        private async Task AddExternalData(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var generateReportDto = await _generateReportDataService.GetReportData(accountsBuilder.ClientId, accountsBuilder.PeriodId);

            var entitySetup = GetEntitySetup(generateReportDto.EntitySetupDto)!;
            var involvements = GetInvolvements(generateReportDto.InvolvementDtos);
            var trialBalance = GetTrialBalance(generateReportDto.TrialBalanceDto);
            var practiceDetails = GetPracticeDetails(generateReportDto.PracticeDetailsDto);
            var nonFinancialData = GetNonFinancialData(generateReportDto.ClientResponse, generateReportDto.ClientAddressDto);


            accountsBuilder.AddEntitySetup(entitySetup);

            accountsBuilder.UpdateNonFinancialData(nonFinancialData);
            var involvementsData = new InvolvementsData
            {
                Involvements = involvements,
                IsDataCompleted = true
            };

            accountsBuilder.AddInvolvements(involvementsData);


            accountsBuilder.SetProcessId(Guid.NewGuid());
            accountsBuilder.FinancialData.ResetRetry();

            accountsBuilder.AddTrialBalance(trialBalance);

            if (practiceDetails is not null)
            {
                accountsBuilder.AddPracticeDetails(practiceDetails);
            }
        }


        private async Task<(Notes, AccountingPolicies)> GetPreviousNotes(Guid clientId, Guid periodId, Guid previousPeriodId, CancellationToken cancellationToken)
        {
            var previousAaccountsBuilder = await _repository.Get(clientId, previousPeriodId, cancellationToken);
            var notes = previousAaccountsBuilder?.Notes ?? new Notes();
            notes.PeriodId = periodId;
            notes.PreviousPeriodId = previousPeriodId;
            notes.PreviousPeriod = notes.CurrentPeriod;
            notes.CurrentPeriod = null;

            var accountingPolicies = previousAaccountsBuilder?.AccountingPolicies ?? new AccountingPolicies();
            accountingPolicies.PeriodId = periodId;
            accountingPolicies.PreviousPeriodId = previousPeriodId;
            accountingPolicies.PreviousPeriodAccountingPolicies = accountingPolicies.CurrentPeriodAccountingPolicies;
            accountingPolicies.CurrentPeriodAccountingPolicies = null;

            return (notes, accountingPolicies);
        }

        private EntitySetup? GetEntitySetup(EntitySetupDto? entitySetupDto)
        {
            var entitySetup = _mapper.Map<EntitySetup?>(entitySetupDto);
            return entitySetup;
        }
        private TrialBalance GetTrialBalance(TrialBalanceDto trialBalanceDto)
        {
            var trialBalance = _mapper.Map<TrialBalance>(trialBalanceDto);
            return trialBalance;
        }
        private List<Involvement> GetInvolvements(IEnumerable<InvolvementDto> involvementDtos)
        {
            var involvements = _mapper.Map<List<Involvement>>(involvementDtos);
            return involvements;
        }

        private PracticeDetails? GetPracticeDetails(PracticeDetailMessage? practiceDetailMessage)
        {
            var practiceDetails = _mapper.Map<PracticeDetails?>(practiceDetailMessage);
            return practiceDetails;
        }

        private NonFinancialData GetNonFinancialData(ClientResponse client, ClientAddressDto processedClientAddresses)
        {
            var nonFinancialData = _mapper.Map<NonFinancialData>(client);
            nonFinancialData.ClientAddresses = _mapper.Map<ClientAddress>(processedClientAddresses);
            nonFinancialData.IsDataCompleted = true;
            return nonFinancialData;
        }
    }
}