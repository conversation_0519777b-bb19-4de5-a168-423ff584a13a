﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.Common.Toolkit.ExtensionMethods.Multitenancy;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using MediatR;

namespace AccountsProduction.AccountsBuilder.Application.Queries.AccountsBuilder
{
    public class GetFullReportQuery : IRequest<AccountsBuilderFullDto>
    {
        public Guid PeriodId { get; set; }

        public Guid ClientId { get; set; }
    }

    public class GetFullReportQueryHandler : IRequestHandler<GetFullReportQuery, AccountsBuilderFullDto>
    {
        private readonly IAccountsBuilderRepository _repository;
        private readonly IMapper _mapper;
        private readonly UserContext _userContext;

        public GetFullReportQueryHandler(IAccountsBuilderRepository repository, IMapper mapper,
            UserContext userContext)
        {
            _repository = repository;
            _mapper = mapper;
            _userContext = userContext;
        }

        public async Task<AccountsBuilderFullDto> Handle(GetFullReportQuery request,
            CancellationToken cancellationToken)
        {
            var entity = await _repository.Get(request.ClientId, request.PeriodId, cancellationToken);

            if (entity == null)
            {
                return null;
            }

            entity.ValidateTenant(Guid.Parse(_userContext.TenantId));

            var dto = _mapper.Map<AccountsBuilderFullDto>(entity);

            var previousPeriod = entity.TrialBalance?.ReportingPeriods.FirstOrDefault(p => p.Id != entity.PeriodId);

            if (previousPeriod != null)
            {
                dto.PreviousPeriodId = previousPeriod.Id;
            }

            return dto;
        }

    }
}
