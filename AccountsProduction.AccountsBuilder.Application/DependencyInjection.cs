﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.Charity;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.FRS102;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.FRS1021A;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.FRS105;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.IFRS;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.Unincorporated;
using AccountsProduction.AccountsBuilder.Application.Commands.GenerateReport;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;

namespace AccountsProduction.AccountsBuilder.Application
{
    [ExcludeFromCodeCoverage]
    public static class DependencyInjection
    {
        public static IServiceCollection AddApplication(this IServiceCollection services)
        {
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssemblies(Assembly.GetExecutingAssembly(), Assembly.GetAssembly(typeof(GenerateReportCommand))));

            services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

            AddMappers(services);
            AddValidations(services);

            return services;
        }

        public static void AddMappers(IServiceCollection services)
        {
            services.AddScoped<IMap, FRS105ReportingDomainMapper>();
            services.AddScoped<IMap, FRS1021AReportingDomainMapper>();
            services.AddScoped<IMap, FRS102ReportingDomainMapper>();
            services.AddScoped<IMap, UnincorporatedReportingDomainMapper>();
            services.AddScoped<IMap, CharityReportingDomainMapper>();
            services.AddScoped<IMap, IFRSReportingDomainMapper>();
        }

        public static void AddValidations(IServiceCollection services)
        {
            services.AddScoped<IValidationHandler, ValidationHandler>();
            services.AddScoped<IValidationRunnerBuilder, ValidationRunnerBuilder>();
        }
    }
}