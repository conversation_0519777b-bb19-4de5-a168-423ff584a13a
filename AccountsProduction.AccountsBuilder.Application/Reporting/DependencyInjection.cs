﻿using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper;
using AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;

namespace AccountsProduction.AccountsBuilder.Reporting.Application
{
    [ExcludeFromCodeCoverage]
    public static class DependencyInjection
    {
        public static IServiceCollection AddReportingApplication(this IServiceCollection serviceCollection)
        {
            var configuredAutoMapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            serviceCollection.TryAddSingleton(configuredAutoMapper);

            serviceCollection.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));

            var reportStrategyTypes = Assembly.GetExecutingAssembly().GetTypes().Where(p => typeof(ISaveUpdateStrategy).IsAssignableFrom(p) && !p.IsInterface && !p.IsAbstract);

            foreach (var type in reportStrategyTypes)
            {
                serviceCollection.AddScoped(typeof(ISaveUpdateStrategy), type);
            }

            return serviceCollection;
        }
    }
}
