﻿using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper.Mappers.NotesMappers
{
    public class NoteAccountingPoliciesMapper : Profile
    {
        public NoteAccountingPoliciesMapper()
        {
            CreateMap<AccountingPoliciesResponseDataMessage, List<NoteAccountingPolicies>>()
                .ConvertUsing<NoteAccountingPoliciesDtoToListOfNoteAccountingPoliciesConverter>();
        }

        public class NoteAccountingPoliciesDtoToListOfNoteAccountingPoliciesConverter : ITypeConverter<AccountingPoliciesResponseDataMessage, List<NoteAccountingPolicies>>
        {
            public List<NoteAccountingPolicies> Convert(AccountingPoliciesResponseDataMessage source, List<NoteAccountingPolicies> destination, ResolutionContext context)
            {
                return ExtractNotes(source);
            }

            private List<NoteAccountingPolicies> ExtractNotes(AccountingPoliciesResponseDataMessage source)
            {
                var listOfNotes = new List<NoteAccountingPolicies>();

                listOfNotes.PopulateWhenNotePresent("ConsolidatedAccountsExemptionSection", ((int?)source.ExemptionsFinancialStatements?.Section).ToString());
                listOfNotes.PopulateWhenNotePresent("ConsolidatedAccountsExemptionParentName", source.ExemptionsFinancialStatements?.ParentName);
                listOfNotes.PopulateWhenNotePresent("ConsolidatedAccountsExemptionParentAddress", source.ExemptionsFinancialStatements?.ParentAddress);
                listOfNotes.PopulateWhenNotePresent("PresentationCcyAcctgPolYesNo", source.PresentationCurrency?.ToString());
                listOfNotes.PopulateWhenNotePresent("ForeignCurrenciesAcctgPolYesNo", source.ForeignCurrencies?.ToString());
                listOfNotes.PopulateWhenNotePresent("ResearchAndDevAcctgPolYesNo", source.ResearchAndDevelopment?.ToString());
                listOfNotes.PopulateWhenNotePresent("ChangesInAccountingPoliciesText", source.ChangesInAccountingPolicies);
                listOfNotes.PopulateWhenNotePresent("FinancialInstrumentsAcctgPolicyText", source.FinancialInstrumentsAccountingPolicy);
                listOfNotes.PopulateWhenNotePresent("GovernmentGrantsAcctgPolicyText", source.GovernmentGrantsAccountingPolicy);
                listOfNotes.PopulateWhenNotePresent("MembersTransactionsWithTheLlpText", source.MembersTransactionsWithTheLlpText);

                if (source.TangibleFixedAssets?.PlantAndMachinery != null)
                {
                    listOfNotes.PopulateWhenNotePresent("PlantAndMachineryEtcDescription", source.TangibleFixedAssets.PlantAndMachinery.ClassNameCustomization);

                    var improvementsToPropertyNotes = GetNotesFromImprovementsToProperty(source.TangibleFixedAssets.PlantAndMachinery);
                    listOfNotes.AddRange(improvementsToPropertyNotes);

                    var plantAndMachineryNotes = GetNotesFromPlantAndMachinery(source.TangibleFixedAssets.PlantAndMachinery);
                    listOfNotes.AddRange(plantAndMachineryNotes);

                    var fixturesAndFittingsNotes = GetNotesFromFixturesAndFittings(source.TangibleFixedAssets.PlantAndMachinery);
                    listOfNotes.AddRange(fixturesAndFittingsNotes);

                    var motorVehiclesNotes = GetNotesFromMotorVehicles(source.TangibleFixedAssets.PlantAndMachinery);
                    listOfNotes.AddRange(motorVehiclesNotes);

                    var computerEquipmentNotes = GetNotesFromComputerEquipment(source.TangibleFixedAssets.PlantAndMachinery);
                    listOfNotes.AddRange(computerEquipmentNotes);
                }

                if (source.TangibleFixedAssets?.LandAndBuildings != null)
                {
                    listOfNotes.PopulateWhenNotePresent("LandAndBuildingsDescription", source.TangibleFixedAssets.LandAndBuildings.ClassNameCustomization);

                    var freeholdPropertyNotes = GetNotesFromFreeholdProperty(source.TangibleFixedAssets.LandAndBuildings);
                    listOfNotes.AddRange(freeholdPropertyNotes);

                    var shortLeaseholdPropertyNotes = GetNotesFromShortLeaseholdProperty(source.TangibleFixedAssets.LandAndBuildings);
                    listOfNotes.AddRange(shortLeaseholdPropertyNotes);

                    var longLeaseholdPropertyNotes = GetNotesFromLongLeaseholdProperty(source.TangibleFixedAssets.LandAndBuildings);
                    listOfNotes.AddRange(longLeaseholdPropertyNotes);
                }

                if (source.IntangibleAssets != null)
                {
                    var goodwillNotes = GetNotesFromGoodwill(source.IntangibleAssets);
                    listOfNotes.AddRange(goodwillNotes);

                    var patentsAndLicensesNotes = GetNotesFromPatentsAndLicenses(source.IntangibleAssets);
                    listOfNotes.AddRange(patentsAndLicensesNotes);

                    var developmentCostsNotes = GetNotesFromDevelopmentCosts(source.IntangibleAssets);
                    listOfNotes.AddRange(developmentCostsNotes);

                    var computerSoftwareNotes = GetNotesFromComputerSoftware(source.IntangibleAssets);
                    listOfNotes.AddRange(computerSoftwareNotes);
                }

                return listOfNotes;
            }

            private List<NoteAccountingPolicies> GetNotesFromLongLeaseholdProperty(LandAndBuildingsMessage landAndBuildings)
            {
                var result = new List<NoteAccountingPolicies>();

                result.PopulateWhenNotePresent("LongLeaseholdPropertyDescription", landAndBuildings.LongLeaseholdProperty?.CategoryDescription);
                result.PopulateWhenNotePresent("LongLeaseholdPropertyDepreciationPolicyAB", landAndBuildings.LongLeaseholdProperty?.AlternativeBasis);
                result.PopulateWhenNotePresent("LongLeaseholdPropertyDepreciationPolicyRB", landAndBuildings.LongLeaseholdProperty?.ReducingBalanceBasis?.ToString());
                result.PopulateWhenNotePresent("LongLeaseholdPropertyDepreciationPolicySL", landAndBuildings.LongLeaseholdProperty?.StraightLineBasis?.ToString());

                return result;
            }

            private List<NoteAccountingPolicies> GetNotesFromShortLeaseholdProperty(LandAndBuildingsMessage landAndBuildings)
            {
                var result = new List<NoteAccountingPolicies>();

                result.PopulateWhenNotePresent("ShortLeaseholdPropertyDescription", landAndBuildings.ShortLeaseholdProperty?.CategoryDescription);
                result.PopulateWhenNotePresent("ShortLeaseholdPropertyDepreciationPolicyAB", landAndBuildings.ShortLeaseholdProperty?.AlternativeBasis);
                result.PopulateWhenNotePresent("ShortLeaseholdPropertyDepreciationPolicyRB", landAndBuildings.ShortLeaseholdProperty?.ReducingBalanceBasis?.ToString());
                result.PopulateWhenNotePresent("ShortLeaseholdPropertyDepreciationPolicySL", landAndBuildings.ShortLeaseholdProperty?.StraightLineBasis?.ToString());

                return result;
            }

            private List<NoteAccountingPolicies> GetNotesFromFreeholdProperty(LandAndBuildingsMessage landAndBuildings)
            {
                var result = new List<NoteAccountingPolicies>();

                result.PopulateWhenNotePresent("FreeholdPropertyDescription", landAndBuildings.FreeholdProperty?.CategoryDescription);
                result.PopulateWhenNotePresent("FreeholdPropertyDepreciationPolicyAB", landAndBuildings.FreeholdProperty?.AlternativeBasis);
                result.PopulateWhenNotePresent("FreeholdPropertyDepreciationPolicyRB", landAndBuildings.FreeholdProperty?.ReducingBalanceBasis?.ToString());
                result.PopulateWhenNotePresent("FreeholdPropertyDepreciationPolicySL", landAndBuildings.FreeholdProperty?.StraightLineBasis?.ToString());

                return result;
            }

            private List<NoteAccountingPolicies> GetNotesFromComputerSoftware(IntangibleAssetsMessage intangibleAssets)
            {
                var result = new List<NoteAccountingPolicies>();

                result.PopulateWhenNotePresent("ComputerSoftwareDescription", intangibleAssets.ComputerSoftware?.CategoryDescription);
                result.PopulateWhenNotePresent("ComputerSoftwareAmortisationPolicyAB", intangibleAssets.ComputerSoftware?.AlternativeBasis);
                result.PopulateWhenNotePresent("ComputerSoftwareAmortisationPolicyRB", intangibleAssets.ComputerSoftware?.ReducingBalanceBasis?.ToString());
                result.PopulateWhenNotePresent("ComputerSoftwareAmortisationPolicySL", intangibleAssets.ComputerSoftware?.StraightLineBasis?.ToString());

                return result;
            }

            private List<NoteAccountingPolicies> GetNotesFromDevelopmentCosts(IntangibleAssetsMessage intangibleAssets)
            {
                var result = new List<NoteAccountingPolicies>();

                result.PopulateWhenNotePresent("DevelopmentCostsDescription", intangibleAssets.DevelopmentCosts?.CategoryDescription);
                result.PopulateWhenNotePresent("DevelopmentCostsAmortisationPolicyAB", intangibleAssets.DevelopmentCosts?.AlternativeBasis);
                result.PopulateWhenNotePresent("DevelopmentCostsAmortisationPolicyRB", intangibleAssets.DevelopmentCosts?.ReducingBalanceBasis?.ToString());
                result.PopulateWhenNotePresent("DevelopmentCostsAmortisationPolicySL", intangibleAssets.DevelopmentCosts?.StraightLineBasis?.ToString());

                return result;
            }

            private List<NoteAccountingPolicies> GetNotesFromPatentsAndLicenses(IntangibleAssetsMessage intangibleAssets)
            {
                var result = new List<NoteAccountingPolicies>();

                result.PopulateWhenNotePresent("PatentsAndLicencesDescription", intangibleAssets.PatentsAndLicenses?.CategoryDescription);
                result.PopulateWhenNotePresent("PatentsAndLicencesAmortisationPolicyAB", intangibleAssets.PatentsAndLicenses?.AlternativeBasis);
                result.PopulateWhenNotePresent("PatentsAndLicencesAmortisationPolicyRB", intangibleAssets.PatentsAndLicenses?.ReducingBalanceBasis?.ToString());
                result.PopulateWhenNotePresent("PatentsAndLicencesAmortisationPolicySL", intangibleAssets.PatentsAndLicenses?.StraightLineBasis?.ToString());

                return result;
            }

            private List<NoteAccountingPolicies> GetNotesFromGoodwill(IntangibleAssetsMessage intangibleAssets)
            {
                var result = new List<NoteAccountingPolicies>();

                result.PopulateWhenNotePresent("GoodwillDescription", intangibleAssets.Goodwill?.CategoryDescription);
                result.PopulateWhenNotePresent("GoodwillAmortisationPolicyAB", intangibleAssets.Goodwill?.AlternativeBasis);
                result.PopulateWhenNotePresent("GoodwillAmortisationPolicyRB", intangibleAssets.Goodwill?.ReducingBalanceBasis?.ToString());
                result.PopulateWhenNotePresent("GoodwillAmortisationPolicySL", intangibleAssets.Goodwill?.StraightLineBasis?.ToString());

                return result;
            }

            private List<NoteAccountingPolicies> GetNotesFromComputerEquipment(PlantAndMachineriesMessage plantAndMachinery)
            {
                var result = new List<NoteAccountingPolicies>();

                result.PopulateWhenNotePresent("ComputerEquipmentDescription", plantAndMachinery.ComputerEquipment?.CategoryDescription);
                result.PopulateWhenNotePresent("ComputerEquipmentDepreciationPolicyAB", plantAndMachinery.ComputerEquipment?.AlternativeBasis);
                result.PopulateWhenNotePresent("ComputerEquipmentDepreciationPolicyRB", plantAndMachinery.ComputerEquipment?.ReducingBalanceBasis?.ToString());
                result.PopulateWhenNotePresent("ComputerEquipmentDepreciationPolicySL", plantAndMachinery.ComputerEquipment?.StraightLineBasis?.ToString());

                return result;
            }

            private List<NoteAccountingPolicies> GetNotesFromMotorVehicles(PlantAndMachineriesMessage plantAndMachinery)
            {
                var result = new List<NoteAccountingPolicies>();

                result.PopulateWhenNotePresent("MotorVehiclesDescription", plantAndMachinery.MotorVehicles?.CategoryDescription);
                result.PopulateWhenNotePresent("MotorVehiclesDepreciationPolicyAB", plantAndMachinery.MotorVehicles?.AlternativeBasis);
                result.PopulateWhenNotePresent("MotorVehiclesDepreciationPolicyRB", plantAndMachinery.MotorVehicles?.ReducingBalanceBasis?.ToString());
                result.PopulateWhenNotePresent("MotorVehiclesDepreciationPolicySL", plantAndMachinery.MotorVehicles?.StraightLineBasis?.ToString());

                return result;
            }

            private List<NoteAccountingPolicies> GetNotesFromFixturesAndFittings(PlantAndMachineriesMessage plantAndMachinery)
            {
                var result = new List<NoteAccountingPolicies>();

                result.PopulateWhenNotePresent("FixturesAndFittingsDescription", plantAndMachinery.FixturesAndFittings?.CategoryDescription);
                result.PopulateWhenNotePresent("FixturesAndFittingsDepreciationPolicyAB", plantAndMachinery.FixturesAndFittings?.AlternativeBasis);
                result.PopulateWhenNotePresent("FixturesAndFittingsDepreciationPolicyRB", plantAndMachinery.FixturesAndFittings?.ReducingBalanceBasis?.ToString());
                result.PopulateWhenNotePresent("FixturesAndFittingsDepreciationPolicySL", plantAndMachinery.FixturesAndFittings?.StraightLineBasis?.ToString());

                return result;
            }

            private List<NoteAccountingPolicies> GetNotesFromPlantAndMachinery(PlantAndMachineriesMessage plantAndMachinery)
            {
                var result = new List<NoteAccountingPolicies>();

                result.PopulateWhenNotePresent("PlantAndMachineryDescription", plantAndMachinery.PlantAndMachinery?.CategoryDescription);
                result.PopulateWhenNotePresent("PlantAndMachineryDepreciationPolicyAB", plantAndMachinery.PlantAndMachinery?.AlternativeBasis);
                result.PopulateWhenNotePresent("PlantAndMachineryDepreciationPolicyRB", plantAndMachinery.PlantAndMachinery?.ReducingBalanceBasis?.ToString());
                result.PopulateWhenNotePresent("PlantAndMachineryDepreciationPolicySL", plantAndMachinery.PlantAndMachinery?.StraightLineBasis?.ToString());

                return result;
            }

            private List<NoteAccountingPolicies> GetNotesFromImprovementsToProperty(PlantAndMachineriesMessage plantAndMachinery)
            {
                var result = new List<NoteAccountingPolicies>();

                result.PopulateWhenNotePresent("ImprovementsToPropertyDescription", plantAndMachinery.ImprovementsToProperty?.CategoryDescription);
                result.PopulateWhenNotePresent("ImprovementsToPropertyDepreciationPolicyAB", plantAndMachinery.ImprovementsToProperty?.AlternativeBasis);
                result.PopulateWhenNotePresent("ImprovementsToPropertyDepreciationPolicyRB", plantAndMachinery.ImprovementsToProperty?.ReducingBalanceBasis?.ToString());
                result.PopulateWhenNotePresent("ImprovementsToPropertyDepreciationPolicySL", plantAndMachinery.ImprovementsToProperty?.StraightLineBasis?.ToString());

                return result;
            }
        }
    }
}
