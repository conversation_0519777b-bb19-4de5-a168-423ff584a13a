﻿using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper.Mappers.NotesMappers
{
    public class NoteBalanceSheetMapper : Profile
    {
        public NoteBalanceSheetMapper()
        {
            CreateMap<NotesResponseDataMessage, List<NoteBalanceSheet>>()
                .ConvertUsing<NotesDtoToListOfNoteBalanceSheetConverter>();

            CreateMap<AccountingPoliciesResponseDataMessage, List<NoteBalanceSheet>>()
                .ConvertUsing<AccountingPoliciesResponseDataMessageToListOfNoteBalanceSheetConverter>();
        }

        public class NotesDtoToListOfNoteBalanceSheetConverter : ITypeConverter<NotesResponseDataMessage, List<NoteBalanceSheet>>
        {
            public List<NoteBalanceSheet> Convert(NotesResponseDataMessage? source, List<NoteBalanceSheet> destination, ResolutionContext context)
            {
                var listOfNotes = new List<NoteBalanceSheet>();
                listOfNotes.AddRange(ExtractIntangibleAssetsRevaluation(source?.IntangibleAssetsRevaluation));
                listOfNotes.AddRange(ExtractTangibleFixedAssetsValuationInCurrentReportingPeriod(source?.TangibleFixedAssetsNotes?.ValuationInCurrentReportingPeriod));
                listOfNotes.AddRange(ExtractTangibleFixedAssetsHistoricalCostBreakdownMessage(source?.TangibleFixedAssetsNotes?.HistoricalCostBreakdown));
                listOfNotes.AddRange(ExtractTangibleFixedAssetsAnalysisOfCost(source?.TangibleFixedAssetsNotes?.AnalysisOfCostOrValuation));
                listOfNotes.AddRange(ExtractLoansAndOtherDebtsDueToMembers(source?.LoansAndOtherDebtsDueToMembers));


                return listOfNotes;
            }

            private List<NoteBalanceSheet> ExtractLoansAndOtherDebtsDueToMembers(string? loansAndOtherDebtsDueToMembers)
            {
                var listOfNotes = new List<NoteBalanceSheet>();

                listOfNotes.PopulateWhenNotePresent("LoansAndOtherDebtsDueToMembersRankingText", loansAndOtherDebtsDueToMembers);

                return listOfNotes;
            }
            private List<NoteBalanceSheet> ExtractIntangibleAssetsRevaluation(string? intangibleAssetsRevaluation)
            {
                var listOfNotes = new List<NoteBalanceSheet>();

                listOfNotes.PopulateWhenNotePresent("IntangibleAssetsRevaluation", intangibleAssetsRevaluation);

                return listOfNotes;
            }


            private List<NoteBalanceSheet> ExtractTangibleFixedAssetsValuationInCurrentReportingPeriod(ValuationInCurrentReportingPeriodMessage? valuationInCurrentReportingPeriod)
            {
                var listOfNotes = new List<NoteBalanceSheet>();

                if (valuationInCurrentReportingPeriod != null)
                {

                    listOfNotes.PopulateWhenNotePresent("TangibleFixedAssetsCurrentYearRevaluation", valuationInCurrentReportingPeriod.ValuationDetails);
                    listOfNotes.PopulateWhenNotePresent("TangibleFixedAssetsCYRevalIndValuer", valuationInCurrentReportingPeriod.IndependentValuerInvolved.ToString());
                    listOfNotes.PopulateWhenNotePresent("TangibleFixedAssetsCYRevalBasis", valuationInCurrentReportingPeriod.RevaluationBasis);

                    if (valuationInCurrentReportingPeriod.DateOfRevaluation != DateTime.MaxValue && valuationInCurrentReportingPeriod.DateOfRevaluation != DateTime.MinValue)
                    {
                        listOfNotes.Add(new NoteBalanceSheet
                        {
                            NoteType = "TangibleFixedAssetsCYRevalDate",
                            NoteText = string.Format("{0:yyyy-MM-dd}", valuationInCurrentReportingPeriod.DateOfRevaluation)
                        });
                    }
                }

                return listOfNotes;
            }

            private List<NoteBalanceSheet> ExtractTangibleFixedAssetsHistoricalCostBreakdownMessage(HistoricalCostBreakdownMessage? historicalCostBreakdownMessage)
            {
                var listOfNotes = new List<NoteBalanceSheet>();

                if (historicalCostBreakdownMessage != null)
                {
                    listOfNotes.PopulateWhenNotePresent("TangibleFixedAssetsRevaluedClass", historicalCostBreakdownMessage.RevaluedAssetClass);
                    listOfNotes.PopulateWhenNotePresent("TangibleFixedAssetsRevaluedClassPronoun", historicalCostBreakdownMessage.RevaluedClassPronoun);
                    listOfNotes.PopulateWhenNotePresent("TangibleFixedAssetsAccDeprcn", historicalCostBreakdownMessage.CurrentReportingPeriodAccumulatedDepreciation);
                    listOfNotes.PopulateWhenNotePresent("TangibleFixedAssetsHistoricCost", historicalCostBreakdownMessage.CurrentReportingPeriodCost);
                }
                return listOfNotes;
            }

            private List<NoteBalanceSheet> ExtractTangibleFixedAssetsAnalysisOfCost(AnalysisOfCostOrValuationMessage? analysisOfCostOrValuation)
            {
                var listOfNotes = new List<NoteBalanceSheet>();

                if (analysisOfCostOrValuation != null)
                {
                    if (analysisOfCostOrValuation.AnalysisOfCostOrValuationItems.IsPopulated())
                    {
                        analysisOfCostOrValuation.AnalysisOfCostOrValuationItems.ForEach(x =>
                            listOfNotes.AddRange(ExtractTangibleFixedAssetsAnalysisOfCostItem(x)));
                    }

                    listOfNotes.PopulateWhenNotePresent("LandAndBuildingsCost", analysisOfCostOrValuation.CostLandAndBuildings);
                    listOfNotes.PopulateWhenNotePresent("LandAndBuildingsValuationTotal", analysisOfCostOrValuation.TotalLandAndBuildings);
                    listOfNotes.PopulateWhenNotePresent("PlantAndMachineryCost", analysisOfCostOrValuation.CostPlantAndMachineryEtc);
                    listOfNotes.PopulateWhenNotePresent("PlantAndMachineryValuationTotal", analysisOfCostOrValuation.TotalPlantAndMachineryEtc);
                }
                return listOfNotes;
            }

            private List<NoteBalanceSheet> ExtractTangibleFixedAssetsAnalysisOfCostItem(AnalysisOfCostOrValuationItemMessage analysisOfCostOrValuationItem)
            {
                var listOfNotes = new List<NoteBalanceSheet>();

                listOfNotes.PopulateWhenNotePresent($"TangibleFixedAssetValuationPeriod{analysisOfCostOrValuationItem.Index}",
                        analysisOfCostOrValuationItem.Year);
                listOfNotes.PopulateWhenNotePresent($"LandAndBuildingsValuationPeriod{analysisOfCostOrValuationItem.Index}",
                        analysisOfCostOrValuationItem.LandAndBuildings);
                listOfNotes.PopulateWhenNotePresent($"PlantAndMachineryValuationPeriod{analysisOfCostOrValuationItem.Index}",
                        analysisOfCostOrValuationItem.PlantAndMachineryEtc);

                return listOfNotes;
            }
        }

        public class AccountingPoliciesResponseDataMessageToListOfNoteBalanceSheetConverter : ITypeConverter<AccountingPoliciesResponseDataMessage, List<NoteBalanceSheet>>
        {
            public List<NoteBalanceSheet> Convert(AccountingPoliciesResponseDataMessage source, List<NoteBalanceSheet> destination, ResolutionContext context)
            {
                return ExtractNotes(source);
            }

            private static List<NoteBalanceSheet> ExtractNotes(AccountingPoliciesResponseDataMessage source)
            {
                var listOfNotes = new List<NoteBalanceSheet>();

                listOfNotes.PopulateWhenNotePresent("GoodwillMaterialYesNo", source.GoodwillMaterial?.ToString());

                return listOfNotes;
            }
        }
    }
}
