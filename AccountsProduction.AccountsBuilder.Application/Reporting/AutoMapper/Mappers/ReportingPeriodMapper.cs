﻿using AccountsProduction.AccountsBuilder.Reporting.Application.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Domain;
using AutoMapper;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper.Mappers
{
    public class ReportingPeriodMapper : Profile
    {
        public ReportingPeriodMapper()
        {
            CreateMap<ReportingPeriodDto, ReportingPeriod>()
                .ForMember(x => x.Client, expression => expression.UseDestinationValue())
                .ForMember(x => x.AccountPeriodId, expression => expression.Ignore())
                .ForMember(x => x.ClientId, expression => expression.Ignore());
        }
    }
}
