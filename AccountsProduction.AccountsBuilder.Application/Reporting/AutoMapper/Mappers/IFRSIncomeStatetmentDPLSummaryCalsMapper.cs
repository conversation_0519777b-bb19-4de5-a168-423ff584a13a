﻿using AccountsProduction.AccountsBuilder.Domain.Reporting.Other;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace AccountsProduction.AccountsBuilder.Application.Reporting.AutoMapper.Mappers
{
    public class IFRSIncomeStatetmentDPLSummaryCalsMapper : Profile
    {
        public IFRSIncomeStatetmentDPLSummaryCalsMapper()
        {
            CreateMap<List<IFRSIncomeStatementMessage>, List<DplSummaryCalcs>>()
               .ConvertUsing<IFRSIncomeStatementMessageListToListOfDplSummaryCalcsConverters>();
        }

        private sealed class IFRSIncomeStatementMessageListToListOfDplSummaryCalcsConverters : ITypeConverter<List<IFRSIncomeStatementMessage>, List<DplSummaryCalcs>>
        {
            public List<DplSummaryCalcs> Convert(List<IFRSIncomeStatementMessage> source, List<DplSummaryCalcs> destination,
                ResolutionContext context)
            {
                return source.Extract();
            }
        }
    }

   
}
