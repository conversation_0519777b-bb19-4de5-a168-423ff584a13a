﻿using AccountsProduction.AccountsBuilder.Domain.Reporting.Other;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AccountsProduction.AccountsBuilder.Application.Reporting.AutoMapper.Mappers
{
    public class IFRSStatementOfFinancialPositionDPLSummaryCalsMapper : Profile
    {
        public IFRSStatementOfFinancialPositionDPLSummaryCalsMapper()
        {
            CreateMap<List<IFRSStatementOfFinancialPositionMessage>, List<DplSummaryCalcs>>()
              .ConvertUsing<IFRSStatementOfFinancialPositionMessageListToListOfDplSummaryCalcsConverters>();
        }

        private sealed class IFRSStatementOfFinancialPositionMessageListToListOfDplSummaryCalcsConverters : ITypeConverter<List<IFRSStatementOfFinancialPositionMessage>, List<DplSummaryCalcs>>
        {
            public List<DplSummaryCalcs> Convert(List<IFRSStatementOfFinancialPositionMessage> source, List<DplSummaryCalcs> destination,
                ResolutionContext context)
            {
                return source.Extract();
            }
        }
    }
}
