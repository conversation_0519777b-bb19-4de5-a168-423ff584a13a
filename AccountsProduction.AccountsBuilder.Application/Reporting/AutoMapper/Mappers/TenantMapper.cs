﻿using AccountsProduction.AccountsBuilder.Reporting.Application.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Domain;
using AutoMapper;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper.Mappers
{
    public class TenantMapper : Profile
    {
        public TenantMapper()
        {
            CreateMap<PracticeDetailsDto, Tenant>()
                .ForMember(s => s.SupervisingBody, opt => opt.MapFrom(s => s.SupervisingBody.ToString()));
        }
    }
}