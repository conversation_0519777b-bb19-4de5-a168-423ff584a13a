﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Address;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AutoMapper;

namespace AccountsProduction.AccountsBuilder.Application.Reporting.AutoMapper.Mappers
{
    public class AccountsBuilderReportingMessageMapper : Profile
    {
        public AccountsBuilderReportingMessageMapper()
        {
            CreateMap<EntitySetupDto, AccountsBuilder.Reporting.Application.Dto.EntitySetupDto>().ReverseMap();
            CreateMap<PracticeDetailsDto, AccountsBuilder.Reporting.Application.Dto.PracticeDetailsDto>().ReverseMap();
            CreateMap<ClientDocumentMessageDto, AccountsBuilder.Reporting.Application.Dto.ClientDto>().ReverseMap();
            CreateMap<ClientAddressDto, AccountsBuilder.Reporting.Application.Dto.ClientAddressDto>().ReverseMap();
            CreateMap<AddressDto, AccountsBuilder.Reporting.Application.Dto.AddressDto>().ReverseMap();
            CreateMap<ClientInvolvementDto, AccountsBuilder.Reporting.Application.Dto.InvolvementDto>().ReverseMap();
            CreateMap<ReportingPeriodDto, AccountsBuilder.Reporting.Application.Dto.PeriodDto>().ReverseMap();
            CreateMap<ReportingSignatureDto, AccountsBuilder.Reporting.Application.Dto.SignatureDto>().ReverseMap();
            CreateMap<ReportingSignatureDetailDto, AccountsBuilder.Reporting.Application.Dto.SignatureDetailDto>().ReverseMap();

            CreateMap<FRS105ReportingMessage, AccountsBuilderReportingMessageDto>().ReverseMap();
            CreateMap<FRS1021AAndFRS102SharedReportingMessage, AccountsBuilderReportingMessageDto>().ReverseMap();
            CreateMap<UnincorporatedReportingMessage, AccountsBuilderReportingMessageDto>().ReverseMap();
        }
    }
}
