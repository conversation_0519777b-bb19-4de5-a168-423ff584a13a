﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AccountsProduction.AccountsBuilder.Reporting.Application.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Domain;
using Iris.AccountsProduction.AccountsBuilder.Messages.AccountPeriod;
using Iris.AccountsProduction.AccountsBuilder.Messages.DataScreenValue;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;

namespace AccountsProduction.AccountsBuilder.Application.Reporting.Dto
{
    public class AccountsBuilderReportingMessageDto
    {
        public Guid ClientId { get; set; }

        public Guid TenantId { get; set; }

        public Guid PeriodId { get; set; }

        public string ReportType { get; set; } = null!;

        public string? WatermarkText { get; set; }

        public ReportingStandardVersion? ReportVersion { get; set; }

        public AccountPeriodMessage? AccountPeriod { get; set; }

        public EntitySetupDto EntitySetup { get; set; } = null!;

        public PracticeDetailsDto? PracticeDetails { get; set; }

        public ClientDto? ClientData { get; set; }

        public List<InvolvementDto> Involvements { get; set; } = new();

        public List<PeriodDto> ReportingPeriods { get; set; } = new();

        public SignatureDto? Signatures { get; set; }

        public List<ProfitAndLossMessage> ProfitAndLossData { get; set; } = new();

        public List<BalanceSheetMessage> BalanceSheetData { get; set; } = new();

        public ProfitShareDataDto ProfitShareData { get; set; } = new();

        public FinancialDataDto? FinancialData { get; set; } = new();

        public NotesResponseDataMessage? Notes { get; set; }

        public DataScreenValueMessage DataScreenValue { get; set; } = new();

        public AccountingPoliciesResponseDataMessage? NoteAccountingPolicies { get; set; }

        public List<OtherMessage> OtherData { get; set; } = new();

        public List<IFRSIncomeStatementMessage> IFRSIncomeStatementData { get; set; } = new();
        public List<IFRSStatementOfFinancialPositionMessage> IFRSStatementOfFinancialPositionData { get; set; } = new();


        public List<Domain.PeriodTrialBalance> TrialBalanceData { get; set; } = new();


        public static implicit operator AccountsBuilderReportingMessageDto(BaseReportingMessage v)
        {
            throw new NotImplementedException();
        }
    }
}