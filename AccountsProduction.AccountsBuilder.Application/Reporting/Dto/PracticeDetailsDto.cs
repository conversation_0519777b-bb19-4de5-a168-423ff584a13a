﻿namespace AccountsProduction.AccountsBuilder.Reporting.Application.Dto
{
    public class PracticeDetailsDto
    {
        public int ReferredType { get; set; }

        public int SupervisingBody { get; set; }

        public string? Name { get; set; }

        public string? AddressLine1 { get; set; }

        public string? AddressLine2 { get; set; }

        public string? AddressLine3 { get; set; }

        public string? AddressTown { get; set; }

        public string? AddressCounty { get; set; }

        public string? AddressPostcode { get; set; }

        public string? AddressCountry { get; set; }
    }
}
