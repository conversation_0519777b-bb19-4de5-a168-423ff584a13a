﻿namespace AccountsProduction.AccountsBuilder.Reporting.Application.Dto
{
    public class ClientDto
    {
        public string? CompanyName { get; set; }

        public string? CompanyRegistrationNumber { get; set; }

        public string? CompanyType { get; set; }

        public string? CompanySubType { get; set; }

        public string? CompanyCategory { get; set; }

        public ClientAddressDto? Addresses { get; set; }
    }
}
