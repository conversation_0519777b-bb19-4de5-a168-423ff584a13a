﻿using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies
{
    public class FRS105Strategy : BaseSaveUpdateStrategy
    {

        private readonly ILogger<FRS105Strategy> _logger;
        private readonly IAccountsProductionReportingDbContext _accountsProductionReportingDbContext;

        public FRS105Strategy(IMediator mediator, ILogger<FRS105Strategy> logger, IAccountsProductionReportingDbContext accountsProductionReportingDbContext, IMapper mapper) : base(mediator, logger, accountsProductionReportingDbContext, mapper)
        {
            _logger = logger;
            _accountsProductionReportingDbContext = accountsProductionReportingDbContext;
        }

        public override string Name => ReportType.FRS105;

        public override async Task SendSaveAndUpdateDataEvents(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
        {
            _logger.LogInformation("{ProcessTime} Start FRS105 events for Client {Client} and Period {@Period}", DateTime.UtcNow, data.ClientId, data.PeriodId);

            await base.SendSaveAndUpdateDataEvents(data, cancellationToken);

            _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateDataEvents for FRS105", DateTime.UtcNow);

            await SaveAndUpdateLineItems(data, cancellationToken);

            _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateLineItems for FRS105", DateTime.UtcNow);

            await SaveAndUpdateProfitAndLoss(data, _accountsProductionReportingDbContext.ProfitAndLossesFRS105, cancellationToken);

            _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateProfitAndLoss for FRS105", DateTime.UtcNow);

            await SaveAndUpdateBalanceSheetForFRS105(data, cancellationToken);

            _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateBalanceSheet for FRS105", DateTime.UtcNow);

            await SaveAndUpdateNotesForFRS105(data, cancellationToken);

            _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateNotes and End of FRS105 events", DateTime.UtcNow);
        }

        private async Task SaveAndUpdateBalanceSheetForFRS105(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
        {
            if (!data.BalanceSheetData.IsPopulated()) return;

            await SaveAndUpdateBalanceSheet(data, _accountsProductionReportingDbContext.BalanceSheetsFRS105, "BalanceSheet FRS105", cancellationToken);
            await SaveAndUpdateBalanceSheet(data, _accountsProductionReportingDbContext.BalanceSheetsLLP, "BalanceSheet LLP", cancellationToken);
        }

        private async Task SaveAndUpdateNotesForFRS105(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
        {
            var dataScreenNoteLists = SaveAndUpdateNoteDataScreenValues(data);

            var noteOther = await MapToNoteOther(data);
            noteOther.AddRange(dataScreenNoteLists.NotesOther);

            var noteProfitAndLoss = await MapToNoteProfitAndLoss(data);
            noteProfitAndLoss.AddRange(dataScreenNoteLists.NotesProfitAndLoss);

            CleanupAllExistingNotes();

            if (noteOther.IsPopulated())
            {
                _logger.LogInformation("Storing {noteOther} NotesOther", noteOther.Count);
                await _accountsProductionReportingDbContext.NotesOther.AddRangeAsync(noteOther, cancellationToken);
            }

            if (noteProfitAndLoss.IsPopulated())
            {
                _logger.LogInformation("Storing {noteProfitAndLoss} NotesProfitAndLoss", noteProfitAndLoss.Count);
                await _accountsProductionReportingDbContext.NotesProfitAndLoss.AddRangeAsync(noteProfitAndLoss, cancellationToken);
            }
        }

    }
}
