﻿using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies
{
    public class FRS1021AStrategy : FRS102BaseStrategy
    {

        public FRS1021AStrategy(
            IMediator mediator,
            ILogger<FRS1021AStrategy> logger,
            IAccountsProductionReportingDbContext accountsProductionReportingDbContext,
            IMapper mapper) : base(mediator, logger, accountsProductionReportingDbContext, mapper)
        {
        }

        public override string ReportTypeName => ReportType.FRS102_1A;

        public override async Task SendSaveAndUpdateDataEvents(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
        {
            await SendFRS102BaseSaveAndUpdateDataEvents(data, ReportType.FRS102_1A, cancellationToken);
        }
    }
}
