﻿using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies;

public class CharityStrategy : FRS102BaseStrategy
{
    public CharityStrategy(
        IMediator mediator,
        ILogger<CharityStrategy> logger,
        IAccountsProductionReportingDbContext accountsProductionReportingDbContext,
        IMapper mapper) : base(mediator, logger, accountsProductionReportingDbContext, mapper)
    {
    }

    public override string ReportTypeName => ReportType.CHARITY;

    public override async Task SendSaveAndUpdateDataEvents(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        await SendFRS102BaseSaveAndUpdateDataEvents(data, ReportType.CHARITY, cancellationToken);
    }
}
