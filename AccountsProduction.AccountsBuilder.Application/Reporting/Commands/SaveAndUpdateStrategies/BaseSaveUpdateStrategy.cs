﻿using AccountsProduction.AccountsBuilder.Application.Reporting.Commands.SaveAndUpdateStrategies;
using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Application.Reporting.Helpers;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Application.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Domain;
using AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet.Contract;
using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using AccountsProduction.AccountsBuilder.Reporting.Domain.ProfitAndLoss.Contract;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.DataScreenValue;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies;

public abstract class BaseSaveUpdateStrategy : ISaveUpdateStrategy
{
    public abstract string Name { get; }
    public Domain.ReportingPeriod? ReportingPeriod { get; set; }

    protected readonly IMediator _mediator;
    private readonly ILogger<BaseSaveUpdateStrategy> _logger;
    private readonly IAccountsProductionReportingDbContext _accountsProductionReportingDbContext;
    private readonly IMapper _mapper;

    protected BaseSaveUpdateStrategy(IMediator mediator, ILogger<BaseSaveUpdateStrategy> logger, IAccountsProductionReportingDbContext accountsProductionReportingDbContext, IMapper mapper)
    {
        _mediator = mediator;
        _logger = logger;
        _accountsProductionReportingDbContext = accountsProductionReportingDbContext;
        _mapper = mapper;
    }

    public bool IsMatch(string name)
    {
        return name == Name;
    }

    public virtual async Task SendSaveAndUpdateDataEvents(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        _logger.LogInformation("{ProcessTime} Start Base events", DateTime.UtcNow);

        await SaveAndUpdateTenant(data, cancellationToken);

        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateTenant", DateTime.UtcNow);

        await SaveAndUpdateClient(data, cancellationToken);

        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateClient", DateTime.UtcNow);

        await SaveAndUpdateReportingPeriods(data, cancellationToken);

        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateReportingPeriods", DateTime.UtcNow);

        await SaveAndUpdateMembers(data, cancellationToken);

        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateMembers", DateTime.UtcNow);

        await SaveAndUpdateSignatures(data, cancellationToken);

        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateSignatures", DateTime.UtcNow);

        await SaveAndUpdateMultiColumnToken(data, cancellationToken);

        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateMultiColumnToken and End of Base events", DateTime.UtcNow);
    }

    private async Task SaveAndUpdateTenant(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        var tenant = ReportingPeriod?.Client?.Tenant ?? await _accountsProductionReportingDbContext.Tenants.FirstOrDefaultAsync(x => x.Id == data.TenantId, cancellationToken);

        if (tenant == null)
        {
            _logger.LogInformation("Add Tenant");

            tenant = new Tenant
            {
                Id = data.TenantId
            };

            if (data.PracticeDetails != null)
            {
                MapTenantDetails(tenant, data.PracticeDetails);
            }

            await _accountsProductionReportingDbContext.Tenants.AddAsync(tenant, cancellationToken);
        }
        else
        {
            _logger.LogInformation("Update Tenant");

            if (data.PracticeDetails != null)
            {
                MapTenantDetails(tenant, data.PracticeDetails);
            }
        }
    }

    private static void MapTenantDetails(Tenant tenant, PracticeDetailsDto practiceDetailsDto)
    {
        tenant.ReferredType = practiceDetailsDto.ReferredType;
        tenant.SupervisingBody = practiceDetailsDto.SupervisingBody.ToString();
        tenant.Name = practiceDetailsDto.Name;
        tenant.AddressLine1 = practiceDetailsDto.AddressLine1;
        tenant.AddressLine2 = practiceDetailsDto.AddressLine2;
        tenant.AddressLine3 = practiceDetailsDto.AddressLine3;
        tenant.AddressTown = practiceDetailsDto.AddressTown;
        tenant.AddressCounty = practiceDetailsDto.AddressCounty;
        tenant.AddressPostcode = practiceDetailsDto.AddressPostcode;
        tenant.AddressCountry = practiceDetailsDto.AddressCountry;
    }

    private async Task SaveAndUpdateClient(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        if (data.ClientData == null) return;

        var client = ReportingPeriod?.Client ?? await _accountsProductionReportingDbContext.Clients.FirstOrDefaultAsync(x => x.Id == data.ClientId, cancellationToken);

        if (client == null)
        {
            _logger.LogInformation("Add Client");

            client = new Client
            {
                Id = data.ClientId,
                TenantId = data.TenantId,
                CompanyName = data.ClientData.CompanyName,
                CompanyRegistrationNumber = data.ClientData.CompanyRegistrationNumber,
                CompanySubtype = data.ClientData.CompanySubType,
                CompanyCategory = data.ClientData.CompanyCategory,
                CompanyType = data.ClientData.CompanyType
            };

            UpdateAddresses(client, data.ClientData);

            await _accountsProductionReportingDbContext.Clients.AddAsync(client, cancellationToken);
        }
        else
        {
            _logger.LogInformation("Update Client");

            client.CompanyName = data.ClientData.CompanyName;
            client.CompanyRegistrationNumber = data.ClientData.CompanyRegistrationNumber;
            client.CompanySubtype = data.ClientData.CompanySubType;
            client.CompanyType = data.ClientData.CompanyType;
            client.CompanyCategory = data.ClientData.CompanyCategory;

            UpdateAddresses(client, data.ClientData);
        }
    }

    private void UpdateAddresses(Client client, ClientDto data)
    {
        if (data.Addresses == null)
        {
            return;
        }

        var mainAddress = data.Addresses.MainAddress;
        var registeredAddress = data.Addresses.RegisteredAddress;


        client.MainAddressCounty = mainAddress?.County;
        client.MainAddressPostcode = mainAddress?.PostCode;
        client.MainAddressTown = mainAddress?.Town;
        client.MainAddressLine1 = mainAddress?.Line1;
        client.MainAddressLine2 = mainAddress?.Line2;

        client.RegisteredOfficeAddressCounty = registeredAddress?.County;
        client.RegisteredOfficeAddressPostcode = registeredAddress?.PostCode;
        client.RegisteredOfficeAddressTown = registeredAddress?.Town;
        client.RegisteredOfficeAddressLine1 = registeredAddress?.Line1;
        client.RegisteredOfficeAddressLine2 = registeredAddress?.Line2;
    }

    private async Task SaveAndUpdateReportingPeriods(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        if (!data.ReportingPeriods.IsPopulated()) return;

        var periodIds = data.ReportingPeriods.Select(x => x.Id).Distinct().ToList();

        var reportingPeriods = await _accountsProductionReportingDbContext.ReportingPeriods
            .Where(x => x.ClientId == data.ClientId && periodIds.Contains(x.AccountPeriodId))
            .ToListAsync(cancellationToken);

        var periods = data.ReportingPeriods.Select(x => new ReportingPeriodDto
        {
            ClientId = data.ClientId,
            AccountPeriodId = x.Id,
            ReportingPeriodEndDate = x.EndDate,
            ReportingPeriodStartDate = x.StartDate
        }).ToList();

        var currentPeriod = periods.OrderByDescending(x => x.ReportingPeriodEndDate).FirstOrDefault();

        foreach (var period in periods)
        {
            var reportingPeriod = reportingPeriods.FirstOrDefault(x =>
                x.ClientId == period.ClientId && x.AccountPeriodId == period.AccountPeriodId);

            var isNew = reportingPeriod == null;

            reportingPeriod = _mapper.Map(period, reportingPeriod);
            if (reportingPeriod == null) { throw new InvalidOperationException(); }

            reportingPeriod.ClientId = period.ClientId;
            reportingPeriod.AccountPeriodId = period.AccountPeriodId;
            reportingPeriod.WatermarkText = data.WatermarkText;

            if (period.AccountPeriodId == currentPeriod?.AccountPeriodId)
            {
                reportingPeriod.ReportingStandard = data.EntitySetup.ReportingStandard;
                reportingPeriod.EntitySize = data.EntitySetup.EntitySize;
                reportingPeriod.Terminology = data.EntitySetup.Terminology;
                reportingPeriod.TradingStatus = data.EntitySetup.TradingStatus;
                reportingPeriod.ChoiceOfStatement = data.EntitySetup.ChoiceOfStatement;
                reportingPeriod.DormantStatus = data.EntitySetup.DormantStatus;
                reportingPeriod.ReportVersion = data.ReportVersion?.ToString();
                reportingPeriod.IndependentReviewType = data.EntitySetup.IndependentReviewType;
                reportingPeriod.ReviseType = data.AccountPeriod?.ReviseType;
                reportingPeriod.CharitySize = data.EntitySetup.CharitySize;
            }
            if (isNew)
            {
                _logger.LogInformation("Add ReportingPeriod {AccountPeriodId}", reportingPeriod.AccountPeriodId);
                await _accountsProductionReportingDbContext.ReportingPeriods.AddAsync(reportingPeriod, cancellationToken);
            }
            else
            {
                _logger.LogInformation("Update ReportingPeriod {AccountPeriodId}", reportingPeriod.AccountPeriodId);
            }
        }
    }

    private async Task SaveAndUpdateMembers(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        if (data.Involvements == null || !data.Involvements.Any())
        {
            return;
        }

        var members = ReportingPeriod?.Client?.Member;

        if (members != null && members.Any())
        {
            _accountsProductionReportingDbContext.Members.RemoveRange(members);
        }

        var recordsToInsert = new List<Member>();
        foreach (var involvement in data.Involvements)
        {
            var member = _mapper.Map<Member>(involvement);
            member.ClientId = data.ClientId;

            recordsToInsert.Add(member);
        }

        if (recordsToInsert.Any())
        {
            _logger.LogInformation("Add Members - {NoOfMembers}", recordsToInsert.Count);
            await _accountsProductionReportingDbContext.Members.AddRangeAsync(recordsToInsert, cancellationToken);
        }
    }
    private async Task SaveAndUpdateSignatures(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        if (data.Signatures == null) return;

        var signatures = ReportingPeriod?.Signature;

        if (signatures != null && signatures.Any())
            _accountsProductionReportingDbContext.Signatures.RemoveRange(signatures);

        if (data.Signatures != null && data.Signatures.Signatures.IsPopulated())
        {
            var newSignatures = data.Signatures.Signatures.Select(s => new Domain.Signature
            {
                ClientId = data.ClientId,
                AccountPeriodId = data.PeriodId,
                SignatureType = s.SignatureType.ToString(),
                SignatoryTitle = s.SignatoryTitle,
                SignatorySurname = s.SignatorySurname,
                SignatoryFirstName = s.SignatoryFirstName,
                SignatureDate = s.SignatureDate,
                DisplayOrder = s.DisplayOrder,
                InvolvementUUID = s.InvolvementUUID,
                InvolvementType = s.InvolvementType
            }).ToList();

            _logger.LogInformation("Add signatories - {NoOfSingatories}", newSignatures.Count);

            await _accountsProductionReportingDbContext.Signatures.AddRangeAsync(newSignatures, cancellationToken);
        }

        if (ReportingPeriod != null)
        {
            _logger.LogInformation("SaveAndUpdateSignatures - Update ReportingPeriod TenantAccountantsReportSignatureDate & includeAccountantsReport");

            ReportingPeriod.TenantAccountantsReportSignatureDate = data.Signatures?.AccountantSigningDate;
            ReportingPeriod.IncludeAccountantsReport = data.Signatures?.IncludeAccountantsReport ?? false;
        }
        else
        {
            _logger.LogInformation("SaveAndUpdateSignatures - ReportingPeriod is null");
        }

    }

    protected virtual async Task SaveAndUpdateLineItems(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Saving line items");

        if (!data.ProfitAndLossData.HasLineItems() && !data.BalanceSheetData.HasLineItems() && !data.OtherData.HasLineItems()) return;

        if (data.ReportingPeriods == null) { throw new InvalidOperationException(); }

        RemoveExistingLineItemsForCurrentPeriod();

        await ExtractAndStoreLineItems(data, cancellationToken);
    }

    private void RemoveExistingLineItemsForCurrentPeriod()
    {
        var dbLineItems = ReportingPeriod?.LineItem;

        _logger.LogInformation("Removing {Count} Line Items.", dbLineItems?.Count);

        if (dbLineItems != null && dbLineItems.Any())
            _accountsProductionReportingDbContext.LineItems.RemoveRange(dbLineItems);
    }

    private async Task ExtractAndStoreLineItems(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        var lineItems = GetLineItemsForCurrentPeriodFrom(data);
        if (!lineItems.Any())
        {
            _logger.LogInformation("LineItems are empty!");
            return;
        }
        _logger.LogInformation("Storing {Count} Line Items.", lineItems.Count);

        await _accountsProductionReportingDbContext.LineItems.AddRangeAsync(lineItems, cancellationToken);
    }

    private List<LineItem> GetLineItemsForCurrentPeriodFrom(AccountsBuilderReportingMessageDto data)
    {
        var currentPeriodId = data.ReportingPeriods.MaxBy(x => x.EndDate)!.Id;

        var lineItems = new List<LineItem>();

        var mappedLineItems = MapToLineItems(data);

        lineItems.AddRange(mappedLineItems);

        lineItems.ForEach(x => { x.ClientId = data.ClientId; });

        _logger.LogInformation("LineItems - count: {lineItemsCount}", lineItems.Count);

        lineItems = LineItemHelper.MergeItems(lineItems, data.ReportingPeriods);

        _logger.LogInformation("LineItems after merge");

        lineItems = lineItems.Where(x => x.AccountPeriodId == currentPeriodId).ToList();

        return lineItems;
    }

    protected virtual List<LineItem> MapToLineItems(AccountsBuilderReportingMessageDto data)
    {
        var profitAndLossLineItems = _mapper.Map<List<LineItem>>(data.ProfitAndLossData);
        var balanceSheetsLineItems = _mapper.Map<List<LineItem>>(data.BalanceSheetData);
        var otherLineItems = _mapper.Map<List<LineItem>>(data.OtherData);

        var lineItems = profitAndLossLineItems.Concat(balanceSheetsLineItems).Concat(otherLineItems).ToList();

        if (data.ProfitShareData != null && data.ProfitShareData.ProfitShares.Any())
        {
            _logger.LogInformation("Adding ProfitShare Line Items");
            var profitShareLineItems = _mapper.Map<List<LineItem>>(data.ProfitShareData.ProfitShares);
            lineItems = lineItems.Concat(profitShareLineItems).ToList();
        }

        _logger.LogInformation("Mapped {Count} Line Items", lineItems.Count);

        return lineItems;
    }

    protected async Task<List<NoteOther>> MapToNoteOther(AccountsBuilderReportingMessageDto data)
    {
        if (data.Notes == null)
        {
            _logger.LogInformation("Notes was Null!");
            return await Task.FromResult(new List<NoteOther>());
        }

        var notesList = _mapper.Map<List<NoteOther>>(data.Notes);
        if (notesList.Count != 0)
        {
            foreach (var noteItem in notesList)
            {
                noteItem.AccountPeriodId = data.PeriodId;
                noteItem.ClientId = data.ClientId;
            }
        }

        return await Task.FromResult(notesList);
    }

    protected async Task<List<NoteProfitAndLoss>> MapToNoteProfitAndLoss(AccountsBuilderReportingMessageDto data)
    {
        if (data.Notes == null)
        {
            _logger.LogInformation("Notes was Null!");
            return await Task.FromResult(new List<NoteProfitAndLoss>());
        }

        var notesList = _mapper.Map<List<NoteProfitAndLoss>>(data.Notes);
        if (notesList.Count != 0)
        {
            foreach (var noteItem in notesList)
            {
                noteItem.AccountPeriodId = data.PeriodId;
                noteItem.ClientId = data.ClientId;
            }
        }

        return await Task.FromResult(notesList);
    }

    protected async Task<List<NoteAccountingPolicies>> MapToAccountingPolicies(AccountsBuilderReportingMessageDto data)
    {
        if (data.NoteAccountingPolicies == null)
        {
            _logger.LogInformation("NotesAccountingPolicies was Null!");
            return await Task.FromResult(new List<NoteAccountingPolicies>());
        }

        var notesList = _mapper.Map<List<NoteAccountingPolicies>>(data.NoteAccountingPolicies);
        if (notesList.Count != 0)
        {
            foreach (var noteItem in notesList)
            {
                noteItem.AccountPeriodId = data.PeriodId;
                noteItem.ClientId = data.ClientId;
            }
        }

        return await Task.FromResult(notesList);
    }

    protected async Task<List<NoteBalanceSheet>> MapToNoteBalanceSheet(AccountsBuilderReportingMessageDto data)
    {
        var notesList = new List<NoteBalanceSheet>();

        notesList.AddRange(MapNoteAccountingPoliciesToNoteBalanceSheet(data));
        notesList.AddRange(MapNotesToNoteBalanceSheet(data));

        return await Task.FromResult(notesList);
    }

    private List<NoteBalanceSheet> MapNoteAccountingPoliciesToNoteBalanceSheet(AccountsBuilderReportingMessageDto data)
    {
        if (data.NoteAccountingPolicies == null)
        {
            _logger.LogInformation("NotesAccountingPolicies was Null!");
            return new List<NoteBalanceSheet>();
        }

        var notesList = _mapper.Map<List<NoteBalanceSheet>>(data.NoteAccountingPolicies);
        if (notesList.Count != 0)
        {
            foreach (var noteItem in notesList)
            {
                noteItem.AccountPeriodId = data.PeriodId;
                noteItem.ClientId = data.ClientId;
            }
        }

        return notesList;
    }

    private List<NoteBalanceSheet> MapNotesToNoteBalanceSheet(AccountsBuilderReportingMessageDto data)
    {
        if (data.Notes == null)
        {
            _logger.LogInformation("NotesResponseDataMessage was Null!");
            return new List<NoteBalanceSheet>();
        }

        var notesList = _mapper.Map<List<NoteBalanceSheet>>(data.Notes);
        if (notesList.Count != 0)
        {
            foreach (var noteItem in notesList)
            {
                noteItem.AccountPeriodId = data.PeriodId;
                noteItem.ClientId = data.ClientId;
            }
        }

        return notesList;
    }

    public void SaveAndUpdateNoteScreenFields(ScreenFieldMessage screenFields, string reportMappingTable, string screenId,
       Guid clientId, Guid periodId, NoteLists noteLists)
    {
        switch (reportMappingTable)
        {
            case "NoteOther":
                noteLists.NotesOther = DataScreenValueUpdater.UpdateGenericNoteTable(noteLists.NotesOther, screenFields, screenId, clientId, periodId);
                return;
            case "NoteProfitAndLoss":
                noteLists.NotesProfitAndLoss = DataScreenValueUpdater.UpdateGenericNoteTable(noteLists.NotesProfitAndLoss, screenFields, screenId, clientId, periodId);
                return;
            case "NoteAcctgPols":
                noteLists.NotesAccountingPolicies = DataScreenValueUpdater.UpdateGenericNoteTable(noteLists.NotesAccountingPolicies, screenFields, screenId, clientId, periodId);
                return;
            case "NoteBalanceSheet":
                noteLists.NotesBalanceSheet = DataScreenValueUpdater.UpdateGenericNoteTable(noteLists.NotesBalanceSheet, screenFields, screenId, clientId, periodId);
                return;
            case "Reports":
                noteLists.Reports = DataScreenValueUpdater.UpdateGenericNoteTable(noteLists.Reports, screenFields, screenId, clientId, periodId);
                return;
            case "NoteSofa":
                noteLists.NoteSofa = DataScreenValueUpdater.UpdateGenericNoteTable(noteLists.NoteSofa, screenFields, screenId, clientId, periodId);
                return;
            default:
                throw new InvalidOperationException($"Invalid reportMappingTable: {reportMappingTable}");
        }
    }

    public NoteLists SaveAndUpdateNoteDataScreenValues(AccountsBuilderReportingMessageDto data)
    {
        _logger.LogInformation("Saving data screen values.");
        var noteLists = new NoteLists();
        foreach (var screenValueMessage in data.DataScreenValue.CurrentPeriod)
        {
            foreach (var screenFields in screenValueMessage.ScreenFields)
            {
                SaveAndUpdateNoteScreenFields(screenFields, screenValueMessage.ReportMappingTable, screenValueMessage.ScreenId, data.DataScreenValue.ClientId, data.DataScreenValue.PeriodId, noteLists);
            }
        }
        return noteLists;

    }

    protected void CleanupAllExistingNotes()
    {
        _logger.LogInformation("Cleaning up existing notes for current client and period.");

        if (ReportingPeriod?.NoteOther != null && ReportingPeriod.NoteOther.Any())
            _accountsProductionReportingDbContext.NotesOther.RemoveRange(ReportingPeriod.NoteOther);

        if (ReportingPeriod?.NoteAccountingPolicies != null && ReportingPeriod.NoteAccountingPolicies.Any())
            _accountsProductionReportingDbContext.NotesAccountingPolicies.RemoveRange(ReportingPeriod.NoteAccountingPolicies);

        if (ReportingPeriod?.NoteBalanceSheet != null && ReportingPeriod.NoteBalanceSheet.Any())
            _accountsProductionReportingDbContext.NotesBalanceSheet.RemoveRange(ReportingPeriod.NoteBalanceSheet);

        if (ReportingPeriod?.NoteProfitAndLoss != null && ReportingPeriod.NoteProfitAndLoss.Any())
            _accountsProductionReportingDbContext.NotesProfitAndLoss.RemoveRange(ReportingPeriod.NoteProfitAndLoss);

        if (ReportingPeriod?.Reports != null && ReportingPeriod.Reports.Any())
            _accountsProductionReportingDbContext.Reports.RemoveRange(ReportingPeriod.Reports);

        if (ReportingPeriod?.NoteSofa != null && ReportingPeriod.NoteSofa.Any())
            _accountsProductionReportingDbContext.NoteSofa.RemoveRange(ReportingPeriod.NoteSofa);
    }

    protected async Task SaveAndUpdateProfitAndLoss<T>(AccountsBuilderReportingMessageDto data, DbSet<T> profitAndLossesSet, CancellationToken cancellationToken) where T : ProfitAndLossBase
    {
        if (!data.ProfitAndLossData.IsPopulated()) return;

        var periodIds = data.ProfitAndLossData.Select(x => x.PeriodId).Distinct().ToList();
        var profitAndLosses = await profitAndLossesSet
            .Where(x => x.ClientId == data.ClientId && periodIds.Contains(x.AccountPeriodId))
            .ToListAsync(cancellationToken);

        foreach (var profitAndLossDto in data.ProfitAndLossData)
        {
            var profitAndLoss = profitAndLosses.Find(x => x.ClientId == data.ClientId && x.AccountPeriodId == profitAndLossDto.PeriodId);
            var isNew = profitAndLoss == null;
            profitAndLoss = _mapper.Map(profitAndLossDto, profitAndLoss);

            if (profitAndLoss == null)
            {
                throw new InvalidOperationException();
            }

            profitAndLoss.ClientId = data.ClientId;
            profitAndLoss.AccountPeriodId = profitAndLossDto.PeriodId;

            if (isNew)
            {
                _logger.LogInformation($"Storing new {typeof(T).Name} entity");
                await profitAndLossesSet.AddAsync(profitAndLoss, cancellationToken);
            }
            else
            {
                _logger.LogInformation($"Updating existing {typeof(T).Name} entity");
            }
        }
    }

    protected async Task SaveAndUpdateBalanceSheet<TEntity>(
        AccountsBuilderReportingMessageDto data,
        DbSet<TEntity> dbSet,
        string loggerMessage,
        CancellationToken cancellationToken) where TEntity : class, IBalanceSheetBase, new()
    {
        var periodIds = data.BalanceSheetData.Select(x => x.PeriodId).Distinct().ToList();
        var balanceSheets = await dbSet
           .Where(x => x.ClientId == data.ClientId && periodIds.Contains(x.AccountPeriodId))
           .ToListAsync(cancellationToken);

        foreach (var balanceSheetDto in data.BalanceSheetData)
        {
            var balanceSheet = balanceSheets.Find(x =>
                x.ClientId == data.ClientId && x.AccountPeriodId == balanceSheetDto.PeriodId);
            var isNew = balanceSheet == null;
            balanceSheet = _mapper.Map(balanceSheetDto, balanceSheet);
            if (balanceSheet == null) { throw new InvalidOperationException(); }
            balanceSheet.ClientId = data.ClientId;
            balanceSheet.AccountPeriodId = balanceSheetDto.PeriodId;
            if (isNew)
            {
                _logger.LogInformation($"Storing new {loggerMessage} entity");

                await dbSet.AddAsync(balanceSheet, cancellationToken);
            }
            else
            {
                _logger.LogInformation($"Updating existing {loggerMessage} entity");
            }
        }
    }

    public List<Reports> AddSeniorStatutoryAuditorToReports(AccountsBuilderReportingMessageDto data, List<Reports> reports)
    {
        if (data.Signatures?.AccountantSigningName != null)
        {
            reports.Add(new Reports
            {
                ClientId = data.ClientId,
                AccountPeriodId = data.PeriodId,
                NoteType = "SeniorStatutoryAuditor",
                NoteText = data.Signatures?.AccountantSigningName,
                ScreenId = null,
                NoteValue = null
            });
        }
        return reports;
    }

    private async Task SaveAndUpdateMultiColumnToken(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        if (ReportingPeriod?.MultiColumnToken != null && ReportingPeriod.MultiColumnToken.Count != 0)
            _accountsProductionReportingDbContext.MultiColumnToken.RemoveRange(ReportingPeriod.MultiColumnToken);

        var multitokens = await MultiTokenHelper.GetProcessedMultiColumnToken(data, cancellationToken);

        if (multitokens is not null && multitokens.Count != 0)
            await _accountsProductionReportingDbContext.MultiColumnToken.AddRangeAsync(multitokens, cancellationToken);
    }
}
