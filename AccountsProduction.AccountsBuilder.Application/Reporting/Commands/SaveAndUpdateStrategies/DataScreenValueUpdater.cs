﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.Note.Contract;
using Iris.AccountsProduction.AccountsBuilder.Messages.DataScreenValue;
using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.Application.Reporting.Commands.SaveAndUpdateStrategies;

[ExcludeFromCodeCoverage]
public static class DataScreenValueUpdater
{
    public static List<T> UpdateGenericNoteTable<T>(List<T> notes, ScreenFieldMessage screenField, string screenId, Guid clientId, Guid periodId)
    where T : NoteBase, new()
    {
        bool isNum = IsValueNum(screenField);
        bool isValueNull = screenField.Value == null;
        decimal? numValue = isNum ? Convert.ToDecimal(screenField.Value) : null;

        notes.Add(new T
        {
            ClientId = clientId,
            AccountPeriodId = periodId,
            ScreenId = screenId,
            NoteType = screenField.Name,
            NoteText = !isNum && !isValueNull ? screenField?.Value?.ToString() : null,
            NoteValue = isNum && !isValueNull ? numValue : null
        });

        return notes;
    }

    private static bool IsValueNum(ScreenFieldMessage screenField)
    {
        if (screenField.Value == null)
        {
            return false;
        }
        var typeName = screenField.Value.GetType().FullName;

        return typeName switch
        {
            "System.Int32" => true,
            "System.Int64" => true,
            "System.Single" => true,
            "System.Double" => true,
            "System.Decimal" => true,
            _ => false
        };
    }
}
