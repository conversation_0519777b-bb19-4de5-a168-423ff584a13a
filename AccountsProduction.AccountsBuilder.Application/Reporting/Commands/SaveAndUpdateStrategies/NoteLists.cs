﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
namespace AccountsProduction.AccountsBuilder.Application.Reporting.Commands.SaveAndUpdateStrategies;

public class NoteLists
{
    public List<NoteOther> NotesOther { get; set; } = new List<NoteOther>();
    public List<NoteProfitAndLoss> NotesProfitAndLoss { get; set; } = new List<NoteProfitAndLoss>();
    public List<NoteAccountingPolicies> NotesAccountingPolicies { get; set; } = new List<NoteAccountingPolicies>();
    public List<NoteBalanceSheet> NotesBalanceSheet { get; set; } = new List<NoteBalanceSheet>();
    public List<Reports> Reports { get; set; } = new List<Reports>();
    public List<NoteSofa> NoteSofa { get; set; } = new List<NoteSofa>();
}
