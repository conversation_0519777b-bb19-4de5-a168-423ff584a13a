﻿using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AccountsProduction.AccountsBuilder.Domain.NoteModels;
using AccountsProduction.AccountsBuilder.Domain.ProfitShareModels;

namespace AccountsProduction.AccountsBuilder.UnitTests
{
    public static class ReportingDomainMapperTestData
    {
        public static AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder GetAccountsBuilderData(string businessType, EntitySetup entitySetup, ReportingStandard reportingStandard)
        {
            return new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
            {
                ClientId = TestHelpers.Guids.GuidTwo,
                PeriodId = TestHelpers.Guids.GuidThree,
                TenantId = TestHelpers.Guids.GuidFour,
                FinancialData = new FinancialData
                {
                    Financials = new List<Financial>
                    {
                        new Financial
                        {
                            Period = new DateTime(2022, 2, 2),
                            Turnover = GetFinancialDataCategory("Turnover"),
                            CalledUpShareCapitalNotPaid = GetFinancialDataCategory("CalledUpShareCapitalNotPaid"),
                            DistributionExpenses = GetFinancialDataCategory("DistributionExpenses"),
                            AccrualsAndDeferredIncome = GetFinancialDataCategory("AccrualsAndDeferredIncome"),
                            AdministrativeExpenses = GetFinancialDataCategory("AdministrativeExpenses"),
                            AmountsWrittenOffInvestments = GetFinancialDataCategory("AmountsWrittenOffInvestments"),
                            CalledUpShareCapital = GetFinancialDataCategory("CalledUpShareCapital"),
                            CapitalAccount = GetFinancialDataCategory("CapitalAccount"),
                            CapitalAndReserves = GetFinancialDataCategory("CapitalAndReserves"),
                            CapitalRedemptionReserve = GetFinancialDataCategory("CapitalRedemptionReserve"),
                            CashAtBankAndInHand = GetFinancialDataCategory("CashAtBankAndInHand"),
                            CostOfRawMaterialsAndConsumables =
                                GetFinancialDataCategory("CostOfRawMaterialsAndConsumables"),
                            CostOfSales = GetFinancialDataCategory("CostOfSales"),
                            CreditorsAmountsFallingAfterMoreThanOneYear =
                                GetFinancialDataCategory("CreditorsAmountsFallingAfterMoreThanOneYear"),
                            CreditorsAmountsFallingDueWithinOneYear =
                                GetFinancialDataCategory("CreditorsAmountsFallingDueWithinOneYear"),
                            CrossCheck = GetFinancialDataCategory("CrossCheck"),
                            CurrentAssetInvestments = GetFinancialDataCategory("CurrentAssetInvestments"),
                            CurrentAssets = GetFinancialDataCategory("CurrentAssets"),
                            Debtors = GetFinancialDataCategory("Debtors"),
                            Depreciation = GetFinancialDataCategory("Depreciation"),
                            DepreciationAndOtherAmountsWrittenOffAssets =
                                GetFinancialDataCategory("DepreciationAndOtherAmountsWrittenOffAssets"),
                            ExceptionalItems = GetFinancialDataCategory("ExceptionalItems"),
                            Expenses = GetFinancialDataCategory("Expenses"),
                            FairValueReserve = GetFinancialDataCategory("FairValueReserve"),
                            FinanceCosts = GetFinancialDataCategory("FinanceCosts"),
                            FixedAssetInvestments = GetFinancialDataCategory("FixedAssetInvestments"),
                            FixedAssets = GetFinancialDataCategory("FixedAssets"),
                            GainLossG282OnRevaluation = GetFinancialDataCategory("GainLossG282OnRevaluation"),
                            GainLossG50OnRevaluation = GetFinancialDataCategory("GainLossG50OnRevaluation"),
                            Goodwill = GetFinancialDataCategory("Goodwill"),
                            GrossProfitLoss = GetFinancialDataCategory("GrossProfitLoss"),
                            HealthcareObligations = GetFinancialDataCategory("HealthcareObligations"),
                            HerdBasis = GetFinancialDataCategory("HerdBasis"),
                            IncomeFromFixedAssetInvestments =
                                GetFinancialDataCategory("IncomeFromFixedAssetInvestments"),
                            IncomeFromInterestInAssociatedUndertakings =
                                GetFinancialDataCategory("IncomeFromInterestInAssociatedUndertakings"),
                            IncomeFromOtherParticipatingInterests =
                                GetFinancialDataCategory("IncomeFromOtherParticipatingInterests"),
                            IncomeFromSharesInGroupUndertakings =
                                GetFinancialDataCategory("IncomeFromSharesInGroupUndertakings"),
                            IntangibleAssets = GetFinancialDataCategory("IntangibleAssets"),
                            InterestPayableAndSimilarExpenses =
                                GetFinancialDataCategory("InterestPayableAndSimilarExpenses"),
                            InterestReceivableAndSimilarIncome =
                                GetFinancialDataCategory("InterestReceivableAndSimilarIncome"),
                            InvestmentProperty = GetFinancialDataCategory("InvestmentProperty"),
                            NetAssets = GetFinancialDataCategory("NetAssets"),
                            NetCurrentAssetsOrLiabilities = GetFinancialDataCategory("OperatingProfitLoss"),
                            OperatingProfitLoss = GetFinancialDataCategory(""),
                            OtherCharges = GetFinancialDataCategory("OtherCharges"),
                            OtherFinanceCosts = GetFinancialDataCategory("OtherFinanceCosts"),
                            OtherFinanceIncome = GetFinancialDataCategory("OtherFinanceIncome"),
                            OtherIncome = GetFinancialDataCategory("OtherIncome"),
                            OtherOperatingIncome = GetFinancialDataCategory("OtherOperatingIncome"),
                            OtherReserves = GetFinancialDataCategory("OtherReserves"),
                            OtherReserves1 = GetFinancialDataCategory("OtherReserves1"),
                            OtherReserves2 = GetFinancialDataCategory("OtherReserves2"),
                            PartnerAppropriations = GetFinancialDataCategory("PartnerAppropriations"),
                            PartnersCapitalAccounts = GetFinancialDataCategory("PartnersCapitalAccounts"),
                            PartnersCurrentAccounts = GetFinancialDataCategory("PartnersCurrentAccounts"),
                            PensionSchemeAssetsLiabilities = GetFinancialDataCategory("PensionSchemeAssetsLiabilities"),
                            PrepaymentsAndAccruedIncome = GetFinancialDataCategory("PrepaymentsAndAccruedIncome"),
                            ProfitLossForTheFinancialYear = GetFinancialDataCategory("ProfitLossForTheFinancialYear"),
                            ProfitLossOnOrdinaryActivitiesBeforeTaxation =
                                GetFinancialDataCategory("ProfitLossOnOrdinaryActivitiesBeforeTaxation"),
                            ProfitLossReserve = GetFinancialDataCategory("ProfitLossReserve"),
                            ProvisionsForLiabilities = GetFinancialDataCategory("ProvisionsForLiabilities"),
                            RevaluationReserve = GetFinancialDataCategory("RevaluationReserve"),
                            Sales = GetFinancialDataCategory("Sales"),
                            SharePremiumReserve = GetFinancialDataCategory("SharePremiumReserve"),
                            StaffCosts = GetFinancialDataCategory("StaffCosts"),
                            Stock = GetFinancialDataCategory("Stock"),
                            TangibleFixedAssets = GetFinancialDataCategory(""),
                            Tax = GetFinancialDataCategory("tax"),
                            Taxation = GetFinancialDataCategory("Taxation"),
                            TotalAssetsLessCurrentLiabilities =
                                GetFinancialDataCategory("TotalAssetsLessCurrentLiabilities"),
                            WagesAndSalaries = GetFinancialDataCategory("WagesAndSalaries"),
                            ShareCapitalMovements = GetFinancialDataCategory("ShareCapitalMovements"),
                        }
                    }
                },
                TrialBalance = new TrialBalance
                {
                    ReportingPeriods = new List<ReportingPeriod>
                    {
                        new ReportingPeriod
                        {
                            Id = TestHelpers.Guids.GuidSeven,
                            EndDate = new DateTime(2022, 2, 2)
                        }
                    }
                },
                Signatory = GetSignatories(reportingStandard, businessType),

                NonFinancialData = new NonFinancialData
                {
                    CompanyName = "CompanyName",
                    BusinessType = businessType
                },
                InvolvementsData = new InvolvementsData
                {
                    Involvements = GetInvolvements(businessType)
                },
                Notes = new Notes
                {
                    CurrentPeriod = new NotesData
                    {
                        AdditionalNote1 = new AdditionalNote
                        {
                            NoteText = "text",
                            NoteTitle = "title"
                        },
                        TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                        {
                            ValuationInCurrentReportingPeriod = new ValuationInCurrentReportingPeriod
                            {
                                ValuationDetails = "Valuation Details long message",
                                IndependentValuerInvolved = true,
                                RevaluationBasis = "Revaluation Basis  short",
                                DateOfRevaluation = new DateTime(2019, 05, 09, 09, 15, 00)
                            },
                            HistoricalCostBreakdown = new HistoricalCostBreakdown
                            {
                                RevaluedAssetClass = "Revalued Asset Class",
                                RevaluedClassPronoun = "Revalued Class Pronoun",
                                CurrentReportingPeriodAccumulatedDepreciation = 1.33m,
                                CurrentReportingPeriodCost = 1.45m,
                            },
                            AnalysisOfCostOrValuation = new AnalysisOfCostOrValuation
                            {
                                AnalysisOfCostOrValuationItems = new List<AnalysisOfCostOrValuationItem>
                                {
                                    new AnalysisOfCostOrValuationItem
                                    {
                                        Index = 3,
                                        Year = 2019,
                                        LandAndBuildings = 1.1m,
                                        PlantAndMachineryEtc = 2.2m
                                    },
                                    new AnalysisOfCostOrValuationItem
                                    {
                                        Index = 4,
                                        Year = 2020,
                                        LandAndBuildings = 3.3m,
                                        PlantAndMachineryEtc = 4.4m
                                    },
                                    new AnalysisOfCostOrValuationItem
                                    {
                                        Index = 5,
                                        Year = 2021,
                                        LandAndBuildings = -3.3m,
                                        PlantAndMachineryEtc = -4.4m
                                    }
                                },
                                CostLandAndBuildings = 5.5m,
                                CostPlantAndMachineryEtc = 6.6m,
                                TotalLandAndBuildings = 11,
                                TotalPlantAndMachineryEtc = 12
                            }
                        }
                    }
                },
                PracticeDetails = new PracticeDetails
                {
                    AddressCountry = "country",
                    AddressTown = "town"
                },
                EntitySetup = entitySetup,
                ReportingStandard = reportingStandard,
                LicenseData = new LicenseData(),
                ProfitShareData = new ProfitShareData
                {
                    IsSuccessful = true,
                    ProfitShares = new List<ProfitShare>
                    {
                        new()
                        {
                            InvolvementId = 0,
                            CumulativeAmount = 100,
                            AccountPeriodId = TestHelpers.Guids.GuidThree
                        }
                    }
                },
                DataScreenValue = TestData.GetDataScreenValues(),
                AccountPeriod = new AccountPeriod
                {
                    ClientId = TestHelpers.Guids.GuidTwo,
                    PeriodId = TestHelpers.Guids.GuidThree,
                    ReviseType = "SupplementaryNote",
                }
            };
        }

        private static List<Involvement> GetInvolvements(string businessType)
        {
            var involvements = new List<Involvement>
                    {
                        new() {
                            InvolvementClientGuid = TestHelpers.Guids.GuidSeven,
                            InvolvementTitle = "Mr",
                            InvolvementFirstName = "Bob",
                            InvolvementSurname = "Smith",
                            InvolvementType = "Director"
                        },
                        new() {
                            InvolvementClientGuid = TestHelpers.Guids.GuidEight,
                            InvolvementTitle = "Mr",
                            InvolvementFirstName = "Bob",
                            InvolvementSurname = "Smith",
                            InvolvementType = "Director"
                        },
                        new() {
                            InvolvementClientGuid = TestHelpers.Guids.GuidNine,
                            InvolvementTitle = "Mr",
                            InvolvementFirstName = "Smith",
                            InvolvementSurname = "Bob",
                            InvolvementType = "Secretary"
                        }
                    };

            switch (businessType)
            {
                case BusinessTypes.LimitedBusinessType:
                case BusinessTypes.LlpBusinessType:
                    involvements.First().InvolvementType = "Director";
                    involvements.Last().InvolvementType = "Secretary";
                    break;

                case BusinessTypes.SoleTraderBusinessType:
                    involvements.First().InvolvementType = "Proprieter";
                    involvements.Last().InvolvementType = "Proprieter";
                    break;

                case BusinessTypes.PartnershipBusinessType:
                    involvements.First().InvolvementType = "Partner";
                    involvements.Last().InvolvementType = "Partner";
                    break;
            }

            return involvements;
        }

        private static Signatory GetSignatories(ReportingStandard reportingStandard, string businessType)
        {
            var signatory = new Signatory
            {
                Signature = new SignatureDetail
                {
                    AccountantSigningDate = new DateTime(2022, 3, 3),
                    IncludeAccountantsReport = true,
                }
            };

            switch (reportingStandard.Type)
            {
                case "FRS105":
                    signatory.Signature.Signatures = GetFRS105Signatories(reportingStandard);
                    break;
                case "FRS102 1A":
                    signatory.Signature.Signatures = GetFRS1021ASignatories(reportingStandard);
                    break;
                case "Unincorporated":
                    signatory.Signature.Signatures = GetUnincorporatedSignatories(reportingStandard, businessType);
                    break;
            }

            return signatory;

        }

        private static List<Signature> GetFRS105Signatories(ReportingStandard reportingStandard)
        {
            var signatories = new List<Signature>
                            {
                                 new Signature
                                {
                                    SignatoryId = TestHelpers.Guids.GuidSeven,
                                    OrderNumber = 1,
                                    SigningDate = new DateTime(2022, 3, 3),
                                    InvolvementType = "Director",
                                    SignatureType = SignatureType.BalanceSheet
                                },
                                new Signature
                                {
                                    SignatoryId = TestHelpers.Guids.GuidEight,
                                    OrderNumber = 1,
                                    SigningDate = new DateTime(2022, 3, 3),
                                    InvolvementType = "Director",
                                    SignatureType = SignatureType.CIC34Report
                                },
                                new Signature
                                {
                                    SignatoryId = TestHelpers.Guids.GuidNine,
                                    OrderNumber = 1,
                                    InvolvementType = "Secretary",
                                    SignatureType = SignatureType.BalanceSheet
                                },
                                new Signature
                                {
                                    SignatoryId = TestHelpers.Guids.GuidSeven,
                                    OrderNumber = 1,
                                    InvolvementType = "Director",
                                    SigningDate = new DateTime(2024, 12, 3),
                                    SignatureType = SignatureType.SupplementaryNote
                                }
                            };

            return signatories;
        }
        
        private static List<Signature> GetFRS1021ASignatories(ReportingStandard reportingStandard)
        {
            var signatories = new List<Signature>
                            {
                                 new Signature
                                {
                                    SignatoryId = TestHelpers.Guids.GuidSeven,
                                    OrderNumber = 1,
                                    SigningDate = new DateTime(2022, 3, 3),
                                    InvolvementType = "Director",
                                    SignatureType = SignatureType.BalanceSheet
                                },
                                new Signature
                                {
                                    SignatoryId = TestHelpers.Guids.GuidEight,
                                    OrderNumber = 1,
                                    SigningDate = new DateTime(2022, 3, 3),
                                    InvolvementType = "Director",
                                    SignatureType = SignatureType.CIC34Report
                                },
                                new Signature
                                {
                                    SignatoryId = TestHelpers.Guids.GuidNine,
                                    OrderNumber = 1,
                                    InvolvementType = "Secretary",
                                    SignatureType = SignatureType.DirectorsReport
                                },
                                new Signature
                                {
                                    SignatoryId = TestHelpers.Guids.GuidSeven,
                                    OrderNumber = 1,
                                    InvolvementType = "Director",
                                    SigningDate = new DateTime(2024, 12, 3),
                                    SignatureType = SignatureType.SupplementaryNote
                                }
                            };

            return signatories;
        }
        
        private static List<Signature> GetUnincorporatedSignatories(ReportingStandard reportingStandard, string businessType)
        {
            var signatories = new List<Signature>
                            {
                                 new Signature
                                {
                                    SignatoryId = TestHelpers.Guids.GuidSeven,
                                    OrderNumber = 1,
                                    SigningDate = new DateTime(2022, 3, 3),
                                    SignatureType = SignatureType.BalanceSheet
                                },
                                new Signature
                                {
                                    SignatoryId = TestHelpers.Guids.GuidNine,
                                    OrderNumber = 1,
                                    SignatureType = SignatureType.BalanceSheet
                                }
                            };

            switch (businessType)
            {

                case BusinessTypes.SoleTraderBusinessType:
                    signatories.First().InvolvementType = "Proprieter";
                    signatories.Last().InvolvementType = "Proprieter";
                    break;

                case BusinessTypes.PartnershipBusinessType:
                    signatories.First().InvolvementType = "Partner";
                    signatories.Last().InvolvementType = "Partner";
                    break;
            }

            return signatories;
        }

        public static FinancialDataCategory GetFinancialDataCategory(string description, int? directorCode = 1, Guid? sectorId = null, DateTime? sectorCreatedDate = null)
        {
            var randomNumber = new Random();
            return new FinancialDataCategory
            {
                Value = randomNumber.Next().ToString(),
                DrilldownData = new List<FinancialDataDrilldown>
                {
                    new FinancialDataDrilldown
                    {
                        AccountCode = randomNumber.Next(1, 900),
                        SubAccountCode = randomNumber.Next(1, 900),
                        Amount = randomNumber.Next(1, 900),
                        Description = description,
                        DirectorCode = directorCode,
                        SectorId = sectorId,
                        SectorCreatedDate = sectorCreatedDate
                    }
                }
            };
        }
    }
}