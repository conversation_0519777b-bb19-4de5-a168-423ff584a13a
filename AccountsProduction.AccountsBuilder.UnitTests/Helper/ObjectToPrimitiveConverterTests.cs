﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers;
using AccountsProduction.AccountsBuilder.Application.Common.Helper;
using System.Text;
using System.Text.Json;
using Xunit;

public class ObjectToPrimitiveConverterTests
{
    public class ScreenField
    {
        public string Name { get; set; }
        public object Value { get; set; }
    }

    public class ScreenValuesResponseMessage
    {
        public ScreenField[] ScreenFields { get; set; }
    }

    [Fact]
    public void DeserializeWithObject_ShouldDeserializePrimitiveInt()
    {
        string json = "{\"screenFields\":[{\"name\":\"Test\",\"value\":4}]}";

        var result = BaseAggregationStrategy.DeserializeWithObject<ScreenValuesResponseMessage>(json);

        Assert.NotNull(result);
        Assert.NotNull(result.ScreenFields);
        Assert.Equal("Test", result.ScreenFields[0].Name);
        Assert.IsType<long>(result.ScreenFields[0].Value);
        Assert.Equal(4L, result.ScreenFields[0].Value);
    }

    [Fact]
    public void DeserializeWithObject_ShouldDeserializePrimitiveString()
    {
        string json = "{\"screenFields\":[{\"name\":\"Test\",\"value\":\"SomeString\"}]}";

        var result = BaseAggregationStrategy.DeserializeWithObject<ScreenValuesResponseMessage>(json);

        Assert.NotNull(result);
        Assert.NotNull(result.ScreenFields);
        Assert.Equal("Test", result.ScreenFields[0].Name);
        Assert.IsType<string>(result.ScreenFields[0].Value);
        Assert.Equal("SomeString", result.ScreenFields[0].Value);
    }

    [Fact]
    public void DeserializeWithObject_ShouldDeserializePrimitiveBoolean()
    {
        string json = "{\"screenFields\":[{\"name\":\"Test\",\"value\":true}]}";

        var result = BaseAggregationStrategy.DeserializeWithObject<ScreenValuesResponseMessage>(json);

        Assert.NotNull(result);
        Assert.NotNull(result.ScreenFields);
        Assert.Equal("Test", result.ScreenFields[0].Name);
        Assert.IsType<bool>(result.ScreenFields[0].Value);
        Assert.True((bool)result.ScreenFields[0].Value);
    }

    [Fact]
    public void DeserializeWithObject_ShouldDeserializePrimitiveDouble()
    {
        string json = "{\"screenFields\":[{\"name\":\"Test\",\"value\":3.14}]}";

        var result = BaseAggregationStrategy.DeserializeWithObject<ScreenValuesResponseMessage>(json);

        Assert.NotNull(result);
        Assert.NotNull(result.ScreenFields);
        Assert.Equal("Test", result.ScreenFields[0].Name);
        Assert.IsType<double>(result.ScreenFields[0].Value);
        Assert.Equal(3.14, (double)result.ScreenFields[0].Value, 2);
    }

    [Fact]
    public void DeserializeWithObject_ShouldDeserializeNullValue()
    {
        string json = "{\"screenFields\":[{\"name\":\"Test\",\"value\":null}]}";

        var result = BaseAggregationStrategy.DeserializeWithObject<ScreenValuesResponseMessage>(json);

        Assert.NotNull(result);
        Assert.NotNull(result.ScreenFields);
        Assert.Equal("Test", result.ScreenFields[0].Name);
        Assert.Null(result.ScreenFields[0].Value);
    }

    [Fact]
    public void DeserializeWithObject_ShouldHandleNullValue()
    {
        string json = "{\"screenFields\":[{\"name\":\"Test\",\"value\":null}]}";

        var result = BaseAggregationStrategy.DeserializeWithObject<ScreenValuesResponseMessage>(json);

        Assert.NotNull(result);
        Assert.NotNull(result.ScreenFields);
        Assert.Null(result.ScreenFields[0].Value);
    }

    [Fact]
    public void DeserializeWithObject_ShouldThrowForUnexpectedToken()
    {
        string json = "{\"screenFields\":[{\"name\":\"Test\",\"value\":{}}]}";

        Assert.Throws<JsonException>(() => BaseAggregationStrategy.DeserializeWithObject<ScreenValuesResponseMessage>(json));
    }

    [Fact]
    public void Write_ShouldSerializeUnknownType()
    {
        var value = new { Prop = "Test" };
        var options = new JsonSerializerOptions();
        using var stream = new MemoryStream();
        using var writer = new Utf8JsonWriter(stream);

        var converter = new ObjectToPrimitiveConverter();
        converter.Write(writer, value, options);

        var json = Encoding.UTF8.GetString(stream.ToArray());
        Assert.Equal("{\"Prop\":\"Test\"}", json);
    }
}
