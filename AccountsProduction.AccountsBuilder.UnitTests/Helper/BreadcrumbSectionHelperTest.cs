﻿using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.Helper;
using Shouldly;
using Xunit;


namespace AccountsProduction.AccountsBuilder.UnitTests.Helper
{
    public class BreadcrumbSectionHelperTest
    {
        public class When_report_standard_for_section_test : BreadcrumbSectionHelperTest
        {
            [Fact]
            public void Should_return_102a1_as_full_text()
            {
                var result = BreadcrumbSectionHelper.ReportingBreadcrumbSection(ReportStandardType.FRS102_1A);
                result.ShouldBe(BreadcrumbSection.Frs1021A);
            }

            [Fact]
            public void Should_return_105_as_full_text()
            {
                var result = BreadcrumbSectionHelper.ReportingBreadcrumbSection(ReportStandardType.FRS105);
                result.ShouldBe(BreadcrumbSection.Frs105);
            }


        }
    }
}
