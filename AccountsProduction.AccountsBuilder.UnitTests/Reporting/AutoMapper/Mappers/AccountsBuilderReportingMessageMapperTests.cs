﻿using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.Dto;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Address;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;
using Iris.AccountsProduction.AccountsBuilder.Messages.ProfitShare;
using Iris.AccountsProduction.AccountsBuilder.Messages.DataScreenValue;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.AutoMapper.Mappers
{
    public class AccountsBuilderReportingMessageMapperTests
    {
        private readonly IMapper _mapper;
        private readonly Mock<UserContext> _userContext;

        public AccountsBuilderReportingMessageMapperTests()
        {
            _userContext = new Mock<UserContext>();
            _userContext.Setup(x => x.TenantId).Returns(TestHelpers.Guids.GuidFour.ToString());
            _userContext.Setup(x => x.CorrelationId).Returns(TestHelpers.Guids.GuidFive.ToString());
            _userContext.Setup(x => x.UserId).Returns(TestHelpers.Guids.GuidSix.ToString());

            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
        }

        [Fact]
        public void Should_map_FRS105ReportingMessage_to_AccountsBuilderReportingMessageDto()
        {
            // Arrange
            var frs105ReportingMessage = new FRS105ReportingMessage
            {
                ReportType = ReportStandardType.FRS105,
                TenantId = TestHelpers.Guids.GuidOne,
                ClientId = TestHelpers.Guids.GuidTwo,
                PeriodId = TestHelpers.Guids.GuidThree,
                WatermarkText = "Test Watermark",
                EntitySetup = GetEntitySetup(),
                PracticeDetails = GetPracticeDetails(),
                ClientData = GetClientData(),
                Involvements = new List<ClientInvolvementDto> { GetClientInvolvement() },

                ReportingPeriods = new List<ReportingPeriodDto>
                {
                    new() {
                        Id = Guid.NewGuid(), EndDate = new DateTime(2020, 1, 1), StartDate = new DateTime(2019,1,1)
                    }
                },

                Signatures = new ReportingSignatureDto
                {
                    AccountantSigningDate = DateTime.Now,
                    IncludeAccountantsReport = true,
                    Signatures = new List<ReportingSignatureDetailDto>
                    {
                        new()
                        {
                            SignatoryFirstName = "First",
                            SignatorySurname = "Last",
                            SignatoryTitle = "Title",
                            DisplayOrder = 1,
                            SignatureDate = DateTime.Now,
                            SignatureType = AccountsBuilder.Domain.SignatureType.BalanceSheet
                        },
                        new()
                        {
                            SignatoryFirstName = "First",
                            SignatorySurname = "Last",
                            SignatoryTitle = "Title",
                            DisplayOrder = 2,
                            SignatureDate = DateTime.Now,
                            SignatureType = AccountsBuilder.Domain.SignatureType.SupplementaryNote
                        }
                    }

                },

                ProfitAndLossData = new List<ProfitAndLossMessage> { StaticData.Data.GetProfitAndLossMessage(Guid.NewGuid(), DateTime.Now) },

                BalanceSheetData = new List<BalanceSheetMessage> { StaticData.Data.GetBalanceSheetMessage(Guid.NewGuid()) },

                Notes = StaticData.Data.GetNotes(reportType: ReportStandardType.FRS105),

                DataScreenValue = TestData.GetDataScreenValuesMessage(),
            };

            // Act
            var result = _mapper.Map<AccountsBuilderReportingMessageDto>(frs105ReportingMessage);

            // Assert
            Assert.NotNull(result);
            result.TenantId.ShouldBe(frs105ReportingMessage.TenantId);
            result.ClientId.ShouldBe(frs105ReportingMessage.ClientId);
            result.PeriodId.ShouldBe(frs105ReportingMessage.PeriodId);
            result.ReportType.ShouldBe(ReportStandardType.FRS105);
            result.WatermarkText.ShouldBe(frs105ReportingMessage.WatermarkText);

            AssertEntitySetup(result.EntitySetup, frs105ReportingMessage.EntitySetup);
            AssertPracticeDetails(result.PracticeDetails, frs105ReportingMessage.PracticeDetails);
            AssertClientDocumentMessage(result.ClientData, frs105ReportingMessage.ClientData);
            AssertClientInvolvements(result.Involvements.First(), frs105ReportingMessage.Involvements.First());

            var reportingPeriod = result.ReportingPeriods.First();
            AssertReportingPeriodDetails(frs105ReportingMessage.ReportingPeriods.First(), reportingPeriod);

            var signature = result.Signatures;
            signature.AccountantSigningDate.ShouldBe(frs105ReportingMessage.Signatures.AccountantSigningDate);
            signature.IncludeAccountantsReport.ShouldBe(frs105ReportingMessage.Signatures.IncludeAccountantsReport);
            var signatures = signature.Signatures;

            AssertSignatureDetails(frs105ReportingMessage.Signatures.Signatures.First(), signatures.First());
            AssertSignatureDetails(frs105ReportingMessage.Signatures.Signatures.Last(), signatures.Last());


            var profitAndLoss = result.ProfitAndLossData.First();
            AssertProfitAndLossDetails(frs105ReportingMessage.ProfitAndLossData.First(), profitAndLoss);

            var balanceSheet = result.BalanceSheetData.First();
            AssertBalanceSheetDetails(frs105ReportingMessage.BalanceSheetData.First(), balanceSheet);

            var notes = result.Notes;
            AssertNotesDetails(frs105ReportingMessage.Notes, notes);

            AssertDataScreenValues(frs105ReportingMessage.DataScreenValue, result.DataScreenValue);
        }

        [Fact]
        public void Should_map_FRS1021AReportingMessage_to_AccountsBuilderReportingMessageDto()
        {
            // Arrange
            var frs1021aReportingMessage = new FRS1021AAndFRS102SharedReportingMessage
            {
                ReportType = ReportStandardType.FRS102_1A,
                TenantId = TestHelpers.Guids.GuidOne,
                ClientId = TestHelpers.Guids.GuidTwo,
                PeriodId = TestHelpers.Guids.GuidThree,
                WatermarkText = "Test Watermark",
                EntitySetup = GetEntitySetup(),
                PracticeDetails = GetPracticeDetails(),
                ClientData = GetClientData(),
                Involvements = new List<ClientInvolvementDto> { GetClientInvolvement() },

                ReportingPeriods = new List<ReportingPeriodDto>
                {
                    new() {
                        Id = Guid.NewGuid(), EndDate = new DateTime(2020, 1, 1), StartDate = new DateTime(2019,1,1)
                    }
                },

                Signatures = new ReportingSignatureDto
                {
                    AccountantSigningDate = DateTime.Now,
                    IncludeAccountantsReport = true,
                    Signatures = new List<ReportingSignatureDetailDto>
                    {
                        new()
                        {
                            SignatoryFirstName = "First",
                            SignatorySurname = "Last",
                            SignatoryTitle = "Title",
                            DisplayOrder = 1,
                            SignatureDate = DateTime.Now,
                            SignatureType = AccountsBuilder.Domain.SignatureType.BalanceSheet
                        },
                        new()
                        {
                            SignatoryFirstName = "First",
                            SignatorySurname = "Last",
                            SignatoryTitle = "Title",
                            DisplayOrder = 2,
                            SignatureDate = DateTime.Now,
                            SignatureType = AccountsBuilder.Domain.SignatureType.DirectorsReport
                        },
                        new()
                        {
                            SignatoryFirstName = "First",
                            SignatorySurname = "Last",
                            SignatoryTitle = "Title",
                            DisplayOrder = 3,
                            SignatureDate = DateTime.Now,
                            SignatureType = AccountsBuilder.Domain.SignatureType.SupplementaryNote
                        }
                    }

                },

                ProfitAndLossData = new List<ProfitAndLossMessage> { StaticData.Data.GetProfitAndLossMessage(Guid.NewGuid(), DateTime.Now) },

                BalanceSheetData = new List<BalanceSheetMessage> { StaticData.Data.GetBalanceSheetMessage(Guid.NewGuid()) },

                ProfitShareData = new ProfitShareDataDto {
                    ProfitShares = new List<ProfitShareMessage>() { StaticData.Data.GetProfitShare(Guid.NewGuid()) }
                },

                Notes = StaticData.Data.GetNotes(reportType: ReportStandardType.FRS102_1A),
                DataScreenValue = TestData.GetDataScreenValuesMessage(),
            };

            // Act
            var result = _mapper.Map<AccountsBuilderReportingMessageDto>(frs1021aReportingMessage);

            // Assert
            Assert.NotNull(result);
            result.TenantId.ShouldBe(frs1021aReportingMessage.TenantId);
            result.ClientId.ShouldBe(frs1021aReportingMessage.ClientId);
            result.PeriodId.ShouldBe(frs1021aReportingMessage.PeriodId);
            result.ReportType.ShouldBe(ReportStandardType.FRS102_1A);
            result.WatermarkText.ShouldBe(frs1021aReportingMessage.WatermarkText);

            AssertEntitySetup(result.EntitySetup, frs1021aReportingMessage.EntitySetup);
            AssertPracticeDetails(result.PracticeDetails, frs1021aReportingMessage.PracticeDetails);
            AssertClientDocumentMessage(result.ClientData, frs1021aReportingMessage.ClientData);
            AssertClientInvolvements(result.Involvements.First(), frs1021aReportingMessage.Involvements.First());

            var reportingPeriod = result.ReportingPeriods.First();
            AssertReportingPeriodDetails(frs1021aReportingMessage.ReportingPeriods.First(), reportingPeriod);

            var signature = result.Signatures;
            signature.AccountantSigningDate.ShouldBe(frs1021aReportingMessage.Signatures.AccountantSigningDate);
            signature.IncludeAccountantsReport.ShouldBe(frs1021aReportingMessage.Signatures.IncludeAccountantsReport);
            var signatures = signature.Signatures;

            AssertSignatureDetails(frs1021aReportingMessage.Signatures.Signatures[0], signatures[0]);
            AssertSignatureDetails(frs1021aReportingMessage.Signatures.Signatures[1], signatures[1]);
            AssertSignatureDetails(frs1021aReportingMessage.Signatures.Signatures[2], signatures[2]);

            var profitAndLoss = result.ProfitAndLossData.First();
            AssertProfitAndLossDetails(frs1021aReportingMessage.ProfitAndLossData.First(), profitAndLoss);

            var balanceSheet = result.BalanceSheetData.First();
            AssertBalanceSheetDetails(frs1021aReportingMessage.BalanceSheetData.First(), balanceSheet);

            var profitShare = result.ProfitShareData.ProfitShares.First();
            AssertProfitShareDetails(frs1021aReportingMessage.ProfitShareData.ProfitShares.First(), profitShare);

            var notes = result.Notes;
            AssertNotesDetails(frs1021aReportingMessage.Notes, notes);

            AssertDataScreenValues(frs1021aReportingMessage.DataScreenValue, result.DataScreenValue);
        }

        [Fact]
        public void Should_map_UnincorporatedReportingMessage_to_AccountsBuilderReportingMessageDto()
        {
            // Arrange
            var unicorporatedReportingMessage = new UnincorporatedReportingMessage
            {
                ReportType = ReportStandardType.UNINCORPORATED,
                TenantId = TestHelpers.Guids.GuidOne,
                ClientId = TestHelpers.Guids.GuidTwo,
                PeriodId = TestHelpers.Guids.GuidThree,
                WatermarkText = "Test Watermark",
                EntitySetup = GetEntitySetup(),
                PracticeDetails = GetPracticeDetails(),
                ClientData = GetClientData(),
                Involvements = new List<ClientInvolvementDto> { GetClientInvolvement() },

                ReportingPeriods = new List<ReportingPeriodDto>
                {
                    new() {
                        Id = Guid.NewGuid(), EndDate = new DateTime(2020, 1, 1), StartDate = new DateTime(2019,1,1)
                    }
                },

                Signatures = new ReportingSignatureDto
                {
                    AccountantSigningDate = DateTime.Now,
                    IncludeAccountantsReport = true,
                    Signatures = new List<ReportingSignatureDetailDto>
                    {
                        new()
                        {
                            SignatoryFirstName = "First",
                            SignatorySurname = "Last",
                            SignatoryTitle = "Title",
                            DisplayOrder = 1,
                            SignatureDate = DateTime.Now,
                            SignatureType = AccountsBuilder.Domain.SignatureType.BalanceSheet
                        }
                    }

                },

                ProfitAndLossData = new List<ProfitAndLossMessage> { StaticData.Data.GetProfitAndLossMessage(Guid.NewGuid(), DateTime.Now) },

                BalanceSheetData = new List<BalanceSheetMessage> { StaticData.Data.GetBalanceSheetMessage(Guid.NewGuid()) },

                ProfitShareData = new ProfitShareDataDto
                {
                    ProfitShares = new List<ProfitShareMessage>() { StaticData.Data.GetProfitShare(Guid.NewGuid()) }
                },

                DataScreenValue = TestData.GetDataScreenValuesMessage(),

            };

            // Act
            var result = _mapper.Map<AccountsBuilderReportingMessageDto>(unicorporatedReportingMessage);

            // Assert
            Assert.NotNull(result);
            result.TenantId.ShouldBe(unicorporatedReportingMessage.TenantId);
            result.ClientId.ShouldBe(unicorporatedReportingMessage.ClientId);
            result.PeriodId.ShouldBe(unicorporatedReportingMessage.PeriodId);
            result.ReportType.ShouldBe(ReportStandardType.UNINCORPORATED);
            result.WatermarkText.ShouldBe(unicorporatedReportingMessage.WatermarkText);

            AssertEntitySetup(result.EntitySetup, unicorporatedReportingMessage.EntitySetup);
            AssertPracticeDetails(result.PracticeDetails, unicorporatedReportingMessage.PracticeDetails);
            AssertClientDocumentMessage(result.ClientData, unicorporatedReportingMessage.ClientData);
            AssertClientInvolvements(result.Involvements.First(), unicorporatedReportingMessage.Involvements.First());

            var reportingPeriod = result.ReportingPeriods.First();
            AssertReportingPeriodDetails(unicorporatedReportingMessage.ReportingPeriods.First(), reportingPeriod);

            var signature = result.Signatures;
            signature.AccountantSigningDate.ShouldBe(unicorporatedReportingMessage.Signatures.AccountantSigningDate);
            signature.IncludeAccountantsReport.ShouldBe(unicorporatedReportingMessage.Signatures.IncludeAccountantsReport);
            var signatures = signature.Signatures;

            AssertSignatureDetails(unicorporatedReportingMessage.Signatures.Signatures.First(), signatures.First());

            var profitAndLoss = result.ProfitAndLossData.First();
            AssertProfitAndLossDetails(unicorporatedReportingMessage.ProfitAndLossData.First(), profitAndLoss);

            var balanceSheet = result.BalanceSheetData.First();
            AssertBalanceSheetDetails(unicorporatedReportingMessage.BalanceSheetData.First(), balanceSheet);

            var profitShare = result.ProfitShareData.ProfitShares.First();
            AssertProfitShareDetails(unicorporatedReportingMessage.ProfitShareData.ProfitShares.First(), profitShare);


            AssertDataScreenValues(unicorporatedReportingMessage.DataScreenValue, result.DataScreenValue);
        }

        private static void AssertNotesDetails(NotesResponseDataMessage source, NotesResponseDataMessage destination)
        {
            source.AverageNumberOfEmployees.ShouldBe(destination.AverageNumberOfEmployees);
            source.MembersLiabilityText.ShouldBe(destination.MembersLiabilityText);

            source.AdditionalNote1.NoteText.ShouldBe(destination.AdditionalNote1.NoteText);
            source.AdditionalNote1.NoteTitle.ShouldBe(destination.AdditionalNote1.NoteTitle);
            source.AdditionalNote2.NoteText.ShouldBe(destination.AdditionalNote2.NoteText);
            source.AdditionalNote2.NoteTitle.ShouldBe(destination.AdditionalNote2.NoteTitle);
        }

        private static void AssertProfitShareDetails(ProfitShareMessage source, ProfitShareMessage destination)
        {
            source.AccountPeriodId.ShouldBe(destination.AccountPeriodId);
            source.CumulativeAmount.ShouldBe(destination.CumulativeAmount);
            source.InvolvementId.ShouldBe(destination.InvolvementId);
        }

        private static void AssertBalanceSheetDetails(BalanceSheetMessage source, BalanceSheetMessage destination)
        {
            source.AccrualsAndDeferredIncome.Value.ShouldBe(destination.AccrualsAndDeferredIncome.Value);
            source.CalledUpShareCapital.Value.ShouldBe(destination.CalledUpShareCapital.Value);
            source.FixedAssets.Value.ShouldBe(destination.FixedAssets.Value);
            source.CurrentAssets.Value.ShouldBe(destination.CurrentAssets.Value);
        }

        private static void AssertProfitAndLossDetails(ProfitAndLossMessage source, ProfitAndLossMessage destination)
        {
            destination.PeriodId.ShouldBe(source.PeriodId);
            destination.Period.ShouldBe(source.Period);
            destination.DepreciationAndOtherAmountsWrittenOffAssets.Value.ShouldBe(source.DepreciationAndOtherAmountsWrittenOffAssets.Value);
            destination.CostOfRawMaterialsAndConsumables.Value.ShouldBe(source.CostOfRawMaterialsAndConsumables.Value);
            destination.OtherCharges.Value.ShouldBe(source.OtherCharges.Value);
            destination.OtherIncome.Value.ShouldBe(source.OtherIncome.Value);
            destination.StaffCosts.Value.ShouldBe(source.StaffCosts.Value);
            destination.Tax.Value.ShouldBe(source.Tax.Value);
            destination.Turnover.Value.ShouldBe(source.Turnover.Value);
        }

        private static void AssertSignatureDetails(ReportingSignatureDetailDto source, AccountsBuilder.Reporting.Application.Dto.SignatureDetailDto destination)
        {
            destination.SignatoryFirstName.ShouldBe(source.SignatoryFirstName);
            destination.SignatorySurname.ShouldBe(source.SignatorySurname);
            destination.SignatoryTitle.ShouldBe(source.SignatoryTitle);
            destination.SignatureDate.ShouldBe(source.SignatureDate);
            destination.DisplayOrder.ShouldBe(source.DisplayOrder);
            destination.SignatureType.ToString().ShouldBe(source.SignatureType.ToString());
        }

        private static void AssertReportingPeriodDetails(ReportingPeriodDto source, AccountsBuilder.Reporting.Application.Dto.PeriodDto destination)
        {
            destination.Id.ShouldBe(source.Id);
            destination.StartDate.ShouldBe(source.StartDate);
            destination.EndDate.ShouldBe(source.EndDate);
        }

        private static EntitySetupDto GetEntitySetup()
        {
            return new EntitySetupDto
            {
                EntitySize = "entitySize",
                IndependentReviewType = "independentReviewType",
                Terminology = "terminology",
                ChoiceOfStatement = "choiceOfStatement",
                DormantStatus = "dormantStatus",
                ReportingStandard = "reportingStandard",
                TradingStatus = "tradingStatus",
                CIC34Report = "Simple",
                CharitySize = "charitySize"
            };
        }

        private static PracticeDetailsDto GetPracticeDetails()
        {
            return new PracticeDetailsDto
            {
                ReferredType = 1,
                SupervisingBody = 2,
                Name = "name",
                AddressLine1 = "AddressLine1",
                AddressLine2 = "AddressLine2",
                AddressLine3 = "AddressLine3",
                AddressTown = "AddressTown",
                AddressCounty = "AddressCounty",
                AddressPostcode = "AddressPostcode",
                AddressCountry = "AddressCountry"
            };
        }

        private static ClientDocumentMessageDto GetClientData()
        {
            return new ClientDocumentMessageDto
            {
                CompanyName = "CompanyName",
                CompanyType = "Limided",
                CompanySubType = "CompanySubType",
                CompanyRegistrationNumber = "CompanyRegistrationNumber",
                Addresses = new ClientAddressDto
                {
                    MainAddress = new AddressDto
                    {
                        Line1 = "AddressLine1",
                        Line2 = "AddressLine2",
                        Town = "AddressTown",
                        County = "AddressCounty",
                        PostCode = "AddressPostcode",
                    },
                    RegisteredAddress = new AddressDto
                    {
                        Line1 = "AddressLine1",
                        Line2 = "AddressLine2",
                        Town = "AddressTown",
                        County = "AddressCounty",
                        PostCode = "AddressPostcode",
                    }
                }
            };
        }

        private static ClientInvolvementDto GetClientInvolvement()
        {
            return new ClientInvolvementDto
            {
                InvolvedClientType = "InvolvedClientType",
                InvolvementClientName = "InvolvementClientName",
                InvolvementType = "InvolvementType",
                InvolvementFirstName = "InvolvementFirstName",
                InvolvementSurname = "InvolvementSurname",
                InvolvementTitle = "InvolvementTitle",
                StartDate = DateTime.Now,
                PdoCode = 1,
                IsDeleted = false,
            };
        }

        private void AssertEntitySetup(AccountsBuilder.Reporting.Application.Dto.EntitySetupDto destination, EntitySetupDto source)
        {
            destination.EntitySize.ShouldBe(source.EntitySize);
            destination.IndependentReviewType.ShouldBe(source.IndependentReviewType);
            destination.Terminology.ShouldBe(source.Terminology);
            destination.ChoiceOfStatement.ShouldBe(source.ChoiceOfStatement);
            destination.DormantStatus.ShouldBe(source.DormantStatus);
            destination.ReportingStandard.ShouldBe(source.ReportingStandard);
            destination.TradingStatus.ShouldBe(source.TradingStatus);
            destination.CharitySize.ShouldBe(source.CharitySize);
        }

        private void AssertPracticeDetails(AccountsBuilder.Reporting.Application.Dto.PracticeDetailsDto destination, PracticeDetailsDto source)
        {
            destination.ReferredType.ShouldBe(source.ReferredType);
            destination.SupervisingBody.ShouldBe(source.SupervisingBody);
            destination.Name.ShouldBe(source.Name);
            destination.AddressLine1.ShouldBe(source.AddressLine1);
            destination.AddressLine2.ShouldBe(source.AddressLine2);
            destination.AddressLine3.ShouldBe(source.AddressLine3);
            destination.AddressTown.ShouldBe(source.AddressTown);
            destination.AddressCounty.ShouldBe(source.AddressCounty);
            destination.AddressPostcode.ShouldBe(source.AddressPostcode);
            destination.AddressCountry.ShouldBe(source.AddressCountry);
        }

        private void AssertClientDocumentMessage(AccountsBuilder.Reporting.Application.Dto.ClientDto destination, ClientDocumentMessageDto source)
        {
            destination.CompanyName.ShouldBe(source.CompanyName);
            destination.CompanyType.ShouldBe(source.CompanyType);
            destination.CompanySubType.ShouldBe(source.CompanySubType);
            destination.CompanyRegistrationNumber.ShouldBe(source.CompanyRegistrationNumber);
            AssertClientAddress(destination.Addresses, source.Addresses);
        }

        private void AssertClientAddress(AccountsBuilder.Reporting.Application.Dto.ClientAddressDto destination, ClientAddressDto source)
        {
            destination.MainAddress.Line1.ShouldBe(source.MainAddress.Line1);
            destination.MainAddress.Line2.ShouldBe(source.MainAddress.Line2);
            destination.MainAddress.Town.ShouldBe(source.MainAddress.Town);
            destination.MainAddress.County.ShouldBe(source.MainAddress.County);
            destination.MainAddress.PostCode.ShouldBe(source.MainAddress.PostCode);

            destination.RegisteredAddress.Line1.ShouldBe(source.RegisteredAddress.Line1);
            destination.RegisteredAddress.Line2.ShouldBe(source.RegisteredAddress.Line2);
            destination.RegisteredAddress.Town.ShouldBe(source.RegisteredAddress.Town);
            destination.RegisteredAddress.County.ShouldBe(source.RegisteredAddress.County);
            destination.RegisteredAddress.PostCode.ShouldBe(source.RegisteredAddress.PostCode);
        }

        private void AssertClientInvolvements(AccountsBuilder.Reporting.Application.Dto.InvolvementDto destination, ClientInvolvementDto source)
        {
            destination.InvolvementClientGuid.ShouldBe(source.InvolvementClientGuid);
            destination.InvolvementClientName.ShouldBe(source.InvolvementClientName);
            destination.InvolvementFirstName.ShouldBe(source.InvolvementFirstName);
            destination.InvolvementId.ShouldBe(source.InvolvementId);
            destination.InvolvementSurname.ShouldBe(source.InvolvementSurname);
            destination.InvolvementTitle.ShouldBe(source.InvolvementTitle);
            destination.InvolvementType.ShouldBe(source.InvolvementType);
            destination.IsDeleted.ShouldBe(source.IsDeleted);
            destination.PdoCode.ShouldBe(source.PdoCode);
            destination.StartDate.ShouldBe(source.StartDate);
        }

        private void AssertDataScreenValues(DataScreenValueMessage destination, DataScreenValueMessage source)
        {
            destination.ClientId.ShouldBe(source.ClientId);
            destination.PeriodId.ShouldBe(source.PeriodId);
            destination.TenantId.ShouldBe(source.TenantId);
            destination.CurrentPeriod.Count.ShouldBe(source.CurrentPeriod.Count);
            destination.CurrentPeriod[0].ScreenFields.Count.ShouldBe(source.CurrentPeriod[0].ScreenFields.Count);
            destination.CurrentPeriod[0].ReportMappingTable.ShouldBe(source.CurrentPeriod[0].ReportMappingTable);
            destination.CurrentPeriod[0].ScreenId.ShouldBe(source.CurrentPeriod[0].ScreenId);
        }
    }
}
