﻿using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper;
using AccountsProduction.AccountsBuilder.Reporting.Domain;
using AccountsProduction.AccountsBuilder.Reporting.Domain.ProfitAndLoss;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.AutoMapper.Mappers
{
    public class ProfitAndLossMapperTests
    {
        private readonly IMapper _mapper;
        private const string Message = "This field name is stored in DB as it is, do not modify!";
        private readonly int _defaultAmount = 0;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = TestHelpers.Guids.GuidTwo;

        public ProfitAndLossMapperTests()
        {
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
        }

        [Fact]
        public void Should_contain_correct_properties()
        {
            var profitAndLoss = new ProfitAndLossMessage();

            var periodIdProperty = profitAndLoss.GetType().GetProperties().SingleOrDefault(s => s.Name == "PeriodId");
            var profitAndLossDtoProperties = profitAndLoss
                .GetType().GetProperties()
                .Where(s => s.PropertyType == typeof(FinancialDataCategoryMessage))
                .Select(x => x).ToList();

            periodIdProperty.ShouldNotBeNull("This field is mandatory, do not modify!");
            profitAndLossDtoProperties.Count.ShouldBe(36);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "Turnover", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "OtherIncome", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "CostOfRawMaterialsAndConsumables", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "StaffCosts", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "DepreciationAndOtherAmountsWrittenOffAssets", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "OtherCharges", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "Sales", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "CostOfSales", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "Expenses", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "FinanceCosts", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "PartnerAppropriations", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "Depreciation", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "Tax", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "GrossProfitLoss", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "DistributionExpenses", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "AdministrativeExpenses", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "OtherOperatingIncome", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "GainLossOnRevaluation1", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "OperatingProfitLoss", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "ExceptionalItems", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "IncomeFromSharesInGroupUndertakings", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "IncomeFromInterestInAssociatedUndertakings", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "IncomeFromOtherParticipatingInterests", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "IncomeFromFixedAssetInvestments", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "InterestReceivableAndSimilarIncome", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "OtherFinanceIncome", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "AmountsWrittenOffInvestments", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "GainLossOnRevaluation2", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "InterestPayableAndSimilarExpenses", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "OtherFinanceCosts", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "ProfitLossOnOrdinaryActivitiesBeforeTaxation", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "Taxation", Message);
            profitAndLossDtoProperties.ShouldContain(x => x.Name == "ProfitLossForTheFinancialYear", Message);
        }

        [Fact]
        public void Should_map_profit_and_loss_for_companies_act()
        {
            var profitAndLoss = new ProfitAndLossCompaniesAct
            {
                ClientId = _clientId,
                AccountPeriodId = _periodId,
                ReportingPeriod = new ReportingPeriod()
            };
            var profitAndLossDto = new ProfitAndLossMessage
            {
                PeriodId = Guid.NewGuid(),
                Period = DateTime.Now,
                CostOfRawMaterialsAndConsumables = new FinancialDataCategoryMessage { Value = "10" },
                DepreciationAndOtherAmountsWrittenOffAssets = new FinancialDataCategoryMessage { Value = "20.0" },
                OtherCharges = new FinancialDataCategoryMessage { Value = "30" },
                OtherIncome = new FinancialDataCategoryMessage { Value = "40" },
                StaffCosts = new FinancialDataCategoryMessage { Value = "50" },
                Tax = new FinancialDataCategoryMessage { Value = "60" },
                Turnover = new FinancialDataCategoryMessage { Value = "70" }
            };

            _mapper.Map(profitAndLossDto, profitAndLoss);

            profitAndLoss.ClientId.ShouldBe(_clientId);
            profitAndLoss.AccountPeriodId.ShouldBe(_periodId);
            profitAndLoss.ReportingPeriod.ShouldNotBeNull();

            profitAndLoss.Turnover.ShouldBe(70m);
            profitAndLoss.CostOfSales.ShouldBe(_defaultAmount);
            profitAndLoss.GrossProfitLoss.ShouldBe(_defaultAmount);
            profitAndLoss.DistributionExpenses.ShouldBe(_defaultAmount);
            profitAndLoss.AdministrativeExpenses.ShouldBe(_defaultAmount);
            profitAndLoss.OtherOperatingIncome.ShouldBe(_defaultAmount);
            profitAndLoss.GainLossOnRevaluation1.ShouldBe(_defaultAmount);
            profitAndLoss.OperatingProfitLoss.ShouldBe(_defaultAmount);
            profitAndLoss.ExceptionalItems.ShouldBe(_defaultAmount);
            profitAndLoss.IncomeFromSharesInGroupUndertakings.ShouldBe(_defaultAmount);
            profitAndLoss.IncomeFromInterestInAssociatedUndertakings.ShouldBe(_defaultAmount);
            profitAndLoss.IncomeFromOtherParticipatingInterests.ShouldBe(_defaultAmount);
            profitAndLoss.IncomeFromFixedAssetInvestments.ShouldBe(_defaultAmount);
            profitAndLoss.InterestReceivableAndSimilarIncome.ShouldBe(_defaultAmount);
            profitAndLoss.OtherFinanceIncome.ShouldBe(_defaultAmount);
            profitAndLoss.AmountsWrittenOffInvestments.ShouldBe(_defaultAmount);
            profitAndLoss.GainLossOnRevaluation2.ShouldBe(_defaultAmount);
            profitAndLoss.InterestPayableAndSimilarExpenses.ShouldBe(_defaultAmount);
            profitAndLoss.OtherFinanceCosts.ShouldBe(_defaultAmount);
            profitAndLoss.ProfitLossOnOrdinaryActivitiesBeforeTaxation.ShouldBe(_defaultAmount);
            profitAndLoss.Taxation.ShouldBe(_defaultAmount);
            profitAndLoss.ProfitLossForTheFinancialYear.ShouldBe(_defaultAmount);
            profitAndLoss.ProfitLossAvailableForDiscretionaryDivision.ShouldBe(_defaultAmount);
            profitAndLoss.NonControllingInterests.ShouldBe(_defaultAmount);
            profitAndLoss.MembersRemunerationAsExpense.ShouldBe(_defaultAmount);
        }

        [Fact]
        public void Should_map_profit_and_loss_for_companies_act_when_all_lineItem_values_are_null()
        {
            var profitAndLoss = new ProfitAndLossCompaniesAct
            {
                ClientId = _clientId,
                AccountPeriodId = _periodId,
                ReportingPeriod = new ReportingPeriod()
            };
            var profitAndLossDto = new ProfitAndLossMessage
            {
                PeriodId = Guid.NewGuid(),
                Period = DateTime.Now
            };

            _mapper.Map(profitAndLossDto, profitAndLoss);

            profitAndLoss.ClientId.ShouldBe(_clientId);
            profitAndLoss.AccountPeriodId.ShouldBe(_periodId);
            profitAndLoss.ReportingPeriod.ShouldNotBeNull();
            profitAndLoss.Turnover.ShouldBe(_defaultAmount);
            profitAndLoss.CostOfSales.ShouldBe(_defaultAmount);
            profitAndLoss.GrossProfitLoss.ShouldBe(_defaultAmount);
            profitAndLoss.DistributionExpenses.ShouldBe(_defaultAmount);
            profitAndLoss.AdministrativeExpenses.ShouldBe(_defaultAmount);
            profitAndLoss.OtherOperatingIncome.ShouldBe(_defaultAmount);
            profitAndLoss.GainLossOnRevaluation1.ShouldBe(_defaultAmount);
            profitAndLoss.OperatingProfitLoss.ShouldBe(_defaultAmount);
            profitAndLoss.ExceptionalItems.ShouldBe(_defaultAmount);
            profitAndLoss.IncomeFromSharesInGroupUndertakings.ShouldBe(_defaultAmount);
            profitAndLoss.IncomeFromInterestInAssociatedUndertakings.ShouldBe(_defaultAmount);
            profitAndLoss.IncomeFromOtherParticipatingInterests.ShouldBe(_defaultAmount);
            profitAndLoss.IncomeFromFixedAssetInvestments.ShouldBe(_defaultAmount);
            profitAndLoss.InterestReceivableAndSimilarIncome.ShouldBe(_defaultAmount);
            profitAndLoss.OtherFinanceIncome.ShouldBe(_defaultAmount);
            profitAndLoss.AmountsWrittenOffInvestments.ShouldBe(_defaultAmount);
            profitAndLoss.GainLossOnRevaluation2.ShouldBe(_defaultAmount);
            profitAndLoss.InterestPayableAndSimilarExpenses.ShouldBe(_defaultAmount);
            profitAndLoss.OtherFinanceCosts.ShouldBe(_defaultAmount);
            profitAndLoss.ProfitLossOnOrdinaryActivitiesBeforeTaxation.ShouldBe(_defaultAmount);
            profitAndLoss.Taxation.ShouldBe(_defaultAmount);
            profitAndLoss.ProfitLossForTheFinancialYear.ShouldBe(_defaultAmount);
            profitAndLoss.ProfitLossAvailableForDiscretionaryDivision.ShouldBe(_defaultAmount);
            profitAndLoss.NonControllingInterests.ShouldBe(_defaultAmount);
            profitAndLoss.MembersRemunerationAsExpense.ShouldBe(_defaultAmount);
        }

        [Fact]
        public void Should_map_profit_and_loss_for_frs105()
        {
            var profitAndLoss = new ProfitAndLossFRS105
            {
                ClientId = _clientId,
                AccountPeriodId = _periodId,
                ReportingPeriod = new ReportingPeriod()
            };
            var profitAndLossDto = new ProfitAndLossMessage
            {
                PeriodId = Guid.NewGuid(),
                Period = DateTime.Now,
                CostOfRawMaterialsAndConsumables = new FinancialDataCategoryMessage { Value = "10.1" },
                DepreciationAndOtherAmountsWrittenOffAssets = new FinancialDataCategoryMessage { Value = "20.0" },
                OtherCharges = new FinancialDataCategoryMessage { Value = "30" },
                OtherIncome = new FinancialDataCategoryMessage { Value = "40" },
                StaffCosts = new FinancialDataCategoryMessage { Value = "50" },
                Tax = new FinancialDataCategoryMessage { Value = "60" },
                Turnover = new FinancialDataCategoryMessage { Value = "70" },
                ProfitLossAvailableForDiscretionaryDivision = new FinancialDataCategoryMessage { Value = "80" },
                MembersRemunerationAsExpense = new FinancialDataCategoryMessage { Value = "90" }
            };

            _mapper.Map(profitAndLossDto, profitAndLoss);

            profitAndLoss.ClientId.ShouldBe(_clientId);
            profitAndLoss.AccountPeriodId.ShouldBe(_periodId);
            profitAndLoss.ReportingPeriod.ShouldNotBeNull();


            profitAndLoss.DepreciationAndOtherAmountsWrittenOffAssets.ShouldBe(20.0m);
            profitAndLoss.CostOfRawMaterialsAndConsumables.ShouldBe(10.1m);
            profitAndLoss.OtherCharges.ShouldBe(30m);
            profitAndLoss.OtherIncome.ShouldBe(40m);
            profitAndLoss.StaffCosts.ShouldBe(50m);
            profitAndLoss.Tax.ShouldBe(60m);
            profitAndLoss.Turnover.ShouldBe(70m);
            profitAndLoss.ProfitLossAvailableForDiscretionaryDivision.ShouldBe(80m);
            profitAndLoss.MembersRemunerationAsExpense.ShouldBe(90m);
        }

        [Fact]
        public void Should_map_profit_and_loss_for_frs105_when_all_lineItem_values_are_null()
        {
            var profitAndLoss = new ProfitAndLossFRS105
            {
                ClientId = _clientId,
                AccountPeriodId = _periodId,
                ReportingPeriod = new ReportingPeriod()
            };

            var profitAndLossDto = new ProfitAndLossMessage
            {
                PeriodId = Guid.NewGuid(),
                Period = DateTime.Now
            };

            _mapper.Map(profitAndLossDto, profitAndLoss);

            profitAndLoss.ClientId.ShouldBe(_clientId);
            profitAndLoss.AccountPeriodId.ShouldBe(_periodId);
            profitAndLoss.ReportingPeriod.ShouldNotBeNull();

            profitAndLoss.DepreciationAndOtherAmountsWrittenOffAssets.ShouldBe(_defaultAmount);
            profitAndLoss.CostOfRawMaterialsAndConsumables.ShouldBe(_defaultAmount);
            profitAndLoss.OtherCharges.ShouldBe(_defaultAmount);
            profitAndLoss.OtherIncome.ShouldBe(_defaultAmount);
            profitAndLoss.StaffCosts.ShouldBe(_defaultAmount);
            profitAndLoss.Tax.ShouldBe(_defaultAmount);
            profitAndLoss.Turnover.ShouldBe(_defaultAmount);
            profitAndLoss.ProfitLossAvailableForDiscretionaryDivision.ShouldBe(_defaultAmount);
            profitAndLoss.MembersRemunerationAsExpense.ShouldBe(_defaultAmount);
        }


        [Fact]
        public void Should_map_profit_and_loss_for_non_corp()
        {
            var clientId = Guid.NewGuid();
            var accountPeriodId = Guid.NewGuid();

            var profitAndLoss = new ProfitAndLossNonCorp
            {
                ClientId = clientId,
                AccountPeriodId = accountPeriodId,
                ReportingPeriod = new ReportingPeriod()
            };

            var profitAndLossDto = new ProfitAndLossMessage
            {
                PeriodId = Guid.NewGuid(),
                Period = DateTime.Now,
                CostOfRawMaterialsAndConsumables = new FinancialDataCategoryMessage { Value = "10.1" },
                DepreciationAndOtherAmountsWrittenOffAssets = new FinancialDataCategoryMessage { Value = "20.0" },
                OtherCharges = new FinancialDataCategoryMessage { Value = "30" },
                OtherIncome = new FinancialDataCategoryMessage { Value = "40" },
                StaffCosts = new FinancialDataCategoryMessage { Value = "50" },
                Tax = new FinancialDataCategoryMessage { Value = "60" },
                Turnover = new FinancialDataCategoryMessage { Value = "70" }
            };

            _mapper.Map(profitAndLossDto, profitAndLoss);

            profitAndLoss.ClientId.ShouldBe(clientId);
            profitAndLoss.AccountPeriodId.ShouldBe(accountPeriodId);
            profitAndLoss.ReportingPeriod.ShouldNotBeNull();

            profitAndLoss.Sales.ShouldBe(_defaultAmount);
            profitAndLoss.CostOfSales.ShouldBe(_defaultAmount);
            profitAndLoss.OtherIncome.ShouldBe(40m);
            profitAndLoss.Expenses.ShouldBe(_defaultAmount);
            profitAndLoss.FinanceCosts.ShouldBe(_defaultAmount);
            profitAndLoss.PartnerAppropriations.ShouldBe(_defaultAmount);
            profitAndLoss.Depreciation.ShouldBe(_defaultAmount);
        }

        [Fact]
        public void Should_map_profit_and_loss_for_non_corp_when_all_lineItem_values_are_null()
        {
            var clientId = Guid.NewGuid();
            var accountPeriodId = Guid.NewGuid();

            var profitAndLoss = new ProfitAndLossNonCorp
            {
                ClientId = clientId,
                AccountPeriodId = accountPeriodId,
                ReportingPeriod = new ReportingPeriod()
            };

            var profitAndLossDto = new ProfitAndLossMessage
            {
                PeriodId = Guid.NewGuid(),
                Period = DateTime.Now
            };

            _mapper.Map(profitAndLossDto, profitAndLoss);

            profitAndLoss.ClientId.ShouldBe(clientId);
            profitAndLoss.AccountPeriodId.ShouldBe(accountPeriodId);
            profitAndLoss.ReportingPeriod.ShouldNotBeNull();

            profitAndLoss.Sales.ShouldBe(_defaultAmount);
            profitAndLoss.CostOfSales.ShouldBe(_defaultAmount);
            profitAndLoss.OtherIncome.ShouldBe(_defaultAmount);
            profitAndLoss.Expenses.ShouldBe(_defaultAmount);
            profitAndLoss.FinanceCosts.ShouldBe(_defaultAmount);
            profitAndLoss.PartnerAppropriations.ShouldBe(_defaultAmount);
            profitAndLoss.Depreciation.ShouldBe(_defaultAmount);
        }

    }
}
