﻿using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using AccountsProduction.AccountsBuilder.UnitTests.Reporting.StaticData;
using AutoMapper;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.AutoMapper.Mappers
{
    public class NoteOtherMapperTests
    {
        private readonly IMapper _mapper;

        public NoteOtherMapperTests()
        {
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
        }

        [Fact]
        public void Should_map_AccountingPoliciesResponseDataMessage_object_to_note_accounting_policies_list()
        {
            var notesOther = Data.GetNotes(ReportType.FRS105);

            var result = _mapper.Map<List<NoteOther>>(notesOther);

            result.Count.ShouldBe(8);

            result.SingleOrDefault(x => x.NoteType == "OffBalanceSheetArrangements" && x.NoteText == "OffBalanceSheetArrangements").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "AdvancesCreditAndGuaranteesGrantedToDirectors" && x.NoteText == "AdvancesCreditAndGuaranteesGrantedToDirectors").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "GuaranteesAndOtherFinancialCommitments" && x.NoteText == "GuaranteesAndOtherFinancialCommitments").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "RelatedPartyTransactions" && x.NoteText == "RelatedPartyTransactions").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ControllingPartyNote" && x.NoteText == "ControllingPartyNote").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "MembersLiabilityText" && x.NoteText == "MembersLiabilityTextText").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "AdditionalNote1" && x.NoteText == "NoteText1").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "AdditionalNote2" && x.NoteText == "NoteText2").ShouldNotBeNull();

        }
    }
}
