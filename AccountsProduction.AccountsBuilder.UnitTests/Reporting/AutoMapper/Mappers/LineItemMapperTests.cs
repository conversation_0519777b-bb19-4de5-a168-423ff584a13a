﻿using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper;
using AccountsProduction.AccountsBuilder.Reporting.Domain;
using AccountsProduction.AccountsBuilder.UnitTests.Reporting.StaticData;
using AutoMapper;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.AutoMapper.Mappers
{
    public class LineItemMapperTests
    {
        private readonly IMapper _mapper;

        public LineItemMapperTests()
        {
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
        }

        [Fact]
        public void Should_map_BalanceSheet_to_LineItem_list()
        {
            var result = _mapper.Map<List<LineItem>>(Data.GetBalanceSheetData());

            result.Count.ShouldBe(2);
            result.SingleOrDefault(x =>
                x.AccountPeriodId == TestHelpers.Guids.GuidOne &&
                x.Category == "CapitalAndReserves" &&
                x.AccountCode == "1" &&
                x.AccountDescription == "description2" &&
                x.CurrentValue == 22 &&
                x.SubAccountCode == "33" &&
                x.InvolvementId == 1 &&
                x.SectorId == TestHelpers.Guids.GuidOne
            ).ShouldNotBeNull();
            result.SingleOrDefault(x =>
                x.AccountPeriodId == TestHelpers.Guids.GuidTwo &&
                x.Category == "CapitalAndReserves" &&
                x.AccountCode == "1" &&
                x.AccountDescription == "description2" &&
                x.CurrentValue == 22 &&
                x.SubAccountCode == "33" &&
                x.InvolvementId == 1 &&
                x.SectorId == TestHelpers.Guids.GuidOne
            ).ShouldNotBeNull();
        }

        [Fact]
        public void Should_map_ProfitAndLoss_to_LineItem_list()
        {
            var result = _mapper.Map<List<LineItem>>(Data.GetProfitAndLossData());

            result.Count.ShouldBe(2);
            result.SingleOrDefault(x => x.AccountPeriodId == TestHelpers.Guids.GuidOne).ShouldNotBeNull();
            result.SingleOrDefault(x => x.AccountPeriodId == TestHelpers.Guids.GuidTwo).ShouldNotBeNull();
            result.All(x => x.Category == "Tax").ShouldBe(true);
            result.All(x => x.AccountCode == "1").ShouldBe(true);
            result.All(x => x.AccountDescription == "description").ShouldBe(true);
            result.All(x => x.CurrentValue == 2).ShouldBe(true);
            result.All(x => x.SubAccountCode == "3").ShouldBe(true);
            result.All(x => x.InvolvementId == 2).ShouldBe(true);
            result.All(x => x.SectorId == TestHelpers.Guids.GuidOne).ShouldBe(true);
        }
    }
}
