﻿using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper;
using AccountsProduction.AccountsBuilder.Reporting.Application.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Domain;
using AutoMapper;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.AutoMapper.Mappers
{
    public class TenantMapperTests
    {
        private readonly IMapper _mapper;

        public TenantMapperTests()
        {
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
        }

        [Fact]
        public void Should_map_practiceDetailsDto_to_tenant()
        {
            var practiceDetailsDto = new PracticeDetailsDto
            {
                ReferredType = 1,
                SupervisingBody = 2,
                Name = "name",
                AddressLine1 = "AddressLine1",
                AddressLine2 = "AddressLine2",
                AddressLine3 = "AddressLine3",
                AddressTown = "AddressTown",
                AddressCounty = "AddressCounty",
                AddressPostcode = "AddressPostcode",
                AddressCountry = "AddressCountry"
            };

            var result = _mapper.Map<Tenant>(practiceDetailsDto);

            Assert.Equal(result.ReferredType, practiceDetailsDto.ReferredType);
            Assert.Equal(result.SupervisingBody, practiceDetailsDto.SupervisingBody.ToString());
            Assert.Equal(result.Name, practiceDetailsDto.Name);
            Assert.Equal(result.AddressLine1, practiceDetailsDto.AddressLine1);
            Assert.Equal(result.AddressLine2, practiceDetailsDto.AddressLine2);
            Assert.Equal(result.AddressLine3, practiceDetailsDto.AddressLine3);
            Assert.Equal(result.AddressTown, practiceDetailsDto.AddressTown);
            Assert.Equal(result.AddressCounty, practiceDetailsDto.AddressCounty);
            Assert.Equal(result.AddressPostcode, practiceDetailsDto.AddressPostcode);
            Assert.Equal(result.AddressCountry, practiceDetailsDto.AddressCountry);
        }
    }
}