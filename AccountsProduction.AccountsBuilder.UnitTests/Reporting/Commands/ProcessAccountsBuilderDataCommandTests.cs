﻿using AccountsProduction.AccountsBuilder.Application.Reporting.Commands.SaveAndUpdateStrategies;
using AccountsProduction.AccountsBuilder.Application.Reporting.Common;
using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper;
using AccountsProduction.AccountsBuilder.Reporting.Application.Commands;
using AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies;
using AccountsProduction.AccountsBuilder.Reporting.Domain;
using AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence;
using AccountsProduction.AccountsBuilder.UnitTests.Reporting.StaticData;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.DataScreenValue;
using Iris.AccountsProduction.Common.Toolkit.Exceptions;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.Commands
{
    public class ProcessAccountsBuilderDataCommandTests
    {
        private readonly ILogger<ProcessAccountsBuilderDataCommandHandler> _logger;
        private readonly AccountsProductionReportingDbContext _dbContext;
        private readonly Mock<IMediator> _mediator;
        private readonly IEnumerable<ISaveUpdateStrategy> _saveUpdateStrategies;
        private readonly Mock<UserContext> _mockUserContext;

        readonly Guid _tenantId = Data.TenantId;
        readonly Guid _clientId = Data.ClientId;
        private readonly IMapper _mapper;

        public ProcessAccountsBuilderDataCommandTests()
        {
            _mockUserContext = new Mock<UserContext>();
            _mockUserContext.Setup(context => context.TenantId).Returns(_tenantId.ToString());
            _mockUserContext.Setup(context => context.UserId).Returns("UserId");

            _logger = Mock.Of<ILogger<ProcessAccountsBuilderDataCommandHandler>>();
            _mediator = new Mock<IMediator>();

            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();

            var options = new DbContextOptionsBuilder<AccountsProductionReportingDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _dbContext = new AccountsProductionReportingDbContext(options);

            _dbContext.Database.EnsureCreated();

            _saveUpdateStrategies = new List<ISaveUpdateStrategy>
            {
               new FRS105Strategy(_mediator.Object, new Mock<ILogger<FRS105Strategy>>().Object, _dbContext, _mapper),
               new FRS1021AStrategy(_mediator.Object , new Mock<ILogger<FRS1021AStrategy>>().Object, _dbContext, _mapper),
               new FRS102Strategy(_mediator.Object, new Mock<ILogger<FRS102Strategy>>().Object, _dbContext, _mapper),
               new UnincorporatedStrategy(_mediator.Object, new Mock<ILogger<UnincorporatedStrategy>>().Object, _dbContext, _mapper),
               new CharityStrategy(_mediator.Object, new Mock<ILogger<CharityStrategy>>().Object, _dbContext, _mapper),
               new IFRSStrategy(_mediator.Object, new Mock<ILogger<IFRSStrategy>>().Object, _dbContext, _mapper)
            };
        }

        [Fact]
        public async Task Should_return_unit_when_data_is_null()
        {
            var command = new ProcessAccountsBuilderDataCommand
            {
                Message = null
            };

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            var result = await handler.Handle(command, new CancellationToken());

            Assert.Equal(Unit.Value, result);
        }

        [Fact]
        public async Task Should_throw_invalid_tenant_exception_when_tenant_id_does_not_match()
        {
            var command = new ProcessAccountsBuilderDataCommand
            {
                Message = new AccountsBuilderReportingMessageDto
                {
                    TenantId = Guid.NewGuid(),
                    ClientId = _clientId,
                    PeriodId = Guid.NewGuid(),
                    ReportType = "FRS105"
                }
            };

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            await Assert.ThrowsAsync<InvalidTenantException>(() => handler.Handle(command, new CancellationToken()));
        }

        [Fact]
        public async Task Should_throw_bad_request_exception_when_report_type_is_invalid()
        {
            var command = new ProcessAccountsBuilderDataCommand
            {
                Message = new AccountsBuilderReportingMessageDto
                {
                    TenantId = _tenantId,
                    ClientId = _clientId,
                    PeriodId = Guid.NewGuid(),
                    ReportType = "InvalidReportType"
                }
            };

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            await Assert.ThrowsAsync<BadRequestException>(() => handler.Handle(command, new CancellationToken()));
        }

        [Fact]
        public async Task Should_fetch_existing_reportingperiod_from_database()
        {
            // Arrange
            var reportingPeriodId = Guid.NewGuid();
            var reportingPeriod = new ReportingPeriod
            {
                ClientId = _clientId,
                AccountPeriodId = reportingPeriodId,
                ReportingPeriodStartDate = new DateTime(2020, 1, 1),
                ReportingPeriodEndDate = new DateTime(2021, 1, 1),
                TenantAccountantsReportSignatureDate = null,
                WatermarkText = "Trial",
                ReviseType = "SupplementaryNote"
            };

            _dbContext.ReportingPeriods.Add(reportingPeriod);
            _dbContext.SaveChanges();

            var command = new ProcessAccountsBuilderDataCommand
            {
                Message = new AccountsBuilderReportingMessageDto
                {
                    TenantId = _tenantId,
                    ClientId = _clientId,
                    PeriodId = reportingPeriodId,
                    ReportType = "FRS105"
                }
            };

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            // Act
            await handler.Handle(command, new CancellationToken());

            // Assert
            var fetchedReportingPeriod = _dbContext.ReportingPeriods.FirstOrDefault(rp => rp.AccountPeriodId == reportingPeriodId);
            Assert.NotNull(fetchedReportingPeriod);
            Assert.Equal(reportingPeriodId, fetchedReportingPeriod.AccountPeriodId);
            Assert.Equal("SupplementaryNote", fetchedReportingPeriod.ReviseType);
        }

        [Fact]
        public async Task Should_update_database_when_report_type_is_FRS105()
        {
            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsBuilder.Reporting.Application.Common.ReportType.FRS105);

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            var result = await handler.Handle(command, new CancellationToken());

            Assert.Equal(Unit.Value, result);

            // Assert
            AssertTenant(command.Message.TenantId);
            AssertClient(command.Message.ClientId);
            AssertMembers(command.Message.ClientId);
            AssertReportingPeriod(command.Message.PeriodId);
            AssertSignatures(command.Message.PeriodId);

            AssertNoteOther(command.Message.PeriodId);
            AssertNoteProfitAndLoss(command.Message.PeriodId);
            AssertBalanceSheetFRS105(command.Message.PeriodId);
            AssertBalanceSheetLLP(command.Message.PeriodId);
            AssertProfitAndLossFRS105(command.Message.PeriodId);

        }

        [Fact]
        public async Task Should_update_database_when_data_is_valid_and_report_type_is_FRS102()
        {
            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.FRS102);

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            command.Message.ClientData.CompanyType = "Limited";

            await handler.Handle(command, CancellationToken.None);

            AssertTenant(command.Message.TenantId);
            AssertClient(command.Message.ClientId);
            AssertMembers(command.Message.ClientId);
            AssertReportingPeriod(command.Message.PeriodId);
            AssertSignatures(command.Message.PeriodId);

            AssertNoteOther(command.Message.PeriodId);
            AssertReports(command.Message.PeriodId);
            AssertLineItems(command.Message.PeriodId);
            AssertNoteAccountingPolicies(command.Message.PeriodId);
            AssertDplSummaryCalc(command.Message.PeriodId);
            AssertMultiColumnToken(command.Message.PeriodId);
        }

        [Fact]
        public async Task Should_update_database_when_data_is_valid_and_report_type_is_FRS1021A()
        {
            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.FRS102_1A);

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            await handler.Handle(command, CancellationToken.None);

            AssertTenant(command.Message.TenantId);
            AssertClient(command.Message.ClientId);
            AssertMembers(command.Message.ClientId);
            AssertReportingPeriod(command.Message.PeriodId);
            AssertSignatures(command.Message.PeriodId);

            AssertNoteOther(command.Message.PeriodId);
            AssertLineItems(command.Message.PeriodId);
            AssertNoteAccountingPolicies(command.Message.PeriodId);
        }

        [Fact]
        public async Task Should_not_throw_exception_when_client_Address_data_and_notes_empty()
        {
            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.FRS102_1A);

            command.Message.Notes = null;
            command.Message.NoteAccountingPolicies = null;
            command.Message.ClientData.Addresses = null;

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            await Should.NotThrowAsync(async () =>
            {
                await handler.Handle(command, CancellationToken.None);
            });
        }

        [Fact]
        public async Task Should_update_database_when_data_is_valid_and_report_type_is_FRS1021A_company_type_llp()
        {
            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.FRS102_1A, CompanyTypes.LlpBusinessType);
            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            await handler.Handle(command, CancellationToken.None);

            AssertTenant(command.Message.TenantId);
            AssertClient(command.Message.ClientId);
            AssertMembers(command.Message.ClientId);
            AssertReportingPeriod(command.Message.PeriodId);
            AssertSignatures(command.Message.PeriodId);

            AssertLineItemsWithProfitshares(command.Message.PeriodId);
            AssertNoteAccountingPolicies(command.Message.PeriodId);
        }

        [Fact]
        public async Task Should_update_database_when_data_is_valid_and_report_type_is_Unincorporated()
        {
            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.UNINCORPORATED);
            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            await handler.Handle(command, CancellationToken.None);

            AssertTenant(command.Message.TenantId);
            AssertClient(command.Message.ClientId);
            AssertMembers(command.Message.ClientId);
            AssertReportingPeriod(command.Message.PeriodId);
            AssertSignatures(command.Message.PeriodId);

            AssertProfitAndLossUnincorporated(command.Message.PeriodId);
            AssertBalanceSheetNonCorp(command.Message.PeriodId);
        }

        [Fact]
        public async Task Should_update_database_when_data_is_valid_and_report_type_is_Charity()
        {
            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.CHARITY);

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            command.Message.ClientData.CompanyType = "Limited";

            await handler.Handle(command, CancellationToken.None);

            AssertTenant(command.Message.TenantId);
            AssertClient(command.Message.ClientId);
            AssertMembers(command.Message.ClientId);
            AssertReportingPeriod(command.Message.PeriodId);
            AssertSignatures(command.Message.PeriodId);

            AssertNoteOther(command.Message.PeriodId);
            AssertReports(command.Message.PeriodId);
            AssertLineItems(command.Message.PeriodId);
            AssertNoteAccountingPolicies(command.Message.PeriodId);
            AssertDplSummaryCalc(command.Message.PeriodId);
        }

        [Fact]
        public async Task Should_update_database_when_data_is_valid_and_report_type_is_IFRS()
        {
            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.IFRS);

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            command.Message.ClientData.CompanyType = "Limited";

            await handler.Handle(command, CancellationToken.None);

            AssertTenant(command.Message.TenantId);
            AssertClient(command.Message.ClientId);
            AssertMembers(command.Message.ClientId);
            AssertReportingPeriod(command.Message.PeriodId);
            AssertSignatures(command.Message.PeriodId);

            AssertNoteOther(command.Message.PeriodId);
            AssertReports(command.Message.PeriodId);
            AssertLineItems(command.Message.PeriodId);
            AssertNoteAccountingPolicies(command.Message.PeriodId);
            AssertDplSummaryCalc(command.Message.PeriodId);
        }

        [Fact]
        public async Task Should_update_tenant_if_already_exists()
        {
            _dbContext.Tenants.Add(new AccountsProduction.AccountsBuilder.Reporting.Domain.Tenant
            {
                Id = _tenantId,
                Name = "Original-TenantName",
                ReferredType = 1,
                SupervisingBody = "SupervisingBody",
                AddressLine1 = "Original-AddressLine1",
                AddressLine2 = "Original-AddressLine2",
                AddressLine3 = "Original-AddressLine3",
                AddressTown = "Original-AddressTown",
                AddressPostcode = "Original-Postcode",
                AddressCounty = "Original-AddressCounty",
                AddressCountry = "Original-AddressCountry",
            });
            _dbContext.SaveChanges();

            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.FRS105);

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            var result = await handler.Handle(command, new CancellationToken());

            // Assert
            var tenant = _dbContext.Tenants.FirstOrDefault(t => t.Id == _tenantId);
            Assert.NotNull(tenant);
            Assert.Equal(command.Message.PracticeDetails.Name, tenant.Name);
            Assert.Equal(command.Message.PracticeDetails.SupervisingBody.ToString(), tenant.SupervisingBody);
            Assert.Equal(command.Message.PracticeDetails.ReferredType, tenant.ReferredType);
            Assert.Equal(command.Message.PracticeDetails.AddressLine1, tenant.AddressLine1);
            Assert.Equal(command.Message.PracticeDetails.AddressLine2, tenant.AddressLine2);
            Assert.Equal(command.Message.PracticeDetails.AddressLine3, tenant.AddressLine3);
            Assert.Equal(command.Message.PracticeDetails.AddressTown, tenant.AddressTown);
            Assert.Equal(command.Message.PracticeDetails.AddressPostcode, tenant.AddressPostcode);
            Assert.Equal(command.Message.PracticeDetails.AddressCounty, tenant.AddressCounty);
            Assert.Equal(command.Message.PracticeDetails.AddressCountry, tenant.AddressCountry);
        }

        [Fact]
        public async Task Should_update_client_if_already_exists()
        {
            _dbContext.Tenants.Add(new AccountsProduction.AccountsBuilder.Reporting.Domain.Tenant
            {
                Id = _tenantId,
                Name = "TenantName",
            });
            _dbContext.Clients.Add(new Client
            {
                Id = _clientId,
                TenantId = _tenantId,
                CompanyName = "Original-CompanyName",
                CompanyType = "Original-CompanyType",
                CompanyRegistrationNumber = "Original-CompanyRegistrationNumber",
                CompanySubtype = "Original-CompanyType",
                CompanyCategory = "Original-CompanyCategory",
                MainAddressTown = "Original-MainAddressTown",
                MainAddressPostcode = "Original-MainAddressPostcode",
                MainAddressLine1 = "Original-MainAddressLine1",
                MainAddressLine2 = "Original-MainAddressLine2",
                MainAddressCounty = "Original-MainAddressCounty",
                RegisteredOfficeAddressTown = "Original-RegisteredOfficeAddressTown",
                RegisteredOfficeAddressPostcode = "Original-RegisteredOfficeAddressPostcode",
                RegisteredOfficeAddressLine1 = "Original-RegisteredOfficeAddressLine1",
                RegisteredOfficeAddressLine2 = "Original-RegisteredOfficeAddressLine2",
                RegisteredOfficeAddressCounty = "Original-RegisteredOfficeAddressCounty",
            });
            _dbContext.SaveChanges();

            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.FRS105);

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            var result = await handler.Handle(command, new CancellationToken());

            // Assert
            var client = _dbContext.Clients.FirstOrDefault(c => c.Id == _clientId);
            Assert.NotNull(client);
            Assert.Equal(command.Message.ClientData.CompanyName, client.CompanyName);
            Assert.Equal(command.Message.ClientData.CompanyType, client.CompanyType);
            Assert.Equal(command.Message.ClientData.CompanyRegistrationNumber, client.CompanyRegistrationNumber);
            Assert.Equal(command.Message.ClientData.CompanySubType, client.CompanySubtype);
            Assert.Equal(command.Message.ClientData.CompanyCategory, client.CompanyCategory);
            Assert.Equal(command.Message.ClientData.Addresses.MainAddress.Town, client.MainAddressTown);
            Assert.Equal(command.Message.ClientData.Addresses.MainAddress.PostCode, client.MainAddressPostcode);
            Assert.Equal(command.Message.ClientData.Addresses.MainAddress.Line1, client.MainAddressLine1);
            Assert.Equal(command.Message.ClientData.Addresses.MainAddress.Line2, client.MainAddressLine2);
            Assert.Equal(command.Message.ClientData.Addresses.MainAddress.County, client.MainAddressCounty);
            Assert.Equal(command.Message.ClientData.Addresses.RegisteredAddress.Town, client.RegisteredOfficeAddressTown);
            Assert.Equal(command.Message.ClientData.Addresses.RegisteredAddress.PostCode, client.RegisteredOfficeAddressPostcode);
            Assert.Equal(command.Message.ClientData.Addresses.RegisteredAddress.Line1, client.RegisteredOfficeAddressLine1);
            Assert.Equal(command.Message.ClientData.Addresses.RegisteredAddress.Line2, client.RegisteredOfficeAddressLine2);
            Assert.Equal(command.Message.ClientData.Addresses.RegisteredAddress.County, client.RegisteredOfficeAddressCounty);
        }

        [Fact]
        public async Task Should_not_remove_existing_members_if_involvements_is_empty()
        {
            _dbContext.Tenants.Add(new AccountsProduction.AccountsBuilder.Reporting.Domain.Tenant
            {
                Id = _tenantId,
                Name = "TenantName",
            });
            _dbContext.Clients.Add(new Client
            {
                Id = _clientId,
                TenantId = _tenantId,
            });

            _dbContext.Members.Add(new Member
            {
                ClientId = _clientId,
                ActiveFrom = DateTime.UtcNow.AddDays(-365),
                EntityName = "Original-EntityName",
                InvolvementUUID = Guid.NewGuid(),
            });
            _dbContext.ReportingPeriods.Add(new AccountsBuilder.Reporting.Domain.ReportingPeriod
            {
                AccountPeriodId = Guid.NewGuid(),
                ClientId = _clientId,
                ReportingPeriodStartDate = DateTime.UtcNow,
                ReportingPeriodEndDate = DateTime.UtcNow.AddDays(365),
            });
            _dbContext.SaveChanges();

            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.FRS105);
            command.Message.Involvements.Clear();

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            var result = await handler.Handle(command, new CancellationToken());

            // Assert
            var members = _dbContext.Members.Where(m => m.ClientId == _clientId).ToList();
            Assert.NotEmpty(members);
        }

        [Fact]
        public async Task Should_remove_existing_members_and_add()
        {
            _dbContext.Tenants.Add(new AccountsProduction.AccountsBuilder.Reporting.Domain.Tenant
            {
                Id = _tenantId,
                Name = "TenantName",
            });
            _dbContext.Clients.Add(new Client
            {
                Id = _clientId,
                TenantId = _tenantId,
            });

            _dbContext.Members.Add(new Member
            {
                ClientId = _clientId,
                ActiveFrom = DateTime.UtcNow.AddDays(-365),
                EntityName = "Original-EntityName1",
                InvolvementUUID = Guid.NewGuid(),
            });
            _dbContext.Members.Add(new Member
            {
                ClientId = _clientId,
                ActiveFrom = DateTime.UtcNow.AddDays(-365),
                EntityName = "Original-EntityName2",
                InvolvementUUID = Guid.NewGuid(),
            });
            _dbContext.ReportingPeriods.Add(new AccountsBuilder.Reporting.Domain.ReportingPeriod
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                ReportingPeriodStartDate = DateTime.UtcNow,
                ReportingPeriodEndDate = DateTime.UtcNow.AddDays(365),
            });
            _dbContext.SaveChanges();

            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.FRS105);

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            var result = await handler.Handle(command, new CancellationToken());

            // Assert
            var members = _dbContext.Members.Where(m => m.ClientId == _clientId).ToList();
            Assert.Single(members);
            Assert.Equal(_clientId, members[0].ClientId);
            Assert.Equal(command.Message.Involvements[0].InvolvementClientName, members[0].EntityName);
            Assert.Equal(command.Message.Involvements[0].InvolvementClientGuid, members[0].InvolvementUUID);
        }

        [Fact]
        public async Task Should_remove_existing_signatures_and_add()
        {
            var involvementUUID = Guid.NewGuid();

            _dbContext.Tenants.Add(new AccountsProduction.AccountsBuilder.Reporting.Domain.Tenant
            {
                Id = _tenantId,
                Name = "TenantName",
            });
            _dbContext.Clients.Add(new Client
            {
                Id = _clientId,
                TenantId = _tenantId,
            });
            _dbContext.ReportingPeriods.Add(new AccountsBuilder.Reporting.Domain.ReportingPeriod
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                ReportingPeriodStartDate = DateTime.UtcNow,
                ReportingPeriodEndDate = DateTime.UtcNow.AddDays(365),
            });
            _dbContext.Signatures.Add(new AccountsBuilder.Reporting.Domain.Signature
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                InvolvementUUID = involvementUUID,
                SignatureType = "Original-SignatureType",
                SignatoryFirstName = "Original-SignatoryFirstName",
                SignatorySurname = "Original-SignatorySurname",
                SignatoryTitle = "Original-SignatoryTitle",
                SignatureDate = DateTime.UtcNow.AddDays(365),
                InvolvementType = "Director",
            });
            _dbContext.SaveChanges();

            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.FRS105);

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            var result = await handler.Handle(command, new CancellationToken());

            // Assert
            var signatures = _dbContext.Signatures.Where(s => s.ClientId == _clientId).ToList();
            Assert.Equal(4, signatures.Count);
            Assert.Equal(2, signatures.Count(x => x.SignatureType == AccountsBuilder.Reporting.Application.Common.SignatureType.BalanceSheet.ToString()));
            Assert.Equal(1, signatures.Count(x => x.SignatureType == AccountsBuilder.Reporting.Application.Common.SignatureType.DirectorsReport.ToString()));
            Assert.Equal(1, signatures.Count(x => x.SignatureType == AccountsBuilder.Reporting.Application.Common.SignatureType.CIC34Report.ToString()));
            Assert.DoesNotContain(signatures, s => s.InvolvementUUID == involvementUUID);
        }

        [Fact]
        public async Task Should_remove_existing_notes()
        {
            _dbContext.Tenants.Add(new AccountsProduction.AccountsBuilder.Reporting.Domain.Tenant
            {
                Id = _tenantId,
                Name = "TenantName",
            });
            _dbContext.Clients.Add(new Client
            {
                Id = _clientId,
                TenantId = _tenantId,
            });
            _dbContext.ReportingPeriods.Add(new AccountsBuilder.Reporting.Domain.ReportingPeriod
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                ReportingPeriodStartDate = DateTime.UtcNow,
                ReportingPeriodEndDate = DateTime.UtcNow.AddDays(365),
            });
            _dbContext.NotesOther.Add(new AccountsBuilder.Reporting.Domain.Note.NoteOther
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                NoteTitle = "Original-NoteTitle",
                NoteText = "Original-Note",
            });
            _dbContext.NotesAccountingPolicies.Add(new AccountsBuilder.Reporting.Domain.Note.NoteAccountingPolicies
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                NoteTitle = "Original-NoteTitle",
                NoteText = "Original-Note",
            });
            _dbContext.NotesBalanceSheet.Add(new AccountsBuilder.Reporting.Domain.Note.NoteBalanceSheet
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                NoteTitle = "Original-NoteTitle",
                NoteText = "Original-Note",
            });
            _dbContext.NotesProfitAndLoss.Add(new AccountsBuilder.Reporting.Domain.Note.NoteProfitAndLoss
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                NoteTitle = "Original-NoteTitle",
                NoteText = "Original-Note",
            });
            _dbContext.SaveChanges();

            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.FRS105);
            command.Message.Notes = null;

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            var result = await handler.Handle(command, new CancellationToken());

            // Assert
            var notesOther = _dbContext.NotesOther.Where(n => n.ClientId == _clientId && n.AccountPeriodId == Data.CurrentPeriodId).ToList();
            var noteAccountingPolicies = _dbContext.NotesAccountingPolicies.Where(n => n.ClientId == _clientId && n.AccountPeriodId == Data.CurrentPeriodId).ToList();
            var noteBalanceSheets = _dbContext.NotesBalanceSheet.Where(n => n.ClientId == _clientId && n.AccountPeriodId == Data.CurrentPeriodId).ToList();
            var noteProfitAndLoss = _dbContext.NotesProfitAndLoss.Where(n => n.ClientId == _clientId && n.AccountPeriodId == Data.CurrentPeriodId).ToList();
            Assert.Empty(notesOther);
            Assert.Empty(noteBalanceSheets);
            Assert.Empty(noteBalanceSheets);
            Assert.Empty(noteProfitAndLoss);
        }

        [Fact]
        public async Task Should_remove_existing_lineitems()
        {
            _dbContext.Tenants.Add(new AccountsProduction.AccountsBuilder.Reporting.Domain.Tenant
            {
                Id = _tenantId,
                Name = "TenantName",
            });
            _dbContext.Clients.Add(new Client
            {
                Id = _clientId,
                TenantId = _tenantId,
            });
            _dbContext.ReportingPeriods.Add(new AccountsBuilder.Reporting.Domain.ReportingPeriod
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                ReportingPeriodStartDate = DateTime.UtcNow,
                ReportingPeriodEndDate = DateTime.UtcNow.AddDays(365),
            });
            _dbContext.LineItems.Add(new AccountsBuilder.Reporting.Domain.LineItem
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                CurrentValue = 9999,
                PreviousValue = 8888
            });

            _dbContext.SaveChanges();

            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.FRS102);

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            await handler.Handle(command, new CancellationToken());

            var lineitems = _dbContext.LineItems.Where(n => n.CurrentValue == 9999 && n.PreviousValue == 8888).ToList();

            Assert.Empty(lineitems);
        }

        [Fact]
        public async Task Should_remove_existing_reports()
        {
            _dbContext.Tenants.Add(new AccountsProduction.AccountsBuilder.Reporting.Domain.Tenant
            {
                Id = _tenantId,
                Name = "TenantName",
            });
            _dbContext.Clients.Add(new Client
            {
                Id = _clientId,
                TenantId = _tenantId,
            });
            _dbContext.ReportingPeriods.Add(new AccountsBuilder.Reporting.Domain.ReportingPeriod
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                ReportingPeriodStartDate = DateTime.UtcNow,
                ReportingPeriodEndDate = DateTime.UtcNow.AddDays(365),
            });
            _dbContext.Reports.Add(new AccountsBuilder.Reporting.Domain.Note.Reports
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                NoteTitle = "Original-ReportTitle",
                NoteText = "Original-Report",
            });

            _dbContext.SaveChanges();

            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.FRS102);

            command.Message.DataScreenValue = new DataScreenValueMessage
            {
                ClientId = _clientId,
                PeriodId = Data.CurrentPeriodId,
                CurrentPeriod = new List<PeriodScreenValueMessage>()
            };

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            await handler.Handle(command, new CancellationToken());

            var reports = _dbContext.Reports.Where(n => n.ClientId == _clientId && n.AccountPeriodId == Data.CurrentPeriodId).ToList();

            Assert.Empty(reports);
        }

        [Fact]
        public async Task Should_remove_existing_and_replace_dplsummarycalcs()
        {
            _dbContext.Tenants.Add(new AccountsProduction.AccountsBuilder.Reporting.Domain.Tenant
            {
                Id = _tenantId,
                Name = "TenantName",
            });
            _dbContext.Clients.Add(new Client
            {
                Id = _clientId,
                TenantId = _tenantId,
            });
            _dbContext.ReportingPeriods.Add(new AccountsBuilder.Reporting.Domain.ReportingPeriod
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                ReportingPeriodStartDate = DateTime.UtcNow,
                ReportingPeriodEndDate = DateTime.UtcNow.AddDays(365),
            });
            _dbContext.DplSummaryCalcs.Add(new AccountsProduction.AccountsBuilder.Domain.Reporting.Other.DplSummaryCalcs
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                DPLCalcType = "Original-DPLCalcType",
                DPLValue = 100
            });

            _dbContext.SaveChanges();

            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.FRS102);

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            await handler.Handle(command, new CancellationToken());

            var dplSummaryCalcs = _dbContext.DplSummaryCalcs.Where(n => n.ClientId == _clientId && n.AccountPeriodId == Data.CurrentPeriodId).ToList();

            Assert.Single(dplSummaryCalcs);
            Assert.NotEqual("Original-DPLCalcType", dplSummaryCalcs[0].DPLCalcType);
        }

        [Fact]
        public async Task Should_remove_existing_multicolumntoken()
        {
            _dbContext.Tenants.Add(new AccountsProduction.AccountsBuilder.Reporting.Domain.Tenant
            {
                Id = _tenantId,
                Name = "TenantName",
            });
            _dbContext.Clients.Add(new Client
            {
                Id = _clientId,
                TenantId = _tenantId,
            });
            _dbContext.ReportingPeriods.Add(new AccountsBuilder.Reporting.Domain.ReportingPeriod
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                ReportingPeriodStartDate = DateTime.UtcNow,
                ReportingPeriodEndDate = DateTime.UtcNow.AddDays(365),
            });
            _dbContext.MultiColumnToken.Add(new AccountsProduction.AccountsBuilder.Domain.Reporting.Other.MultiColumnToken
            {
                AccountPeriodId = Data.CurrentPeriodId,
                ClientId = _clientId,
                AssignedToken = "Original",
                TokenCount = 1,
                Token8 = "966"
            });

            _dbContext.SaveChanges();

            var command = Data.GetProcessAccountsBuilderDataCommand(AccountsProduction.AccountsBuilder.Reporting.Application.Common.ReportType.FRS102);

            var handler = new ProcessAccountsBuilderDataCommandHandler(_mockUserContext.Object, _dbContext, _logger, _saveUpdateStrategies);

            await handler.Handle(command, new CancellationToken());

            var multiColumnTokens = _dbContext.MultiColumnToken.Where(n => n.ClientId == _clientId && n.AccountPeriodId == Data.CurrentPeriodId).ToList();

            Assert.Empty(multiColumnTokens);
        }

        private void AssertTenant(Guid tenantId)
        {
            var tenant = _dbContext.Tenants.FirstOrDefault(t => t.Id == tenantId);
            Assert.NotNull(tenant);
        }

        private void AssertClient(Guid clientId)
        {
            var client = _dbContext.Clients.FirstOrDefault(c => c.Id == clientId);
            Assert.NotNull(client);
        }

        private void AssertReportingPeriod(Guid periodId)
        {
            var reportingPeriod = _dbContext.ReportingPeriods.FirstOrDefault(rp => rp.AccountPeriodId == periodId);
            Assert.NotNull(reportingPeriod);
            Assert.Equal("SupplementaryNote", reportingPeriod.ReviseType);
        }

        private void AssertSignatures(Guid periodId)
        {
            var signature = _dbContext.Signatures.FirstOrDefault(x => x.AccountPeriodId == periodId);
            Assert.NotNull(signature);
        }

        private void AssertMembers(Guid clientId)
        {
            var member = _dbContext.Members.FirstOrDefault(x => x.ClientId == clientId);
            Assert.NotNull(member);
        }

        private void AssertNoteOther(Guid periodId)
        {
            var noteOther = _dbContext.NotesOther.FirstOrDefault(x => x.AccountPeriodId == periodId);
            Assert.NotNull(noteOther);
        }

        private void AssertReports(Guid periodId)
        {
            var reports = _dbContext.Reports.FirstOrDefault(x => x.AccountPeriodId == periodId);
            Assert.NotNull(reports);
        }

        private void AssertNoteProfitAndLoss(Guid periodId)
        {
            var noteProfitAndLoss = _dbContext.NotesProfitAndLoss.FirstOrDefault(x => x.AccountPeriodId == periodId);
            Assert.NotNull(noteProfitAndLoss);
        }

        private void AssertBalanceSheetFRS105(Guid periodId)
        {
            var balanceSheetFRS105 = _dbContext.BalanceSheetsFRS105.FirstOrDefault(x => x.AccountPeriodId == periodId);
            Assert.NotNull(balanceSheetFRS105);
        }

        private void AssertBalanceSheetLLP(Guid periodId)
        {
            var balanceSheetLLP = _dbContext.BalanceSheetsLLP.FirstOrDefault(x => x.AccountPeriodId == periodId);
            Assert.NotNull(balanceSheetLLP);
        }

        private void AssertProfitAndLossFRS105(Guid periodId)
        {
            var profitAndLossFRS105 = _dbContext.ProfitAndLossesFRS105.FirstOrDefault(x => x.AccountPeriodId == periodId);
            Assert.NotNull(profitAndLossFRS105);
        }

        private void AssertLineItems(Guid periodId)
        {
            var lineItem = _dbContext.LineItems.FirstOrDefault(x => x.AccountPeriodId == periodId);
            Assert.NotNull(lineItem);
        }

        private void AssertNoteAccountingPolicies(Guid periodId)
        {
            var lineItem = _dbContext.NotesAccountingPolicies.FirstOrDefault(x => x.AccountPeriodId == periodId);
            Assert.NotNull(lineItem);
        }

        private void AssertLineItemsWithProfitshares(Guid periodId)
        {
            var lineItem = _dbContext.NotesProfitAndLoss.FirstOrDefault(x => x.AccountPeriodId == periodId);
            Assert.NotNull(lineItem);
        }

        private void AssertProfitAndLossUnincorporated(Guid periodId)
        {
            var profitAndLossUnincorporated = _dbContext.ProfitAndLossesNonCorp.FirstOrDefault(x => x.AccountPeriodId == periodId);
            Assert.NotNull(profitAndLossUnincorporated);
        }

        private void AssertBalanceSheetNonCorp(Guid periodId)
        {
            var balanceSheetNonCorp = _dbContext.BalanceSheetsNonCorp.FirstOrDefault(x => x.AccountPeriodId == periodId);
            Assert.NotNull(balanceSheetNonCorp);
        }

        private void AssertDplSummaryCalc(Guid periodId)
        {
            var dplSummaryCalc = _dbContext.DplSummaryCalcs.FirstOrDefault(x => x.AccountPeriodId == periodId);
            Assert.NotNull(dplSummaryCalc);
        }

        private void AssertMultiColumnToken(Guid periodId)
        {
            var multiColumnToken = _dbContext.MultiColumnToken.FirstOrDefault(x => x.AccountPeriodId == periodId);
            Assert.NotNull(multiColumnToken);
        }
    }
}
