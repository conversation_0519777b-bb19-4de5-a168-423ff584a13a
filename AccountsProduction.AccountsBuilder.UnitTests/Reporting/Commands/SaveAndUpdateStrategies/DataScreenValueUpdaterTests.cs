﻿using AccountsProduction.AccountsBuilder.Application.Reporting.Commands.SaveAndUpdateStrategies;
using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using AccountsProduction.AccountsBuilder.Reporting.Domain.Note.Contract;
using Iris.AccountsProduction.AccountsBuilder.Messages.DataScreenValue;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.Commands.SaveAndUpdateStrategies
{
    public class DataScreenValueUpdaterTests
    {
        public static readonly List<object[]> UpdateGenericNoteTableTestData = new()
        {
            new object[] { new List<NoteOther>(), "TestNote" },
            new object[] { new List<Reports>(), "TestReports" },
            new object[] { new List<NoteSofa>(), "TestNoteSofa" },
            new object[] { new List<NoteProfitAndLoss>(), "NoteProfitAndLoss" },
            new object[] { new List<NoteAccountingPolicies>(), "NoteAccountingPolicies" },
            new object[] { new List<NoteBalanceSheet>(), "NoteBalanceSheet" }
        };

        [Theory]
        [MemberData(nameof(UpdateGenericNoteTableTestData))]
        public void UpdateGenericNoteTable_ShouldAddEntry<T>(List<T> notes, string expectedNoteType) where T : NoteBase, new()
        {
            // Arrange
            var screenField = new ScreenFieldMessage { Name = expectedNoteType, Value = "TestValue" };
            var screenId = "Screen1";
            var clientId = Guid.NewGuid();
            var periodId = Guid.NewGuid();

            // Act
            var result = DataScreenValueUpdater.UpdateGenericNoteTable(notes, screenField, screenId, clientId, periodId);

            // Assert
            Assert.Single(result);
            var note = result[0];

            Assert.Equal(clientId, note.ClientId);
            Assert.Equal(periodId, note.AccountPeriodId);
            Assert.Equal(screenId, note.ScreenId);
            Assert.Equal(expectedNoteType, note.NoteType);
            Assert.Equal("TestValue", note.NoteText);
            Assert.Null(note.NoteValue);
        }

        [Fact]
        public void UpdateNoteOther_ShouldAddNoteOther()
        {
            // Arrange
            var notesOther = new List<NoteOther>();
            var screenField = new ScreenFieldMessage { Name = "TestNote", Value = "TestValue" };
            var screenId = "Screen1";
            var clientId = Guid.NewGuid();
            var periodId = Guid.NewGuid();

            var result = DataScreenValueUpdater.UpdateGenericNoteTable(notesOther, screenField, screenId, clientId, periodId);

            Assert.Single(result);
            Assert.Equal(clientId, result[0].ClientId);
            Assert.Equal(periodId, result[0].AccountPeriodId);
            Assert.Equal(screenId, result[0].ScreenId);
            Assert.Equal("TestNote", result[0].NoteType);
            Assert.Equal("TestValue", result[0].NoteText);
            Assert.Null(result[0].NoteValue);
        }
    }
}
