﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Application.Reporting.Commands.SaveAndUpdateStrategies;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence;
using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.Commands.SaveAndUpdateStrategies;

public class IFRSStrategyTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly Mock<ILogger<IFRSStrategy>> _loggerMock;
    private readonly IMapper _mapper;
    private readonly AccountsProductionReportingDbContext _dbContext;
    private readonly IFRSStrategy _ifrsStrategy;
    public IFRSStrategyTests()
    {
        _mediatorMock = new Mock<IMediator>();
        _loggerMock = new Mock<ILogger<IFRSStrategy>>();
        _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
        var options = new DbContextOptionsBuilder<AccountsProductionReportingDbContext>()
           .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()).Options;
        _dbContext = new AccountsProductionReportingDbContext(options);
        _dbContext.Database.EnsureCreated();
        _ifrsStrategy = new IFRSStrategy(_mediatorMock.Object, _loggerMock.Object, _dbContext, _mapper);
    }
    [Fact]
    public void Name_should_return_expected_value()
    {
        var result = _ifrsStrategy.Name;
        Assert.Equal(ReportType.IFRS, result);
    }
}
