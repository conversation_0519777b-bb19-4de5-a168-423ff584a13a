﻿using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper;
using AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence;
using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.Commands.SaveAndUpdateStrategies
{
    public class FRS105StrategyTests
    {
        private readonly Mock<IMediator> _mediatorMock;
        private readonly Mock<ILogger<FRS105Strategy>> _loggerMock;
        private readonly IMapper _mapper;

        private readonly AccountsProductionReportingDbContext _dbContext;
        private readonly FRS105Strategy _frs105Strategy;

        public FRS105StrategyTests()
        {
            _mediatorMock = new Mock<IMediator>();
            _loggerMock = new Mock<ILogger<FRS105Strategy>>();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();

            var options = new DbContextOptionsBuilder<AccountsProductionReportingDbContext>()
               .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
               .Options;
            _dbContext = new AccountsProductionReportingDbContext(options);
            _dbContext.Database.EnsureCreated();

            _frs105Strategy = new FRS105Strategy(_mediatorMock.Object, _loggerMock.Object, _dbContext, _mapper);
        }

        [Fact]
        public void Name_should_return_expected_value()
        {
            var result = _frs105Strategy.Name;

            Assert.Equal(ReportType.FRS105, result);
        }
    }
}
