﻿using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper;
using AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence;
using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.Commands.SaveAndUpdateStrategies
{
    public class UnincorporatedStrategyTests
    {
        private readonly Mock<IMediator> _mediatorMock;
        private readonly Mock<ILogger<UnincorporatedStrategy>> _loggerMock;
        private readonly IMapper _mapper;

        private readonly AccountsProductionReportingDbContext _dbContext;
        private readonly UnincorporatedStrategy _unincorporatedStrategy;

        public UnincorporatedStrategyTests()
        {
            _mediatorMock = new Mock<IMediator>();
            _loggerMock = new Mock<ILogger<UnincorporatedStrategy>>();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();

            var options = new DbContextOptionsBuilder<AccountsProductionReportingDbContext>()
               .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
               .Options;
            _dbContext = new AccountsProductionReportingDbContext(options);
            _dbContext.Database.EnsureCreated();

            _unincorporatedStrategy = new UnincorporatedStrategy(_mediatorMock.Object, _loggerMock.Object, _dbContext, _mapper);
        }

        [Fact]
        public void Name_should_return_expected_value()
        {
            var result = _unincorporatedStrategy.Name;

            Assert.Equal(ReportType.UNINCORPORATED, result);
        }
    }
}
