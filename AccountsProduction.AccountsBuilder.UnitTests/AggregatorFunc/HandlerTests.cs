﻿using AccountsProduction.AccountsBuilder.AggregatorFunc;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands;
using Amazon.Lambda.SNSEvents;
using Amazon.Lambda.TestUtilities;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Iris.Elements.Messaging.Message;
using Iris.Platform.Eventbus.Client.Dotnet.InputHandlers;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.AggregatorFunc
{
    public class HandlerTests
    {
        private static readonly Guid ClientId = TestHelpers.Guids.GuidTwo;
        private static readonly Guid PeriodId = TestHelpers.Guids.GuidThree;
        private readonly Mock<IMediator> _mediator;
        private readonly Mock<ILogger<Handler>> _logger;
        private readonly Mock<UserContext> _userContext;
        private readonly Mock<ILambdaInputHandler> _lambdaInputHandler;

        public HandlerTests()
        {
            _logger = new Mock<ILogger<Handler>>();
            _userContext = new Mock<UserContext>();
            _mediator = new Mock<IMediator>();
            _lambdaInputHandler = new Mock<ILambdaInputHandler>();
        }

        [Theory]
        [InlineData(EventTypes.SignatoriesDataEvent)]
        [InlineData(EventTypes.FinancialNotesDataEvent)]
        [InlineData(EventTypes.TrialBalanceDataEvent)]
        [InlineData(EventTypes.AccountingPoliciesNotesDataEvent)]
        [InlineData(EventTypes.PartnershipProfitShareDataEvent)]
        public async Task Should_process_sns_message_for_data_events(string eventType)
        {
            var message = new ElementsMessage<dynamic>
            {
                Message = "test message",
                Meta = new ElementsMessageMeta
                {
                    Type = eventType,
                    TenantId = TestHelpers.Guids.GuidThree.ToString()
                }
            };
            var jsonNamingPolicy = new JsonSerializerOptions
            { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
            var function = new Handler(_userContext.Object, _logger.Object, _mediator.Object, _lambdaInputHandler.Object);
            var messageAttributes = new Dictionary<string, SNSEvent.MessageAttribute>
            {
                { "ClientId", new SNSEvent.MessageAttribute() { Value = ClientId.ToString() } },
                { "PeriodId", new SNSEvent.MessageAttribute() { Value = PeriodId.ToString() } }
            };

            var snsEvent = new SNSEvent
            {
                Records = new List<SNSEvent.SNSRecord>
                {
                    new()
                    { Sns = new SNSEvent.SNSMessage {
                        Message =JsonSerializer.Serialize(message,jsonNamingPolicy),MessageAttributes=messageAttributes}
                    }

                }
            };
            var snsEventStream = new MemoryStream(Encoding.UTF8.GetBytes(JsonSerializer.Serialize(snsEvent)));

            await function.HandleEvent(snsEventStream, new TestLambdaContext());

            _mediator.Verify(m => m.Send(It.Is<ProcessAggregationCommand>(arg =>
            arg.ClientId == ClientId && arg.PeriodId == PeriodId && arg.MessageType == eventType), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Should_process_valid_sns_message()
        {
            var message = new ElementsMessage<dynamic>
            {
                Message = "test message",
                Meta = new ElementsMessageMeta
                {
                    Type = EventTypes.TrialBalanceDataEvent,
                    TenantId = TestHelpers.Guids.GuidThree.ToString()
                }
            };
            var jsonNamingPolicy = new JsonSerializerOptions
            { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
            var messageAttributes = new Dictionary<string, SNSEvent.MessageAttribute>
            {
                { "ClientId", new SNSEvent.MessageAttribute() { Value = ClientId.ToString() } },
                { "PeriodId", new SNSEvent.MessageAttribute() { Value = PeriodId.ToString() } }
            };

            var function = new Handler(_userContext.Object, _logger.Object, _mediator.Object, _lambdaInputHandler.Object);
            var snsEvent = new SNSEvent
            {
                Records = new List<SNSEvent.SNSRecord>
                {
                    new SNSEvent.SNSRecord { Sns = new SNSEvent.SNSMessage {
                        Message =JsonSerializer.Serialize(message,jsonNamingPolicy),
                        MessageAttributes=messageAttributes }
                    }

                }
            };
            var snsEventStream = new MemoryStream(Encoding.UTF8.GetBytes(JsonSerializer.Serialize(snsEvent)));

            await function.HandleEvent(snsEventStream, new TestLambdaContext());

            _mediator.Verify(m => m.Send(It.Is<ProcessAggregationCommand>(arg =>
                arg.ClientId == ClientId && arg.PeriodId == PeriodId && arg.MessageType == EventTypes.TrialBalanceDataEvent), It.IsAny<CancellationToken>()), Times.Once);
        }

    }
}
