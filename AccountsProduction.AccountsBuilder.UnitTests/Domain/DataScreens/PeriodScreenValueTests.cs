﻿using AccountsProduction.AccountsBuilder.Domain.DataScreens;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Domain.DataScreens
{
    public class PeriodScreenValueTests
    {
        [Fact]
        public void ScreenId_ShouldGetAndSet()
        {
            var periodScreenValue = new PeriodScreenValue();
            periodScreenValue.ScreenId = "TestScreenId";
            Assert.Equal("TestScreenId", periodScreenValue.ScreenId);
        }

        [Fact]
        public void ReportMappingTable_ShouldGetAndSet()
        {
            var periodScreenValue = new PeriodScreenValue();
            periodScreenValue.ReportMappingTable = "TestReportMappingTable";
            Assert.Equal("TestReportMappingTable", periodScreenValue.ReportMappingTable);
        }

        [Fact]
        public void ScreenFields_ShouldGetAndSet()
        {
            var screenFields = new List<ScreenField>
            {
                new ScreenField { Name = "Field1", Value = "Value1" },
                new ScreenField { Name = "Field2", Value = 123 }
            };

            var periodScreenValue = new PeriodScreenValue();
            periodScreenValue.ScreenFields = screenFields;

            Assert.Equal(screenFields, periodScreenValue.ScreenFields);
        }
    }
}
