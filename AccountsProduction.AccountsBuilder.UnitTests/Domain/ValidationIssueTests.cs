﻿using AccountsProduction.AccountsBuilder.Domain;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Domain
{
    public class ValidationIssueTests
    {
        [Fact]
        public void Should_return_mandatory_missing_issue()
        {
            var issue = ValidationIssue.BuildValidationIssue("errorCode", "name", "displayName", "breadcrumb", "description", ValidationRuleType.Missing, Target.IssueLog, ErrorCategoryType.Mandatory);

            issue.ErrorCode.ShouldBe("errorCode");
            issue.Name.ShouldBe("name");
            issue.DisplayName.ShouldBe("displayName");
            issue.Breadcrumb.ShouldBe("breadcrumb");
            issue.Description.ShouldBe("description");
            issue.Type.ShouldBe(ValidationRuleType.Missing);
            issue.Target.ShouldBe(Target.IssueLog);
            issue.ErrorCategory.ShouldBe(ErrorCategoryType.Mandatory);
        }
    }
}