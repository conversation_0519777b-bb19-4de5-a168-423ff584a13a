﻿using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Domain
{
    public class FinancialTests
    {
        [Fact]
        public void Should_set_all_properties()
        {
            var financialEntity = new Financial();

            financialEntity.ShouldNotBeNull();
            financialEntity.CrossCheck.ShouldBe(default);
            financialEntity.DepreciationAndOtherAmountsWrittenOffAssets.ShouldBe(default);
            financialEntity.CostOfRawMaterialsAndConsumables.ShouldBe(default);
            financialEntity.OtherCharges.ShouldBe(default);
            financialEntity.OtherIncome.ShouldBe(default);
            financialEntity.Period.ShouldBe(default);
            financialEntity.Turnover.ShouldBe(default);
            financialEntity.StaffCosts.ShouldBe(default);
            financialEntity.Tax.ShouldBe(default);
            financialEntity.CalledUpShareCapitalNotPaid.ShouldBe(default);
            financialEntity.FixedAssets.ShouldBe(default);
            financialEntity.CurrentAssets.ShouldBe(default);
            financialEntity.PrepaymentsAndAccruedIncome.ShouldBe(default);
            financialEntity.CreditorsAmountsFallingDueWithinOneYear.ShouldBe(default);

            financialEntity.NetCurrentAssetsOrLiabilities.ShouldBe(default);
            financialEntity.TotalAssetsLessCurrentLiabilities.ShouldBe(default);
            financialEntity.CreditorsAmountsFallingAfterMoreThanOneYear.ShouldBe(default);
            financialEntity.ProvisionsForLiabilities.ShouldBe(default);
            financialEntity.AccrualsAndDeferredIncome.ShouldBe(default);
            financialEntity.NetAssets.ShouldBe(default);
            financialEntity.CapitalAndReserves.ShouldBe(default);
            financialEntity.ProfitLossAvailableForDiscretionaryDivision.ShouldBe(default);
            financialEntity.MembersRemunerationAsExpense.ShouldBe(default);
            financialEntity.NonControllingInterestsPL.ShouldBe(default);
            financialEntity.NonControllingInterestsBS.ShouldBe(default);
            financialEntity.TotalMembersInterests.ShouldBe(default);
            financialEntity.MembersOtherInterests.ShouldBe(default);
            financialEntity.MembersCapital.ShouldBe(default);
            financialEntity.OtherDebtsDueToMembers.ShouldBe(default);
            financialEntity.LoansAndOtherDebtsDueToMembers.ShouldBe(default);
            financialEntity.HerdBasis.ShouldBe(default);
        }
    }
}