﻿using AccountsProduction.AccountsBuilder.Domain;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Domain
{
    public class GroupAccountSubAccountIntervalTests
    {
        public class When_working_with_group_account_sub_account_interval_entity : GroupAccountSubAccountIntervalTests
        {
            [Fact]
            public void Should_check_if_account_code_is_within_range_correctly()
            {
                var interval = new GroupAccountSubAccountInterval()
                {
                    AccountIntervalFrom = 1,
                    AccountIntervalTo = 10,
                    SubAccountIntervalFrom = 0,
                    SubAccountIntervalTo = 0
                };
                this.ShouldSatisfyAllConditions(() => interval.IsAccountCodeSubAccountCodeInRange(1, null).ShouldBeTrue(),
                    () => interval.IsAccountCodeSubAccountCodeInRange(10, null).ShouldBeTrue(),
                    () => interval.IsAccountCodeSubAccountCodeInRange(5, null).ShouldBeTrue(),
                    () => interval.IsAccountCodeSubAccountCodeInRange(5, 5).ShouldBeTrue(),
                    () => interval.IsAccountCodeSubAccountCodeInRange(11, null).ShouldBeFalse());
            }

            [Fact]
            public void Should_check_if_sub_account_code_is_within_range_correctly()
            {
                var interval = new GroupAccountSubAccountInterval()
                {
                    AccountIntervalFrom = 1,
                    AccountIntervalTo = 1,
                    SubAccountIntervalFrom = 1,
                    SubAccountIntervalTo = 10
                };
                this.ShouldSatisfyAllConditions(() => interval.IsAccountCodeSubAccountCodeInRange(1, 1).ShouldBeTrue(),
                    () => interval.IsAccountCodeSubAccountCodeInRange(1, 5).ShouldBeTrue(),
                    () => interval.IsAccountCodeSubAccountCodeInRange(1, 10).ShouldBeTrue(),
                    () => interval.IsAccountCodeSubAccountCodeInRange(2, null).ShouldBeFalse(),
                    () => interval.IsAccountCodeSubAccountCodeInRange(1, 20).ShouldBeFalse());
            }
        }
    }
}