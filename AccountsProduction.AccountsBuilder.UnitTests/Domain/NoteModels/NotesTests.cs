﻿using AccountsProduction.AccountsBuilder.Domain.NoteModels;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Domain.NoteModels
{
    public class NotesTests
    {
        private readonly NotesData _currentNotesData;
        private readonly NotesData _previousNotesData;

        public NotesTests()
        {
            _currentNotesData = new NotesData
            {
                AverageNumberOfEmployees = new AverageNumberOfEmployees
                {
                    CurrentPeriod = 1,
                    PreviousPeriod = 2
                },
                OffBalanceSheetArrangements = "text1",
                AdvancesCreditAndGuaranteesGrantedToDirectors = "text2",
                GuaranteesAndOtherFinancialCommitments = "text3",
                RelatedPartyTransactions = "text4",
                LoansAndOtherDebtsDueToMembers = "text44",
                AdditionalNote1 = new AdditionalNote { NoteTitle = "text5", NoteText = "text6" },
                AdditionalNote2 = new AdditionalNote { NoteTitle = "text7", NoteText = "text8" },
                ControllingPartyNote = "text9",
                IntangibleAssetsRevaluation = "text10",
                OperatingProfitLoss = new OperatingProfitLoss
                {
                    IsEnabled = true,
                    Items = new List<OperatingProfitLossItem> {
                            new OperatingProfitLossItem { Index = 1, Description = "Test1", Value = 201 },
                            new OperatingProfitLossItem { Index = 2, Description = "Test2", Value = 202.34M },
                            new OperatingProfitLossItem { Index = 3, Description = "Test3", Value = -203.67M },
                    }
                },
                AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = new AdvancesCreditAndGuaranteesGrantedToDirectors
                {
                    Guarantees = "Guarantees",
                    Items = new List<AdvancesCreditAndGuaranteesGrantedToDirectorItem>
                        {
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItem {
                                Index = 1,
                                InvolvementClientGuid = TestHelpers.Guids.GuidFive,
                                DirectorName = "Director1",
                                BalanceOutstandingAtStartOfYear = 1000M,
                                AmountsAdvanced = 100M,
                                AmountsRepaid = 0M,
                                AmountsWrittenOff = 0M,
                                AmountsWaived = 0M,
                                AdvanceCreditConditions = "Director1 AdvanceCreditConditions",
                                BalanceOutstandingAtEndOfYear = 1100M
                            },
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItem {
                                Index = 2,
                                InvolvementClientGuid = TestHelpers.Guids.GuidSix,
                                DirectorName = "Director2",
                                BalanceOutstandingAtStartOfYear = 2000M,
                                AmountsAdvanced = 200M,
                                AmountsRepaid = 0M,
                                AmountsWrittenOff = 0M,
                                AmountsWaived = 0M,
                                AdvanceCreditConditions = "Director2 AdvanceCreditConditions",
                                BalanceOutstandingAtEndOfYear = 2200M
                            },
                        }
                },
                TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                {
                    ValuationInCurrentReportingPeriod = new ValuationInCurrentReportingPeriod
                    {
                        ValuationDetails = "Valuation Details",
                        IndependentValuerInvolved = true,
                        RevaluationBasis = "Revaluation Basis",
                        DateOfRevaluation = new DateTime(2019, 05, 09, 09, 15, 00)
                    },
                    HistoricalCostBreakdown = new HistoricalCostBreakdown
                    {
                        RevaluedAssetClass = "Revalued Asset Class",
                        RevaluedClassPronoun = "Revalued Class Pronoun",
                        CurrentReportingPeriodAccumulatedDepreciation = 1.33m,
                        CurrentReportingPeriodCost = 1.45m,
                    },
                    AnalysisOfCostOrValuation = new AnalysisOfCostOrValuation
                    {
                        AnalysisOfCostOrValuationItems = new List<AnalysisOfCostOrValuationItem>
                            {
                                new AnalysisOfCostOrValuationItem
                                {
                                    Index = 3,
                                    Year = 2020,
                                    LandAndBuildings = 1.1m,
                                    PlantAndMachineryEtc = 2.2m
                                },
                                new AnalysisOfCostOrValuationItem
                                {
                                    Index = 4,
                                    Year = 2021,
                                    LandAndBuildings = 3.3m,
                                    PlantAndMachineryEtc = 4.4m
                                },
                                new AnalysisOfCostOrValuationItem
                                {
                                    Index = 5,
                                    Year = 2022,
                                    LandAndBuildings = -3.3m,
                                    PlantAndMachineryEtc = -4.4m
                                }
                            },
                        CostLandAndBuildings = 5.5m,
                        CostPlantAndMachineryEtc = 6.6m,
                        TotalLandAndBuildings = 10,
                        TotalPlantAndMachineryEtc = 11
                    }
                }
            };
            _previousNotesData = new NotesData
            {
                AverageNumberOfEmployees = new AverageNumberOfEmployees
                {
                    CurrentPeriod = 2,
                    PreviousPeriod = 3
                },
                OffBalanceSheetArrangements = "text11",
                AdvancesCreditAndGuaranteesGrantedToDirectors = "text22",
                GuaranteesAndOtherFinancialCommitments = "text33",
                RelatedPartyTransactions = "text44",
                LoansAndOtherDebtsDueToMembers = "text444",
                AdditionalNote1 = new AdditionalNote { NoteTitle = "text55", NoteText = "text66" },
                AdditionalNote2 = new AdditionalNote { NoteTitle = "text77", NoteText = "text88" },
                ControllingPartyNote = "text99",
                IntangibleAssetsRevaluation = "text101",
                OperatingProfitLoss = new OperatingProfitLoss
                {
                    IsEnabled = true,
                    Items = new List<OperatingProfitLossItem> {
                            new OperatingProfitLossItem { Index = 1, Description = "Test1", Value = -201.56M },
                    }
                },
                AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = new AdvancesCreditAndGuaranteesGrantedToDirectors
                {
                    Guarantees = "Guarantees",
                    Items = new List<AdvancesCreditAndGuaranteesGrantedToDirectorItem>
                        {
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItem {
                                Index = 1,
                                InvolvementClientGuid = TestHelpers.Guids.GuidFive,
                                DirectorName = "Director1", BalanceOutstandingAtStartOfYear = 1000M,
                                AmountsAdvanced = 100M,
                                AmountsRepaid = 0M,
                                AmountsWrittenOff = 0M,
                                AmountsWaived = 0M,
                                AdvanceCreditConditions = "Director1 AdvanceCreditConditions",
                                BalanceOutstandingAtEndOfYear = 1100M
                            },
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItem {
                                Index = 2,
                                InvolvementClientGuid = TestHelpers.Guids.GuidSix,
                                DirectorName = "Director2",
                                BalanceOutstandingAtStartOfYear = 2000M,
                                AmountsAdvanced = 200M,
                                AmountsRepaid = 0M,
                                AmountsWrittenOff = 0M,
                                AmountsWaived = 0M,
                                AdvanceCreditConditions = "Director2 AdvanceCreditConditions",
                                BalanceOutstandingAtEndOfYear = 2200M
                            },
                        }
                },
                TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                {
                    ValuationInCurrentReportingPeriod = new ValuationInCurrentReportingPeriod
                    {
                        ValuationDetails = "Valuation Details previous",
                        IndependentValuerInvolved = true,
                        RevaluationBasis = "Revaluation Basis previous",
                        DateOfRevaluation = new DateTime(2018, 05, 09, 09, 15, 00)
                    },
                    HistoricalCostBreakdown = new HistoricalCostBreakdown
                    {
                        RevaluedAssetClass = "Revalued Asset Class",
                        RevaluedClassPronoun = "Revalued Class Pronoun",
                        CurrentReportingPeriodAccumulatedDepreciation = 1.33m,
                        CurrentReportingPeriodCost = 1.45m,
                    },
                    AnalysisOfCostOrValuation = new AnalysisOfCostOrValuation
                    {
                        AnalysisOfCostOrValuationItems = new List<AnalysisOfCostOrValuationItem>
                            {
                                new AnalysisOfCostOrValuationItem
                                {
                                    Index = 3,
                                    Year = 2019,
                                    LandAndBuildings = 1.1m,
                                    PlantAndMachineryEtc = 2.2m
                                },
                                new AnalysisOfCostOrValuationItem
                                {
                                    Index = 4,
                                    Year = 2020,
                                    LandAndBuildings = 3.3m,
                                    PlantAndMachineryEtc = 4.4m
                                },
                                new AnalysisOfCostOrValuationItem
                                {
                                    Index = 5,
                                    Year = 2021,
                                    LandAndBuildings = -3.3m,
                                    PlantAndMachineryEtc = -4.4m
                                }
                            },
                        CostLandAndBuildings = 5.5m,
                        CostPlantAndMachineryEtc = 6.6m,
                        TotalLandAndBuildings = 11,
                        TotalPlantAndMachineryEtc = 12
                    }
                }
            };
        }

        [Fact]
        public void Should_map_notes_from_currentPeriod_when_all_data_is_available()
        {
            var note = new Notes
            {
                CurrentPeriod = _currentNotesData
            };

            note.AverageNumberOfEmployees.CurrentPeriod.ShouldBe(_currentNotesData.AverageNumberOfEmployees.CurrentPeriod);
            note.AverageNumberOfEmployees.PreviousPeriod.ShouldBe(_currentNotesData.AverageNumberOfEmployees.PreviousPeriod);
            note.OffBalanceSheetArrangements.ShouldBe(_currentNotesData.OffBalanceSheetArrangements);
            note.AdvancesCreditAndGuaranteesGrantedToDirectors.ShouldBe(_currentNotesData.AdvancesCreditAndGuaranteesGrantedToDirectors);
            note.GuaranteesAndOtherFinancialCommitments.ShouldBe(_currentNotesData.GuaranteesAndOtherFinancialCommitments);
            note.RelatedPartyTransactions.ShouldBe(_currentNotesData.RelatedPartyTransactions);
            note.LoansAndOtherDebtsDueToMembers.ShouldBe(_currentNotesData.LoansAndOtherDebtsDueToMembers);
            note.AdditionalNote1.NoteTitle.ShouldBe(_currentNotesData.AdditionalNote1.NoteTitle);
            note.AdditionalNote1.NoteText.ShouldBe(_currentNotesData.AdditionalNote1.NoteText);
            note.AdditionalNote2.NoteTitle.ShouldBe(_currentNotesData.AdditionalNote2.NoteTitle);
            note.AdditionalNote2.NoteText.ShouldBe(_currentNotesData.AdditionalNote2.NoteText);
            note.ControllingPartyNote.ShouldBe(_currentNotesData.ControllingPartyNote);
            note.IntangibleAssetsRevaluation.ShouldBe(_currentNotesData.IntangibleAssetsRevaluation);
            note.OperatingProfitLoss.IsEnabled.ShouldBe(_currentNotesData.OperatingProfitLoss.IsEnabled);
            note.OperatingProfitLoss.Items[0].Index.ShouldBe(_currentNotesData.OperatingProfitLoss.Items[0].Index);
            note.OperatingProfitLoss.Items[0].Description.ShouldBe(_currentNotesData.OperatingProfitLoss.Items[0].Description);
            note.OperatingProfitLoss.Items[0].Value.ShouldBe(_currentNotesData.OperatingProfitLoss.Items[0].Value);
            note.OperatingProfitLoss.Items[1].Index.ShouldBe(_currentNotesData.OperatingProfitLoss.Items[1].Index);
            note.OperatingProfitLoss.Items[1].Description.ShouldBe(_currentNotesData.OperatingProfitLoss.Items[1].Description);
            note.OperatingProfitLoss.Items[1].Value.ShouldBe(_currentNotesData.OperatingProfitLoss.Items[1].Value);
            note.OperatingProfitLoss.Items[2].Index.ShouldBe(_currentNotesData.OperatingProfitLoss.Items[2].Index);
            note.OperatingProfitLoss.Items[2].Description.ShouldBe(_currentNotesData.OperatingProfitLoss.Items[2].Description);
            note.OperatingProfitLoss.Items[2].Value.ShouldBe(_currentNotesData.OperatingProfitLoss.Items[2].Value);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].Index.ShouldBe(1);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidFive);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].DirectorName.ShouldBe("Director1");
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtStartOfYear.ShouldBe(1000M);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsAdvanced.ShouldBe(100M);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsRepaid.ShouldBe(0M);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWaived.ShouldBe(0M);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWrittenOff.ShouldBe(0M);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtEndOfYear.ShouldBe(1100M);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AdvanceCreditConditions.ShouldBe("Director1 AdvanceCreditConditions");
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidSix);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].DirectorName.ShouldBe("Director2");
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].BalanceOutstandingAtStartOfYear.ShouldBe(2000M);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsAdvanced.ShouldBe(200M);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsRepaid.ShouldBe(0M);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsWaived.ShouldBe(0M);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsWrittenOff.ShouldBe(0M);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].BalanceOutstandingAtEndOfYear.ShouldBe(2200M);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AdvanceCreditConditions.ShouldBe("Director2 AdvanceCreditConditions");
            note.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.ValuationDetails.ShouldBe("Valuation Details");
            note.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.IndependentValuerInvolved.ShouldBeTrue();
            note.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.RevaluationBasis.ShouldBe("Revaluation Basis");
            note.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.DateOfRevaluation.ShouldBe(new DateTime(2019, 05, 09, 09, 15, 00));
            note.TangibleFixedAssetsNotes.HistoricalCostBreakdown.RevaluedAssetClass.ShouldBe("Revalued Asset Class");
            note.TangibleFixedAssetsNotes.HistoricalCostBreakdown.RevaluedClassPronoun.ShouldBe("Revalued Class Pronoun");
            note.TangibleFixedAssetsNotes.HistoricalCostBreakdown.CurrentReportingPeriodAccumulatedDepreciation.ShouldBe(1.33m);
            note.TangibleFixedAssetsNotes.HistoricalCostBreakdown.CurrentReportingPeriodCost.ShouldBe(1.45m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems.Count.ShouldBe(3);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].LandAndBuildings.ShouldBe(1.1m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].PlantAndMachineryEtc.ShouldBe(2.2m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].Year.ShouldBe(2020);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].Index.ShouldBe(3);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].LandAndBuildings.ShouldBe(3.3m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].PlantAndMachineryEtc.ShouldBe(4.4m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].Year.ShouldBe(2021);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].Index.ShouldBe(4);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].LandAndBuildings.ShouldBe(-3.3m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].PlantAndMachineryEtc.ShouldBe(-4.4m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].Year.ShouldBe(2022);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].Index.ShouldBe(5);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.CostLandAndBuildings.ShouldBe(5.5m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.CostPlantAndMachineryEtc.ShouldBe(6.6m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.TotalLandAndBuildings.ShouldBe(10);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.TotalPlantAndMachineryEtc.ShouldBe(11);
        }

        [Fact]
        public void Should_map_notes_from_previousPeriod_when_currentPeriod_data_is_unavailable()
        {
            var note = new Notes
            {
                PreviousPeriod = _previousNotesData
            };

            note.AverageNumberOfEmployees.CurrentPeriod.ShouldBeNull();
            note.AverageNumberOfEmployees.PreviousPeriod.ShouldBe(_previousNotesData.AverageNumberOfEmployees.CurrentPeriod);
            note.OffBalanceSheetArrangements.ShouldBe(_previousNotesData.OffBalanceSheetArrangements);
            note.AdvancesCreditAndGuaranteesGrantedToDirectors.ShouldBeNull();
            note.GuaranteesAndOtherFinancialCommitments.ShouldBe(_previousNotesData.GuaranteesAndOtherFinancialCommitments);
            note.RelatedPartyTransactions.ShouldBe(_previousNotesData.RelatedPartyTransactions);
            note.LoansAndOtherDebtsDueToMembers.ShouldBe(_previousNotesData.LoansAndOtherDebtsDueToMembers);
            note.AdditionalNote1.NoteTitle.ShouldBe(_previousNotesData.AdditionalNote1.NoteTitle);
            note.AdditionalNote1.NoteText.ShouldBe(_previousNotesData.AdditionalNote1.NoteText);
            note.AdditionalNote2.NoteTitle.ShouldBe(_previousNotesData.AdditionalNote2.NoteTitle);
            note.AdditionalNote2.NoteText.ShouldBe(_previousNotesData.AdditionalNote2.NoteText);
            note.ControllingPartyNote.ShouldBe(_previousNotesData.ControllingPartyNote);
            note.IntangibleAssetsRevaluation.ShouldBe(_previousNotesData.IntangibleAssetsRevaluation);
            note.OperatingProfitLoss.IsEnabled.ShouldBe(_previousNotesData.OperatingProfitLoss.IsEnabled);
            note.OperatingProfitLoss.Items[0].Index.ShouldBe(_previousNotesData.OperatingProfitLoss.Items[0].Index);
            note.OperatingProfitLoss.Items[0].Description.ShouldBe(_previousNotesData.OperatingProfitLoss.Items[0].Description);
            note.OperatingProfitLoss.Items[0].Value.ShouldBe(_previousNotesData.OperatingProfitLoss.Items[0].Value);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].Index.ShouldBe(1);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidFive);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].DirectorName.ShouldBe("Director1");
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtStartOfYear.ShouldBe(1100M);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsAdvanced.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsRepaid.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWaived.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWrittenOff.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtEndOfYear.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AdvanceCreditConditions.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].Index.ShouldBe(2);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidSix);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].DirectorName.ShouldBe("Director2");
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].BalanceOutstandingAtStartOfYear.ShouldBe(2200M);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsAdvanced.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsRepaid.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsWaived.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsWrittenOff.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].BalanceOutstandingAtEndOfYear.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AdvanceCreditConditions.ShouldBeNull();
            note.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.ValuationDetails.ShouldBe("Valuation Details previous");
            note.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.IndependentValuerInvolved.ShouldBeTrue();
            note.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.RevaluationBasis.ShouldBe("Revaluation Basis previous");
            note.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.DateOfRevaluation.ShouldBe(DateTime.MinValue);
            note.TangibleFixedAssetsNotes.HistoricalCostBreakdown.RevaluedAssetClass.ShouldBe("Revalued Asset Class");
            note.TangibleFixedAssetsNotes.HistoricalCostBreakdown.RevaluedClassPronoun.ShouldBe("Revalued Class Pronoun");
            note.TangibleFixedAssetsNotes.HistoricalCostBreakdown.CurrentReportingPeriodAccumulatedDepreciation.ShouldBe(1.33m);
            note.TangibleFixedAssetsNotes.HistoricalCostBreakdown.CurrentReportingPeriodCost.ShouldBe(1.45m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems.Count.ShouldBe(3);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].LandAndBuildings.ShouldBe(1.1m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].PlantAndMachineryEtc.ShouldBe(2.2m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].Year.ShouldBe(2019);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].Index.ShouldBe(3);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].LandAndBuildings.ShouldBe(3.3m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].PlantAndMachineryEtc.ShouldBe(4.4m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].Year.ShouldBe(2020);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].Index.ShouldBe(4);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].LandAndBuildings.ShouldBe(-3.3m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].PlantAndMachineryEtc.ShouldBe(-4.4m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].Year.ShouldBe(2021);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].Index.ShouldBe(5);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.CostLandAndBuildings.ShouldBe(5.5m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.CostPlantAndMachineryEtc.ShouldBe(6.6m);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.TotalLandAndBuildings.ShouldBe(11);
            note.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.TotalPlantAndMachineryEtc.ShouldBe(12);
        }

        [Fact]
        public void Should_not_map_advance_credit_guarantees_to_directors_notes_from_previousPeriod_when_currentPeriod_data_is_unavailable_and_balance_outstanding_at_end_of_year_is_0()
        {
            var note = new Notes
            {
                PreviousPeriod = new NotesData
                {
                    AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = new AdvancesCreditAndGuaranteesGrantedToDirectors
                    {
                        Guarantees = "Guarantees",
                        Items = new List<AdvancesCreditAndGuaranteesGrantedToDirectorItem>
                        {
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItem {
                                Index = 1,
                                InvolvementClientGuid = TestHelpers.Guids.GuidFive,
                                DirectorName = "Director1",
                                BalanceOutstandingAtStartOfYear = 1000M,
                                AmountsAdvanced = 100M,
                                AmountsRepaid = 0M,
                                AmountsWrittenOff = 0M,
                                AmountsWaived = 0M,
                                AdvanceCreditConditions = "Director1 AdvanceCreditConditions",
                                BalanceOutstandingAtEndOfYear = 1100M
                            },
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItem {
                                Index = 2,
                                InvolvementClientGuid = TestHelpers.Guids.GuidSix,
                                DirectorName = "Director2",
                                BalanceOutstandingAtStartOfYear = 2000M,
                                AmountsAdvanced = 200M,
                                AmountsRepaid = 2200M,
                                AmountsWrittenOff = 0M,
                                AmountsWaived = 0M,
                                AdvanceCreditConditions = "Director2 AdvanceCreditConditions",
                                BalanceOutstandingAtEndOfYear = 0
                            },
                        }
                    }
                }
            };

            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items.Count.ShouldBe(1);

            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].Index.ShouldBe(1);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidFive);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].DirectorName.ShouldBe("Director1");
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtStartOfYear.ShouldBe(1100M);
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsAdvanced.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsRepaid.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWaived.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWrittenOff.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtEndOfYear.ShouldBeNull();
            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AdvanceCreditConditions.ShouldBeNull();
        }

        [Fact]
        public void Should_have_empty_advance_credit_guarantees_to_directors_notes_items_when_currentPeriod_data_is_unavailable_and_all_directors_have_balance_outstanding_at_end_of_year_is_0_in_previousPeriod()
        {
            var note = new Notes
            {
                PreviousPeriod = new NotesData
                {
                    AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = new AdvancesCreditAndGuaranteesGrantedToDirectors
                    {
                        Guarantees = "Guarantees",
                        Items = new List<AdvancesCreditAndGuaranteesGrantedToDirectorItem>
                        {
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItem {
                                Index = 1,
                                InvolvementClientGuid = TestHelpers.Guids.GuidFive,
                                DirectorName = "Director1",
                                BalanceOutstandingAtStartOfYear = 1000M,
                                AmountsAdvanced = 100M,
                                AmountsRepaid = 1100M,
                                AmountsWrittenOff = 0M,
                                AmountsWaived = 0M,
                                AdvanceCreditConditions = "Director1 AdvanceCreditConditions",
                                BalanceOutstandingAtEndOfYear = 0
                            },
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItem {
                                Index = 2,
                                InvolvementClientGuid = TestHelpers.Guids.GuidSix,
                                DirectorName = "Director2",
                                BalanceOutstandingAtStartOfYear = 2000M,
                                AmountsAdvanced = 200M,
                                AmountsRepaid = 2200M,
                                AmountsWrittenOff = 0M,
                                AmountsWaived = 0M,
                                AdvanceCreditConditions = "Director2 AdvanceCreditConditions",
                                BalanceOutstandingAtEndOfYear = 0
                            },
                        }
                    }
                }
            };

            note.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items.Count.ShouldBe(0);
        }
    }
}
