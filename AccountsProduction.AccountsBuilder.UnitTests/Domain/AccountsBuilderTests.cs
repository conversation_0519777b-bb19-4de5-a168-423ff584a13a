﻿using AccountsProduction.AccountsBuilder.Domain;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Domain
{
    public class AccountsBuilderTests
    {
        [Fact]
        public void Should_set_entity_modification_time()
        {
            var entity = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder();

            entity.EntityModificationTime.ShouldBeLessThanOrEqualTo(DateTime.UtcNow);
            entity.ReportingStandard.ShouldNotBeNull();
        }

        [Fact]
        public void Should_set_all_properties()
        {
            var tenantId = TestHelpers.Guids.GuidOne;
            var clientId = Guid.NewGuid();
            var periodId = Guid.NewGuid();

            const string entitySize = "size1";
            const string independentReviewType = "Accountants";
            const string terminology = "term1";
            const string tradingStatus = "tradingStatus1";
            const string dormantStatus = "dormantStatus1";
            const string choiceOfStatement = "choiceOfStatement1";
            const string cic34Report = "Simple";
            const string charitySize = "Large";

            var reportingStandard = new ReportingStandard
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = "FRS 105",
                Version = ReportingStandardVersion.Full
            };

            var entitySetup = new EntitySetup
            {
                EntitySize = entitySize,
                IndependentReviewType = independentReviewType,
                Terminology = terminology,
                TradingStatus = tradingStatus,
                DormantStatus = dormantStatus,
                ChoiceOfStatement = choiceOfStatement,
                CIC34Report = cic34Report,
                CharitySize = charitySize
            };

            var entity = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId.ToString(), clientId, periodId, entitySetup, new LicenseData(), reportingStandard);

            entity.ProcessId = Guid.NewGuid();
            entity.LastSuccessfullProcessId = entity.ProcessId;
            entity.Status = Status.Successful;

            entity.EntityModificationTime.ShouldBeLessThanOrEqualTo(DateTime.UtcNow);
            entity.ReportingStandard.ShouldNotBeNull();
            entity.ClientId.ShouldBe(clientId);
            entity.TenantId.ShouldBe(tenantId);
            entity.PeriodId.ShouldBe(periodId);
            entity.EntitySetup.EntitySize.ShouldBe(entitySize);
            entity.EntitySetup.IndependentReviewType.ShouldBe(independentReviewType);
            entity.EntitySetup.Terminology.ShouldBe(terminology);
            entity.EntitySetup.TradingStatus.ShouldBe(tradingStatus);
            entity.EntitySetup.DormantStatus.ShouldBe(dormantStatus);
            entity.EntitySetup.ChoiceOfStatement.ShouldBe(choiceOfStatement);
            entity.EntitySetup.CIC34Report.ShouldBe(cic34Report);
            entity.EntitySetup.CharitySize.ShouldBe(charitySize);
            entity.ReportingStandard.Name.ShouldBe(reportingStandard.Name);
        }

        [Fact]
        public void Should_update_report_type()
        {
            var tenantId = TestHelpers.Guids.GuidOne.ToString();
            var clientId = Guid.NewGuid();
            var periodId = Guid.NewGuid();

            const string entitySize = "size1";
            const string independentReviewType = "Accountants";
            const string tradingStatus = "tradingStatus1";
            const string dormantStatus = "dormantStatus1";
            const string choiceOfStatement = "choiceOfStatement1";
            const string terminology = "term1";
            const string cic34Report = "Simple";
            const string charitySize = "charitySize";

            var reportingStandard = new ReportingStandard
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = "FRS 105",
                Version = ReportingStandardVersion.Full
            };

            var entitySetup = new EntitySetup
            {
                EntitySize = entitySize,
                IndependentReviewType = independentReviewType,
                Terminology = terminology,
                TradingStatus = tradingStatus,
                DormantStatus = dormantStatus,
                ChoiceOfStatement = choiceOfStatement,
                CIC34Report = cic34Report,
                CharitySize = charitySize
            };

            var entity =
                new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId, clientId, periodId, entitySetup, new LicenseData(), reportingStandard);

            var entitySetup2 = new EntitySetup
            {
                EntitySize = "size2",
                IndependentReviewType = "None",
                Terminology = "term2",
                TradingStatus = "tradingStatus2",
                DormantStatus = "dormantStatus2",
                ChoiceOfStatement = "choiceOfStatement2",
                CIC34Report = "Detailed",
                CharitySize = "charitySize"
            };

            entity.UpdateReportingStandard(reportingStandard);
            entity.AddEntitySetup(entitySetup2);
            entity.UpdateReportingStandard(reportingStandard);

            entity.ReportingStandard.ShouldBe(reportingStandard);
            entity.EntitySetup.ShouldBe(entitySetup2);
        }

        [Fact]
        public void Should_update_account_period()
        {
            // Arrange
            var tenantId = TestHelpers.Guids.GuidOne.ToString();
            var clientId = Guid.NewGuid();
            var periodId = Guid.NewGuid();

            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId, clientId, periodId);
            var accountPeriodMock = new Mock<AccountPeriod>();

            // Act
            accountsBuilder.UpdateAccountPeriod(accountPeriodMock.Object);

            // Assert
            Assert.Equal(accountPeriodMock.Object, accountsBuilder.AccountPeriod);
            Assert.Equal(DateTime.UtcNow, accountsBuilder.EntityModificationTime, TimeSpan.FromSeconds(1));
            Assert.True(accountsBuilder.IsDirty);
        }

        [Fact]
        public void Should_update_validations_false()
        {
            var tenantId = TestHelpers.Guids.GuidOne.ToString();
            var clientId = Guid.NewGuid();
            var periodId = Guid.NewGuid();

            var entity = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId, clientId, periodId)
            {
                TrialBalance = new TrialBalance
                {
                    EntityModificationTime = new DateTime(2021, 11, 1)
                }
            };

            entity.UpdateValidations(new ValidationData());

            entity.ValidationData.ShouldNotBeNull();
        }

        [Fact]
        public void Should_set_and_get_charity_size_property()
        {
            // Arrange
            const string expectedCharitySize = "Large";
            var entitySetup = new EntitySetup();

            // Act
            entitySetup.CharitySize = expectedCharitySize;

            // Assert
            entitySetup.CharitySize.ShouldBe(expectedCharitySize);
        }

        [Fact]
        public void Should_update_modification_time_when_entity_setup_is_updated()
        {
            // Arrange
            var tenantId = TestHelpers.Guids.GuidOne.ToString();
            var clientId = Guid.NewGuid();
            var periodId = Guid.NewGuid();
            var initialEntitySetup = new EntitySetup
            {
                EntitySize = "Small",
                CharitySize = "Small"
            };

            var entity = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId, clientId, periodId, initialEntitySetup, new LicenseData(), new ReportingStandard());

            var updatedEntitySetup = new EntitySetup
            {
                EntitySize = "Large",
                CharitySize = "Large"
            };

            var initialModificationTime = entity.EntityModificationTime;
            Thread.Sleep(10); // Ensure time difference

            // Act
            entity.AddEntitySetup(updatedEntitySetup);

            // Assert
            entity.EntitySetup.CharitySize.ShouldBe("Large");
            entity.EntityModificationTime.ShouldBeGreaterThan(initialModificationTime);
        }

    }
}