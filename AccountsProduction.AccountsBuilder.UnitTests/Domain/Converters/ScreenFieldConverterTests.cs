﻿using AccountsProduction.AccountsBuilder.Domain.DataScreens;
using Amazon.DynamoDBv2.DocumentModel;
using System.Reflection;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Domain.Converters;

public class ScreenFieldConverterTests
{
    private readonly PolymorphicConverter _converter = new PolymorphicConverter();

    [Fact]
    public void ToEntry_NullValue_ReturnsDynamoDBNull()
    {
        var result = _converter.ToEntry(null);
        Assert.IsType<DynamoDBNull>(result);
    }

    [Theory]
    [InlineData(123, "123")]
    [InlineData(123L, "123")]
    [InlineData(123.45f, "123.45")]
    [InlineData(123.45, "123.45")]
    public void ToEntry_NumericValues_ReturnsPrimitive(object value, string expected)
    {
        var result = _converter.ToEntry(value);
        var primitive = Assert.IsType<Primitive>(result);
        Assert.Equal(expected, primitive.Value);
    }

    [Fact]
    public void ToEntry_BooleanValue_ReturnsDynamoDBBool()
    {
        var result = _converter.ToEntry(true);
        var boolEntry = Assert.IsType<DynamoDBBool>(result);
        Assert.True(boolEntry.AsBoolean());
    }

    [Fact]
    public void ToEntry_StringValue_ReturnsPrimitive()
    {
        var result = _converter.ToEntry("test");
        var primitive = Assert.IsType<Primitive>(result);
        Assert.Equal("test", primitive.Value);
    }

    [Fact]
    public void ToEntry_JsonElementValue_ReturnsPrimitive()
    {
        var jsonElement = JsonDocument.Parse("\"test\"").RootElement;
        var result = _converter.ToEntry(jsonElement);
        var primitive = Assert.IsType<Primitive>(result);
        Assert.Equal("\"test\"", primitive.Value);
    }

    [Fact]
    public void FromEntry_DynamoDBNull_ReturnsNull()
    {
        var result = _converter.FromEntry(new DynamoDBNull());
        Assert.Null(result);
    }

    [Fact]
    public void FromEntry_DynamoDBBool_ReturnsBoolean()
    {
        var result = _converter.FromEntry(new DynamoDBBool(true));
        Assert.IsType<bool>(result);
        Assert.True((bool)result);
    }

    [Theory]
    [InlineData("123", 123.0)]
    [InlineData("123.45", 123.45)]
    public void FromEntry_PrimitiveNumeric_ReturnsDouble(string value, double expected)
    {
        var result = _converter.FromEntry(new Primitive(value, true));
        Assert.IsType<double>(result);
        Assert.Equal(expected, result);
    }

    [Fact]
    public void FromEntry_PrimitiveString_ReturnsString()
    {
        var result = _converter.FromEntry(new Primitive("test", false));
        Assert.IsType<string>(result);
        Assert.Equal("test", result);
    }

    [Fact]
    public void HandleJsonElement_NullValueKind_ReturnsDynamoDBNull()
    {
        var jsonElement = JsonDocument.Parse("null").RootElement;
        var result = InvokeHandleJsonElement(jsonElement);
        Assert.IsType<DynamoDBNull>(result);
    }

    [Fact]
    public void HandleJsonElement_NumberValueKind_ReturnsPrimitive()
    {
        var jsonElement = JsonDocument.Parse("123.45").RootElement;
        var result = InvokeHandleJsonElement(jsonElement);
        var primitive = Assert.IsType<Primitive>(result);
        Assert.Equal("123.45", primitive.Value);
    }

    [Fact]
    public void HandleJsonElement_StringValueKind_ReturnsPrimitive()
    {
        var jsonElement = JsonDocument.Parse("\"test\"").RootElement;
        var result = InvokeHandleJsonElement(jsonElement);
        var primitive = Assert.IsType<Primitive>(result);
        Assert.Equal("\"test\"", primitive.Value);
    }

    [Fact]
    public void HandleJsonElement_TrueValueKind_ReturnsDynamoDBBool()
    {
        var jsonElement = JsonDocument.Parse("true").RootElement;
        var result = InvokeHandleJsonElement(jsonElement);
        var boolEntry = Assert.IsType<DynamoDBBool>(result);
        Assert.True(boolEntry.AsBoolean());
    }

    [Fact]
    public void HandleJsonElement_FalseValueKind_ReturnsDynamoDBBool()
    {
        var jsonElement = JsonDocument.Parse("false").RootElement;
        var result = InvokeHandleJsonElement(jsonElement);
        var boolEntry = Assert.IsType<DynamoDBBool>(result);
        Assert.False(boolEntry.AsBoolean());
    }

    [Fact]
    public void HandleJsonElement_UnsupportedValueKind_ThrowsArgumentException()
    {
        var jsonElement = JsonDocument.Parse("{}").RootElement; // JsonObject is unsupported
        Assert.Throws<TargetInvocationException>(() => InvokeHandleJsonElement(jsonElement));
    }

    private static DynamoDBEntry InvokeHandleJsonElement(JsonElement jsonElement)
    {
        var method = typeof(PolymorphicConverter).GetMethod("HandleJsonElement", BindingFlags.NonPublic | BindingFlags.Static);
        return (DynamoDBEntry)method.Invoke(null, new object[] { jsonElement });
    }
}
