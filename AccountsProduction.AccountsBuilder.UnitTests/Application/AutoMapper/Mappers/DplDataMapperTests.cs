﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Dpl;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AutoMapper;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.AutoMapper.Mappers
{
    public class DplDataMapperTests
    {
        private readonly IMapper _mapper;

        public DplDataMapperTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<DplDataMapper>();
            });
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Map_FundSectorInfo_MapsCorrectly()
        {
            // Arrange
            var sectorId = Guid.NewGuid();
            var fundSector = new SectorInfoDto
            {
                Id = sectorId,
                DisplayId = "FUND123",
                Type = "Fund",
                SubType = "Restricted",
                Order = 1
            };
            var src = new PeriodTrialBalanceDto
            {
                CharityOperationIds =
                [
                    sectorId
                ]
            };
            var sectors = new List<SectorInfoDto> { fundSector };

            // Act
            var result = _mapper.Map<PeriodTrialBalanceDto, DplRequestDataDto>(src, opt => opt.Items[nameof(TrialBalanceDto.SectorsInformation)] = sectors);

            // Assert
            Assert.Equal(sectorId, result.FundId);
        }

        [Fact]
        public void Map_ActivitySectorInfo_MapsCorrectly()
        {
            // Arrange
            var sectorId = Guid.NewGuid();
            var activitySector = new SectorInfoDto
            {
                Id = sectorId,
                DisplayId = "ACT456",
                Type = "Activity",
                SubType = "Charitable",
                Order = 2
            };
            var src = new PeriodTrialBalanceDto
            {
                CharityOperationIds =
                [
                   sectorId
                ]
            };
            var sectors = new List<SectorInfoDto> { activitySector };

            // Act
            var result = _mapper.Map<PeriodTrialBalanceDto, DplRequestDataDto>(src, opt => opt.Items[nameof(TrialBalanceDto.SectorsInformation)] = sectors);

            // Assert
            Assert.Equal(sectorId, result.ActivityId);
        }

        [Fact]
        public void Map_GrantSectorInfo_MapsCorrectly()
        {
            // Arrange
            var sectorId = Guid.NewGuid();
            var grantSector = new SectorInfoDto
            {
                Id = sectorId,
                DisplayId = "GRANT789",
                Type = "Grant",
                SubType = "Incoming",
                Order = 3
            };
            var src = new PeriodTrialBalanceDto
            {
                CharityOperationIds =
                [
                    sectorId
                ]
            };
            var sectors = new List<SectorInfoDto> { grantSector };

            // Act
            var result = _mapper.Map<PeriodTrialBalanceDto, DplRequestDataDto>(src, opt => opt.Items[nameof(TrialBalanceDto.SectorsInformation)] = sectors);

            // Assert
            Assert.Equal(sectorId, result.GrantId);

        }

        [Fact]
        public void Map_FundAndActivitySectorInfo_MapsCorrectly()
        {
            // Arrange
            var fundId = Guid.NewGuid();
            var activityid = Guid.NewGuid();

            var fundSector = new SectorInfoDto
            {
                Id = fundId,
                DisplayId = "FUND123",
                Type = "Fund",
                SubType = "Restricted",
                Order = 1
            };
            var activitySector = new SectorInfoDto
            {
                Id = activityid,
                DisplayId = "ACT456",
                Type = "Activity",
                SubType = "Charitable",
                Order = 2
            };
            var src = new PeriodTrialBalanceDto
            {
                CharityOperationIds =
                [
                    fundId,
                    activityid
                ]
            };
            var sectors = new List<SectorInfoDto> { fundSector, activitySector };

            // Act
            var result = _mapper.Map<PeriodTrialBalanceDto, DplRequestDataDto>(src, opt => opt.Items[nameof(TrialBalanceDto.SectorsInformation)] = sectors);

            // Assert
            Assert.Equal(fundId, result.FundId);
            Assert.Equal(activityid, result.ActivityId);

        }

        [Fact]
        public void Map_NoMatchingSectorInfo_ReturnsNullsOrDefaults()
        {
            // Arrange
            var src = new PeriodTrialBalanceDto
            {
                SectorId = Guid.NewGuid()
            };
            var sectors = new List<SectorInfoDto>(); // No matching sector

            // Act
            var result = _mapper.Map<PeriodTrialBalanceDto, DplRequestDataDto>(src, opt => opt.Items[nameof(TrialBalanceDto.SectorsInformation)] = sectors);

            // Assert
            Assert.Null(result.FundId);
            Assert.Null(result.ActivityId);
            Assert.Null(result.GrantId);
        }
    }
}