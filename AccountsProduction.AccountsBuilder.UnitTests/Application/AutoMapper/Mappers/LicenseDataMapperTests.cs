﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.AutoMapper.Mappers
{
    public class LicenseDataMapperTests
    {

        private readonly IMapper _mapper;
        public LicenseDataMapperTests()
        {
            var mapperConfig = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<LicenseDataMapper>();
            }
            );

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();
        }

        [Theory]
        [InlineData("Active", null)]
        [InlineData("Trial", "IRIS Elements Accounts Production Trial Version")]
        public void Should_map_licensedata_to_licensedatadto(string licenseStatus, string watermarkText)
        {
            var isTrialAPLicense = licenseStatus.Equals("Trial");

            var licenseData = new LicenseData(isTrialAPLicense);

            var result = _mapper.Map<LicenseDataDto>(licenseData);

            result.IsTrial.ShouldBe(isTrialAPLicense);
            result.WatermarkText.ShouldBe(watermarkText);
        }

        [Theory]
        [InlineData("Active", null)]
        [InlineData("Trial", "IRIS Elements Accounts Production Trial Version")]
        public void Should_map_licensedatadto_to_licensedata(string licenseStatus, string watermarkText)
        {
            var isTrialAPLicense = licenseStatus.Equals("Trial");

            var licenseData = new LicenseDataDto
            {
                IsTrial = isTrialAPLicense
            };

            var result = _mapper.Map<LicenseData>(licenseData);

            result.IsTrial.ShouldBe(isTrialAPLicense);
            result.WatermarkText.ShouldBe(watermarkText);
        }
    }
}
