﻿using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;
using Shouldly;
using Xunit;
using ExemptionsSectionEnum = AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels.ExemptionsSectionEnum;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.AutoMapper.Mappers
{
    public class AccountingPoliciesMapperTests
    {
        private readonly IMapper _mapper;

        public AccountingPoliciesMapperTests()
        {
            var mapperConfig = new MapperConfiguration(cfg => { cfg.AddProfile<AccountingPoliciesMapper>(); });

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();
        }

        [Theory]
        [InlineData(ReportStandardType.FRS102_1A)]
        [InlineData(ReportStandardType.FRS102)]
        [InlineData(ReportStandardType.UNINCORPORATED)]
        [InlineData(default)]
        public void Should_map_correct_accounting_policies_when_current_period_accounting_policies_exists(string reportType)
        {
            var accountingPoliciesResponseMessage = new AccountingPoliciesResponseMessage
            {
                PreviousPeriodId = TestHelpers.Guids.GuidOne,
                PeriodId = TestHelpers.Guids.GuidTwo,
                ClientId = TestHelpers.Guids.GuidThree,
                TenantId = TestHelpers.Guids.GuidFour,
                CorrelationId = TestHelpers.Guids.GuidFour,
                CurrentPeriodAccountingPolicies = new AccountingPoliciesResponseDataMessage
                {
                    ExemptionsFinancialStatements = new ExemptionsFinancialStatementsMessage
                    {
                        ParentAddress = TestData.CurrentExemptionsParentAddress,
                        ParentName = TestData.CurrentExemptionsParentName,
                        Section = ExemptionsFinancialStatementsEnum.Section_400
                    },
                    ChangesInAccountingPolicies = TestData.CurrentChangesInAccountingPolicies,
                    FinancialInstrumentsAccountingPolicy = TestData.CurrentFinancialInstrumentsAccountingPolicy,
                    GovernmentGrantsAccountingPolicy = TestData.CurrentGovernmentGrantsAccountingPolicy,
                    MembersTransactionsWithTheLlpText = TestData.CurrentMembersTransactionsWithTheLlpText,
                    TangibleFixedAssets = new TangibleFixedAssetsMessage
                    {
                        PlantAndMachinery = new PlantAndMachineriesMessage
                        {
                            PlantAndMachinery = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.CurrentPlantAndMachineryAlternativeBasis,
                                CategoryDescription = TestData.CurrentPlantAndMachineryCategoryDescription,
                                ReducingBalanceBasis = 1,
                                StraightLineBasis = 1
                            },
                            ComputerEquipment = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.CurrentComputerEquipmentAlternativeBasis,
                                CategoryDescription = TestData.CurrentComputerEquipmentCategoryDescription,
                                ReducingBalanceBasis = 2,
                                StraightLineBasis = 2
                            },
                            FixturesAndFittings = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.CurrentFixturesAndFittingsAlternativeBasis,
                                CategoryDescription = TestData.CurrentFixturesAndFittingsCategoryDescription,
                                ReducingBalanceBasis = 3,
                                StraightLineBasis = 3
                            },
                            ImprovementsToProperty = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.CurrentImprovementsToPropertyAlternativeBasis,
                                CategoryDescription = TestData.CurrentImprovementsToPropertyCategoryDescription,
                                ReducingBalanceBasis = 4,
                                StraightLineBasis = 4
                            },
                            MotorVehicles = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.CurrentMotorVehiclesAlternativeBasis,
                                CategoryDescription = TestData.CurrentMotorVehiclesCategoryDescription,
                                ReducingBalanceBasis = 5,
                                StraightLineBasis = 5
                            }
                        },
                        LandAndBuildings = new LandAndBuildingsMessage
                        {
                            FreeholdProperty = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.CurrentFreeholdPropertyAlternativeBasis,
                                CategoryDescription = TestData.CurrentFreeholdPropertyCategoryDescription,
                                ReducingBalanceBasis = 6,
                                StraightLineBasis = 6
                            },
                            ShortLeaseholdProperty = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.CurrentShortLeaseholdPropertyAlternativeBasis,
                                CategoryDescription = TestData.CurrentShortLeaseholdPropertyCategoryDescription,
                                ReducingBalanceBasis = 7,
                                StraightLineBasis = 7
                            },
                            LongLeaseholdProperty = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.CurrentLongLeaseholdPropertyAlternativeBasis,
                                CategoryDescription = TestData.CurrentLongLeaseholdPropertyCategoryDescription,
                                ReducingBalanceBasis = 8,
                                StraightLineBasis = 8
                            }
                        }
                    },
                    IntangibleAssets = new IntangibleAssetsMessage
                    {
                        Goodwill = new AssetsAdjustmentMessage
                        {
                            CategoryDescription = TestData.CurrentGoodwillCategoryDescription,
                            AlternativeBasis = TestData.CurrentGoodwillAlternativeBasis,
                            ReducingBalanceBasis = 1,
                            StraightLineBasis = 1
                        },
                        PatentsAndLicenses = new AssetsAdjustmentMessage
                        {
                            CategoryDescription = TestData.CurrentPatentsAndLicensesCategoryDescription,
                            AlternativeBasis = TestData.CurrentPatentsAndLicensesAlternativeBasis,
                            ReducingBalanceBasis = 2,
                            StraightLineBasis = 2
                        },
                        DevelopmentCosts = new AssetsAdjustmentMessage
                        {
                            CategoryDescription = TestData.CurrentDevelopmentCostsCategoryDescription,
                            AlternativeBasis = TestData.CurrentDevelopmentCostsAlternativeBasis,
                            ReducingBalanceBasis = 3,
                            StraightLineBasis = 3
                        },
                        ComputerSoftware = new AssetsAdjustmentMessage
                        {
                            CategoryDescription = TestData.CurrentComputerSoftwareCategoryDescription,
                            AlternativeBasis = TestData.CurrentComputerSoftwareAlternativeBasis,
                            ReducingBalanceBasis = 4,
                            StraightLineBasis = 4
                        }
                    }
                },
                IsSuccessful = true
            };

            var mappedAccountingPolicies = _mapper.Map<AccountingPolicies>(accountingPoliciesResponseMessage, opt => opt.Items["ReportType"] = reportType);
            mappedAccountingPolicies.ClientId.ShouldBe(TestHelpers.Guids.GuidThree);
            mappedAccountingPolicies.PeriodId.ShouldBe(TestHelpers.Guids.GuidTwo);
            mappedAccountingPolicies.TenantId.ShouldBe(TestHelpers.Guids.GuidFour);
            mappedAccountingPolicies.PreviousPeriodId.ShouldBe(TestHelpers.Guids.GuidOne);
            mappedAccountingPolicies.CorrelationId.ShouldBe(TestHelpers.Guids.GuidFour);
            mappedAccountingPolicies.IsSuccessful.ShouldBe(true);
            mappedAccountingPolicies.Error.ShouldBeNull();

            switch (reportType)
            {
                case ReportStandardType.FRS102_1A:
                case ReportStandardType.FRS102:
                    mappedAccountingPolicies.PreviousPeriodAccountingPolicies.ShouldBeNull();
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ShouldNotBeNull();
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ExemptionsFinancialStatements.ParentName.ShouldBe(TestData.CurrentExemptionsParentName);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ExemptionsFinancialStatements.ParentAddress.ShouldBe(TestData.CurrentExemptionsParentAddress);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ExemptionsFinancialStatements.Section.ShouldBe(ExemptionsSectionEnum.Section_400);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.FinancialInstrumentsAccountingPolicy.ShouldBe(TestData.CurrentFinancialInstrumentsAccountingPolicy);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ChangesInAccountingPolicies.ShouldBe(TestData.CurrentChangesInAccountingPolicies);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.GovernmentGrantsAccountingPolicy.ShouldBe(TestData.CurrentGovernmentGrantsAccountingPolicy);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.MembersTransactionsWithTheLlpText.ShouldBe(TestData.CurrentMembersTransactionsWithTheLlpText);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.CategoryDescription.ShouldBe(TestData.CurrentPlantAndMachineryCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.AlternativeBasis.ShouldBe(TestData.CurrentPlantAndMachineryAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.ReducingBalanceBasis.ShouldBe(1);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.StraightLineBasis.ShouldBe(1);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.CategoryDescription.ShouldBe(TestData.CurrentComputerEquipmentCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.AlternativeBasis.ShouldBe(TestData.CurrentComputerEquipmentAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.ReducingBalanceBasis.ShouldBe(2);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.StraightLineBasis.ShouldBe(2);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.CategoryDescription.ShouldBe(TestData.CurrentFixturesAndFittingsCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.AlternativeBasis.ShouldBe(TestData.CurrentFixturesAndFittingsAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.ReducingBalanceBasis.ShouldBe(3);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.StraightLineBasis.ShouldBe(3);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.CategoryDescription.ShouldBe(TestData.CurrentImprovementsToPropertyCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.AlternativeBasis.ShouldBe(TestData.CurrentImprovementsToPropertyAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.ReducingBalanceBasis.ShouldBe(4);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.StraightLineBasis.ShouldBe(4);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.CategoryDescription.ShouldBe(TestData.CurrentMotorVehiclesCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.AlternativeBasis.ShouldBe(TestData.CurrentMotorVehiclesAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.ReducingBalanceBasis.ShouldBe(5);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.StraightLineBasis.ShouldBe(5);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.CategoryDescription.ShouldBe(TestData.CurrentFreeholdPropertyCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.AlternativeBasis.ShouldBe(TestData.CurrentFreeholdPropertyAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.ReducingBalanceBasis.ShouldBe(6);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.StraightLineBasis.ShouldBe(6);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.CategoryDescription.ShouldBe(TestData.CurrentShortLeaseholdPropertyCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.AlternativeBasis.ShouldBe(TestData.CurrentShortLeaseholdPropertyAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.ReducingBalanceBasis.ShouldBe(7);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.StraightLineBasis.ShouldBe(7);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.CategoryDescription.ShouldBe(TestData.CurrentLongLeaseholdPropertyCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.AlternativeBasis.ShouldBe(TestData.CurrentLongLeaseholdPropertyAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.ReducingBalanceBasis.ShouldBe(8);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.StraightLineBasis.ShouldBe(8);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.Goodwill.CategoryDescription.ShouldBe(TestData.CurrentGoodwillCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.Goodwill.AlternativeBasis.ShouldBe(TestData.CurrentGoodwillAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.Goodwill.ReducingBalanceBasis.ShouldBe(1);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.Goodwill.StraightLineBasis.ShouldBe(1);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.CategoryDescription.ShouldBe(TestData.CurrentPatentsAndLicensesCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.AlternativeBasis.ShouldBe(TestData.CurrentPatentsAndLicensesAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.ReducingBalanceBasis.ShouldBe(2);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.StraightLineBasis.ShouldBe(2);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.CategoryDescription.ShouldBe(TestData.CurrentDevelopmentCostsCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.AlternativeBasis.ShouldBe(TestData.CurrentDevelopmentCostsAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.ReducingBalanceBasis.ShouldBe(3);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.StraightLineBasis.ShouldBe(3);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.CategoryDescription.ShouldBe(TestData.CurrentComputerSoftwareCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.AlternativeBasis.ShouldBe(TestData.CurrentComputerSoftwareAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.ReducingBalanceBasis.ShouldBe(4);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.StraightLineBasis.ShouldBe(4);
                    mappedAccountingPolicies.ExemptionsFinancialStatements.ParentName.ShouldBe(TestData.CurrentExemptionsParentName);
                    mappedAccountingPolicies.ExemptionsFinancialStatements.ParentAddress.ShouldBe(TestData.CurrentExemptionsParentAddress);
                    mappedAccountingPolicies.ExemptionsFinancialStatements.Section.ShouldBe(ExemptionsSectionEnum.Section_400);
                    mappedAccountingPolicies.FinancialInstrumentsAccountingPolicy.ShouldBe(TestData.CurrentFinancialInstrumentsAccountingPolicy);
                    mappedAccountingPolicies.GovernmentGrantsAccountingPolicy.ShouldBe(TestData.CurrentGovernmentGrantsAccountingPolicy);
                    mappedAccountingPolicies.MembersTransactionsWithTheLlpText.ShouldBe(TestData.CurrentMembersTransactionsWithTheLlpText);
                    mappedAccountingPolicies.ChangesInAccountingPolicies.ShouldBe(TestData.CurrentChangesInAccountingPolicies);
                    mappedAccountingPolicies.TangibleAssetsPlantAndMachinery.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery);
                    mappedAccountingPolicies.TangibleAssetsComputerEquipment.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment);
                    mappedAccountingPolicies.TangibleAssetsFixturesAndFittings.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings);
                    mappedAccountingPolicies.TangibleAssetsImprovementsToProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty);
                    mappedAccountingPolicies.TangibleAssetsMotorVehicles.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles);
                    mappedAccountingPolicies.TangibleAssetsFreeholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty);
                    mappedAccountingPolicies.TangibleAssetsShortLeaseholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty);
                    mappedAccountingPolicies.TangibleAssetsLongLeaseholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty);
                    mappedAccountingPolicies.IntangibleAssetsGoodwill.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.Goodwill);
                    mappedAccountingPolicies.IntangibleAssetsPatentsAndLicenses.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses);
                    mappedAccountingPolicies.IntangibleAssetsDevelopmentCosts.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts);
                    mappedAccountingPolicies.IntangibleAssetsComputerSoftware.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware);
                    break;
                case ReportStandardType.UNINCORPORATED:
                    mappedAccountingPolicies.PreviousPeriodAccountingPolicies.ShouldBeNull();
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ShouldNotBeNull();
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ExemptionsFinancialStatements.ShouldBeNull();
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ExemptionsFinancialStatements.ShouldBeNull();
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ExemptionsFinancialStatements.ShouldBeNull();
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.FinancialInstrumentsAccountingPolicy.ShouldBeNull();
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ChangesInAccountingPolicies.ShouldBeNull();
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.GovernmentGrantsAccountingPolicy.ShouldBeNull();
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.MembersTransactionsWithTheLlpText.ShouldBeNull();
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.CategoryDescription.ShouldBe(TestData.CurrentPlantAndMachineryCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.AlternativeBasis.ShouldBe(TestData.CurrentPlantAndMachineryAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.ReducingBalanceBasis.ShouldBe(1);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.StraightLineBasis.ShouldBe(1);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.CategoryDescription.ShouldBe(TestData.CurrentComputerEquipmentCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.AlternativeBasis.ShouldBe(TestData.CurrentComputerEquipmentAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.ReducingBalanceBasis.ShouldBe(2);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.StraightLineBasis.ShouldBe(2);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.CategoryDescription.ShouldBe(TestData.CurrentFixturesAndFittingsCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.AlternativeBasis.ShouldBe(TestData.CurrentFixturesAndFittingsAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.ReducingBalanceBasis.ShouldBe(3);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.StraightLineBasis.ShouldBe(3);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.CategoryDescription.ShouldBe(TestData.CurrentImprovementsToPropertyCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.AlternativeBasis.ShouldBe(TestData.CurrentImprovementsToPropertyAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.ReducingBalanceBasis.ShouldBe(4);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.StraightLineBasis.ShouldBe(4);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.CategoryDescription.ShouldBe(TestData.CurrentMotorVehiclesCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.AlternativeBasis.ShouldBe(TestData.CurrentMotorVehiclesAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.ReducingBalanceBasis.ShouldBe(5);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.StraightLineBasis.ShouldBe(5);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.CategoryDescription.ShouldBe(TestData.CurrentFreeholdPropertyCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.AlternativeBasis.ShouldBe(TestData.CurrentFreeholdPropertyAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.ReducingBalanceBasis.ShouldBe(6);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.StraightLineBasis.ShouldBe(6);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.CategoryDescription.ShouldBe(TestData.CurrentShortLeaseholdPropertyCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.AlternativeBasis.ShouldBe(TestData.CurrentShortLeaseholdPropertyAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.ReducingBalanceBasis.ShouldBe(7);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.StraightLineBasis.ShouldBe(7);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.CategoryDescription.ShouldBe(TestData.CurrentLongLeaseholdPropertyCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.AlternativeBasis.ShouldBe(TestData.CurrentLongLeaseholdPropertyAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.ReducingBalanceBasis.ShouldBe(8);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.StraightLineBasis.ShouldBe(8);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ShouldBeNull();
                    mappedAccountingPolicies.ExemptionsFinancialStatements.ShouldBeNull();
                    mappedAccountingPolicies.FinancialInstrumentsAccountingPolicy.ShouldBeNull();
                    mappedAccountingPolicies.GovernmentGrantsAccountingPolicy.ShouldBeNull();
                    mappedAccountingPolicies.MembersTransactionsWithTheLlpText.ShouldBeNull();
                    mappedAccountingPolicies.ChangesInAccountingPolicies.ShouldBeNull();
                    mappedAccountingPolicies.TangibleAssetsPlantAndMachinery.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery);
                    mappedAccountingPolicies.TangibleAssetsComputerEquipment.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment);
                    mappedAccountingPolicies.TangibleAssetsFixturesAndFittings.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings);
                    mappedAccountingPolicies.TangibleAssetsImprovementsToProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty);
                    mappedAccountingPolicies.TangibleAssetsMotorVehicles.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles);
                    mappedAccountingPolicies.TangibleAssetsFreeholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty);
                    mappedAccountingPolicies.TangibleAssetsShortLeaseholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty);
                    mappedAccountingPolicies.TangibleAssetsLongLeaseholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty);
                    mappedAccountingPolicies.IntangibleAssetsGoodwill.ShouldBeNull();
                    mappedAccountingPolicies.IntangibleAssetsPatentsAndLicenses.ShouldBeNull();
                    mappedAccountingPolicies.IntangibleAssetsDevelopmentCosts.ShouldBeNull();
                    mappedAccountingPolicies.IntangibleAssetsComputerSoftware.ShouldBeNull();
                    break;
                default:
                    mappedAccountingPolicies.PreviousPeriodAccountingPolicies.ShouldBeNull();
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ShouldNotBeNull();
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ExemptionsFinancialStatements.ParentName.ShouldBe(TestData.CurrentExemptionsParentName);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ExemptionsFinancialStatements.ParentAddress.ShouldBe(TestData.CurrentExemptionsParentAddress);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ExemptionsFinancialStatements.Section.ShouldBe(ExemptionsSectionEnum.Section_400);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.FinancialInstrumentsAccountingPolicy.ShouldBe(TestData.CurrentFinancialInstrumentsAccountingPolicy);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ChangesInAccountingPolicies.ShouldBe(TestData.CurrentChangesInAccountingPolicies);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.GovernmentGrantsAccountingPolicy.ShouldBe(TestData.CurrentGovernmentGrantsAccountingPolicy);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.MembersTransactionsWithTheLlpText.ShouldBe(TestData.CurrentMembersTransactionsWithTheLlpText);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.CategoryDescription.ShouldBe(TestData.CurrentPlantAndMachineryCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.AlternativeBasis.ShouldBe(TestData.CurrentPlantAndMachineryAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.ReducingBalanceBasis.ShouldBe(1);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.StraightLineBasis.ShouldBe(1);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.CategoryDescription.ShouldBe(TestData.CurrentComputerEquipmentCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.AlternativeBasis.ShouldBe(TestData.CurrentComputerEquipmentAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.ReducingBalanceBasis.ShouldBe(2);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.StraightLineBasis.ShouldBe(2);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.CategoryDescription.ShouldBe(TestData.CurrentFixturesAndFittingsCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.AlternativeBasis.ShouldBe(TestData.CurrentFixturesAndFittingsAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.ReducingBalanceBasis.ShouldBe(3);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.StraightLineBasis.ShouldBe(3);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.CategoryDescription.ShouldBe(TestData.CurrentImprovementsToPropertyCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.AlternativeBasis.ShouldBe(TestData.CurrentImprovementsToPropertyAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.ReducingBalanceBasis.ShouldBe(4);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.StraightLineBasis.ShouldBe(4);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.CategoryDescription.ShouldBe(TestData.CurrentMotorVehiclesCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.AlternativeBasis.ShouldBe(TestData.CurrentMotorVehiclesAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.ReducingBalanceBasis.ShouldBe(5);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.StraightLineBasis.ShouldBe(5);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.CategoryDescription.ShouldBe(TestData.CurrentFreeholdPropertyCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.AlternativeBasis.ShouldBe(TestData.CurrentFreeholdPropertyAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.ReducingBalanceBasis.ShouldBe(6);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.StraightLineBasis.ShouldBe(6);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.CategoryDescription.ShouldBe(TestData.CurrentShortLeaseholdPropertyCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.AlternativeBasis.ShouldBe(TestData.CurrentShortLeaseholdPropertyAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.ReducingBalanceBasis.ShouldBe(7);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.StraightLineBasis.ShouldBe(7);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.CategoryDescription.ShouldBe(TestData.CurrentLongLeaseholdPropertyCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.AlternativeBasis.ShouldBe(TestData.CurrentLongLeaseholdPropertyAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.ReducingBalanceBasis.ShouldBe(8);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.StraightLineBasis.ShouldBe(8);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.Goodwill.CategoryDescription.ShouldBe(TestData.CurrentGoodwillCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.Goodwill.AlternativeBasis.ShouldBe(TestData.CurrentGoodwillAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.Goodwill.ReducingBalanceBasis.ShouldBe(1);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.Goodwill.StraightLineBasis.ShouldBe(1);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.CategoryDescription.ShouldBe(TestData.CurrentPatentsAndLicensesCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.AlternativeBasis.ShouldBe(TestData.CurrentPatentsAndLicensesAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.ReducingBalanceBasis.ShouldBe(2);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.StraightLineBasis.ShouldBe(2);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.CategoryDescription.ShouldBe(TestData.CurrentDevelopmentCostsCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.AlternativeBasis.ShouldBe(TestData.CurrentDevelopmentCostsAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.ReducingBalanceBasis.ShouldBe(3);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.StraightLineBasis.ShouldBe(3);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.CategoryDescription.ShouldBe(TestData.CurrentComputerSoftwareCategoryDescription);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.AlternativeBasis.ShouldBe(TestData.CurrentComputerSoftwareAlternativeBasis);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.ReducingBalanceBasis.ShouldBe(4);
                    mappedAccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.StraightLineBasis.ShouldBe(4);
                    mappedAccountingPolicies.ExemptionsFinancialStatements.ParentName.ShouldBe(TestData.CurrentExemptionsParentName);
                    mappedAccountingPolicies.ExemptionsFinancialStatements.ParentAddress.ShouldBe(TestData.CurrentExemptionsParentAddress);
                    mappedAccountingPolicies.ExemptionsFinancialStatements.Section.ShouldBe(ExemptionsSectionEnum.Section_400);
                    mappedAccountingPolicies.FinancialInstrumentsAccountingPolicy.ShouldBe(TestData.CurrentFinancialInstrumentsAccountingPolicy);
                    mappedAccountingPolicies.GovernmentGrantsAccountingPolicy.ShouldBe(TestData.CurrentGovernmentGrantsAccountingPolicy);
                    mappedAccountingPolicies.MembersTransactionsWithTheLlpText.ShouldBe(TestData.CurrentMembersTransactionsWithTheLlpText);
                    mappedAccountingPolicies.ChangesInAccountingPolicies.ShouldBe(TestData.CurrentChangesInAccountingPolicies);
                    mappedAccountingPolicies.TangibleAssetsPlantAndMachinery.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery);
                    mappedAccountingPolicies.TangibleAssetsComputerEquipment.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment);
                    mappedAccountingPolicies.TangibleAssetsFixturesAndFittings.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings);
                    mappedAccountingPolicies.TangibleAssetsImprovementsToProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty);
                    mappedAccountingPolicies.TangibleAssetsMotorVehicles.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles);
                    mappedAccountingPolicies.TangibleAssetsFreeholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty);
                    mappedAccountingPolicies.TangibleAssetsShortLeaseholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty);
                    mappedAccountingPolicies.TangibleAssetsLongLeaseholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty);
                    mappedAccountingPolicies.IntangibleAssetsGoodwill.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.Goodwill);
                    mappedAccountingPolicies.IntangibleAssetsPatentsAndLicenses.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses);
                    mappedAccountingPolicies.IntangibleAssetsDevelopmentCosts.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts);
                    mappedAccountingPolicies.IntangibleAssetsComputerSoftware.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware);
                    break;
            }
        }


        [Theory]
        [InlineData(ReportStandardType.FRS102_1A)]
        [InlineData(ReportStandardType.FRS102)]
        [InlineData(ReportStandardType.UNINCORPORATED)]
        [InlineData(default)]
        public void Should_map_correct_accounting_policies_when_previous_period_accounting_policies_exists(string reportType)
        {
            var accountingPolicy = new AccountingPoliciesResponseMessage
            {
                PreviousPeriodId = TestHelpers.Guids.GuidOne,
                PeriodId = TestHelpers.Guids.GuidTwo,
                ClientId = TestHelpers.Guids.GuidThree,
                TenantId = TestHelpers.Guids.GuidFour,
                CorrelationId = TestHelpers.Guids.GuidFour,
                PreviousPeriodAccountingPolicies = new AccountingPoliciesResponseDataMessage
                {
                    ExemptionsFinancialStatements = new ExemptionsFinancialStatementsMessage
                    {
                        ParentAddress = TestData.PreviousExemptionsParentAddress,
                        ParentName = TestData.PreviousExemptionsParentName,
                        Section = ExemptionsFinancialStatementsEnum.Section_400
                    },
                    ChangesInAccountingPolicies = TestData.PreviousChangesInAccountingPolicies,
                    FinancialInstrumentsAccountingPolicy = TestData.PreviousFinancialInstrumentsAccountingPolicy,
                    GovernmentGrantsAccountingPolicy = TestData.PreviousGovernmentGrantsAccountingPolicy,
                    MembersTransactionsWithTheLlpText = TestData.PreviousMembersTransactionsWithTheLlpText,
                    TangibleFixedAssets = new TangibleFixedAssetsMessage
                    {
                        PlantAndMachinery = new PlantAndMachineriesMessage
                        {
                            PlantAndMachinery = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.PreviousPlantAndMachineryAlternativeBasis,
                                CategoryDescription = TestData.PreviousPlantAndMachineryCategoryDescription,
                                ReducingBalanceBasis = 11,
                                StraightLineBasis = 11
                            },
                            ComputerEquipment = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.PreviousComputerEquipmentAlternativeBasis,
                                CategoryDescription = TestData.PreviousComputerEquipmentCategoryDescription,
                                ReducingBalanceBasis = 22,
                                StraightLineBasis = 22
                            },
                            FixturesAndFittings = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.PreviousFixturesAndFittingsAlternativeBasis,
                                CategoryDescription = TestData.PreviousFixturesAndFittingsCategoryDescription,
                                ReducingBalanceBasis = 33,
                                StraightLineBasis = 33
                            },
                            ImprovementsToProperty = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.PreviousImprovementsToPropertyAlternativeBasis,
                                CategoryDescription = TestData.PreviousImprovementsToPropertyCategoryDescription,
                                ReducingBalanceBasis = 44,
                                StraightLineBasis = 44
                            },
                            MotorVehicles = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.PreviousMotorVehiclesAlternativeBasis,
                                CategoryDescription = TestData.PreviousMotorVehiclesCategoryDescription,
                                ReducingBalanceBasis = 55,
                                StraightLineBasis = 55
                            }
                        },
                        LandAndBuildings = new LandAndBuildingsMessage
                        {
                            FreeholdProperty = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.PreviousFreeholdPropertyAlternativeBasis,
                                CategoryDescription = TestData.PreviousFreeholdPropertyCategoryDescription,
                                ReducingBalanceBasis = 66,
                                StraightLineBasis = 66
                            },
                            ShortLeaseholdProperty = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.PreviousShortLeaseholdPropertyAlternativeBasis,
                                CategoryDescription = TestData.PreviousShortLeaseholdPropertyCategoryDescription,
                                ReducingBalanceBasis = 77,
                                StraightLineBasis = 77
                            },
                            LongLeaseholdProperty = new AssetsAdjustmentMessage
                            {
                                AlternativeBasis = TestData.PreviousLongLeaseholdPropertyAlternativeBasis,
                                CategoryDescription = TestData.PreviousLongLeaseholdPropertyCategoryDescription,
                                ReducingBalanceBasis = 88,
                                StraightLineBasis = 88
                            }
                        }
                    },
                    IntangibleAssets = new IntangibleAssetsMessage
                    {
                        Goodwill = new AssetsAdjustmentMessage
                        {
                            CategoryDescription = TestData.PreviousGoodwillCategoryDescription,
                            AlternativeBasis = TestData.PreviousGoodwillAlternativeBasis,
                            ReducingBalanceBasis = 11,
                            StraightLineBasis = 11
                        },
                        PatentsAndLicenses = new AssetsAdjustmentMessage
                        {
                            CategoryDescription = TestData.PreviousPatentsAndLicensesCategoryDescription,
                            AlternativeBasis = TestData.PreviousPatentsAndLicensesAlternativeBasis,
                            ReducingBalanceBasis = 22,
                            StraightLineBasis = 22
                        },
                        DevelopmentCosts = new AssetsAdjustmentMessage
                        {
                            CategoryDescription = TestData.PreviousDevelopmentCostsCategoryDescription,
                            AlternativeBasis = TestData.PreviousDevelopmentCostsAlternativeBasis,
                            ReducingBalanceBasis = 33,
                            StraightLineBasis = 33
                        },
                        ComputerSoftware = new AssetsAdjustmentMessage
                        {
                            CategoryDescription = TestData.PreviousComputerSoftwareCategoryDescription,
                            AlternativeBasis = TestData.PreviousComputerSoftwareAlternativeBasis,
                            ReducingBalanceBasis = 44,
                            StraightLineBasis = 44
                        }
                    }
                },
                IsSuccessful = true
            };

            var mappedAccountingPolicy = _mapper.Map<AccountingPolicies>(accountingPolicy, opt => opt.Items["ReportType"] = reportType);
            mappedAccountingPolicy.ClientId.ShouldBe(TestHelpers.Guids.GuidThree);
            mappedAccountingPolicy.PeriodId.ShouldBe(TestHelpers.Guids.GuidTwo);
            mappedAccountingPolicy.TenantId.ShouldBe(TestHelpers.Guids.GuidFour);
            mappedAccountingPolicy.PreviousPeriodId.ShouldBe(TestHelpers.Guids.GuidOne);
            mappedAccountingPolicy.IsSuccessful.ShouldBe(true);
            mappedAccountingPolicy.Error.ShouldBeNull();

            switch (reportType)
            {
                case ReportStandardType.FRS102_1A:
                case ReportStandardType.FRS102:
                    mappedAccountingPolicy.CurrentPeriodAccountingPolicies.ShouldBeNull();
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.ShouldNotBeNull();
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.ExemptionsFinancialStatements.ParentName.ShouldBe(TestData.PreviousExemptionsParentName);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.ExemptionsFinancialStatements.ParentAddress.ShouldBe(TestData.PreviousExemptionsParentAddress);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.ExemptionsFinancialStatements.Section.ShouldBe(ExemptionsSectionEnum.Section_400);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.FinancialInstrumentsAccountingPolicy.ShouldBe(TestData.PreviousFinancialInstrumentsAccountingPolicy);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.ChangesInAccountingPolicies.ShouldBe(TestData.PreviousChangesInAccountingPolicies);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.GovernmentGrantsAccountingPolicy.ShouldBe(TestData.PreviousGovernmentGrantsAccountingPolicy);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.MembersTransactionsWithTheLlpText.ShouldBe(TestData.PreviousMembersTransactionsWithTheLlpText);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.CategoryDescription.ShouldBe(TestData.PreviousPlantAndMachineryCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.AlternativeBasis.ShouldBe(TestData.PreviousPlantAndMachineryAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.ReducingBalanceBasis.ShouldBe(11);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.StraightLineBasis.ShouldBe(11);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.CategoryDescription.ShouldBe(TestData.PreviousComputerEquipmentCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.AlternativeBasis.ShouldBe(TestData.PreviousComputerEquipmentAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.ReducingBalanceBasis.ShouldBe(22);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.StraightLineBasis.ShouldBe(22);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.CategoryDescription.ShouldBe(TestData.PreviousFixturesAndFittingsCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.AlternativeBasis.ShouldBe(TestData.PreviousFixturesAndFittingsAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.ReducingBalanceBasis.ShouldBe(33);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.StraightLineBasis.ShouldBe(33);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.CategoryDescription.ShouldBe(TestData.PreviousImprovementsToPropertyCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.AlternativeBasis.ShouldBe(TestData.PreviousImprovementsToPropertyAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.ReducingBalanceBasis.ShouldBe(44);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.StraightLineBasis.ShouldBe(44);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.CategoryDescription.ShouldBe(TestData.PreviousMotorVehiclesCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.AlternativeBasis.ShouldBe(TestData.PreviousMotorVehiclesAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.ReducingBalanceBasis.ShouldBe(55);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.StraightLineBasis.ShouldBe(55);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.CategoryDescription.ShouldBe(TestData.PreviousFreeholdPropertyCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.AlternativeBasis.ShouldBe(TestData.PreviousFreeholdPropertyAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.ReducingBalanceBasis.ShouldBe(66);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.StraightLineBasis.ShouldBe(66);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.CategoryDescription.ShouldBe(TestData.PreviousShortLeaseholdPropertyCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.AlternativeBasis.ShouldBe(TestData.PreviousShortLeaseholdPropertyAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.ReducingBalanceBasis.ShouldBe(77);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.StraightLineBasis.ShouldBe(77);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.CategoryDescription.ShouldBe(TestData.PreviousLongLeaseholdPropertyCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.AlternativeBasis.ShouldBe(TestData.PreviousLongLeaseholdPropertyAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.ReducingBalanceBasis.ShouldBe(88);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.StraightLineBasis.ShouldBe(88);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.Goodwill.CategoryDescription.ShouldBe(TestData.PreviousGoodwillCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.Goodwill.AlternativeBasis.ShouldBe(TestData.PreviousGoodwillAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.Goodwill.ReducingBalanceBasis.ShouldBe(11);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.Goodwill.StraightLineBasis.ShouldBe(11);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.CategoryDescription.ShouldBe(TestData.PreviousPatentsAndLicensesCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.AlternativeBasis.ShouldBe(TestData.PreviousPatentsAndLicensesAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.ReducingBalanceBasis.ShouldBe(22);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.StraightLineBasis.ShouldBe(22);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.CategoryDescription.ShouldBe(TestData.PreviousDevelopmentCostsCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.AlternativeBasis.ShouldBe(TestData.PreviousDevelopmentCostsAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.ReducingBalanceBasis.ShouldBe(33);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.StraightLineBasis.ShouldBe(33);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.CategoryDescription.ShouldBe(TestData.PreviousComputerSoftwareCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.AlternativeBasis.ShouldBe(TestData.PreviousComputerSoftwareAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.ReducingBalanceBasis.ShouldBe(44);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.StraightLineBasis.ShouldBe(44);
                    mappedAccountingPolicy.FinancialInstrumentsAccountingPolicy.ShouldBe(TestData.PreviousFinancialInstrumentsAccountingPolicy);
                    mappedAccountingPolicy.GovernmentGrantsAccountingPolicy.ShouldBe(TestData.PreviousGovernmentGrantsAccountingPolicy);
                    mappedAccountingPolicy.MembersTransactionsWithTheLlpText.ShouldBe(TestData.PreviousMembersTransactionsWithTheLlpText);
                    mappedAccountingPolicy.ChangesInAccountingPolicies.ShouldBeNull();
                    mappedAccountingPolicy.TangibleAssetsPlantAndMachinery.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery);
                    mappedAccountingPolicy.TangibleAssetsComputerEquipment.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment);
                    mappedAccountingPolicy.TangibleAssetsFixturesAndFittings.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings);
                    mappedAccountingPolicy.TangibleAssetsImprovementsToProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty);
                    mappedAccountingPolicy.TangibleAssetsMotorVehicles.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles);
                    mappedAccountingPolicy.TangibleAssetsFreeholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty);
                    mappedAccountingPolicy.TangibleAssetsShortLeaseholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty);
                    mappedAccountingPolicy.TangibleAssetsLongLeaseholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty);
                    mappedAccountingPolicy.IntangibleAssetsGoodwill.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.IntangibleAssets.Goodwill);
                    mappedAccountingPolicy.IntangibleAssetsPatentsAndLicenses.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses);
                    mappedAccountingPolicy.IntangibleAssetsDevelopmentCosts.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts);
                    mappedAccountingPolicy.IntangibleAssetsComputerSoftware.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware);
                    break;
                case ReportStandardType.UNINCORPORATED:
                    mappedAccountingPolicy.CurrentPeriodAccountingPolicies.ShouldBeNull();
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.ShouldNotBeNull();
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.ExemptionsFinancialStatements.ShouldBeNull();
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.FinancialInstrumentsAccountingPolicy.ShouldBeNull();
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.ChangesInAccountingPolicies.ShouldBeNull();
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.GovernmentGrantsAccountingPolicy.ShouldBeNull();
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.MembersTransactionsWithTheLlpText.ShouldBeNull();
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.CategoryDescription.ShouldBe(TestData.PreviousPlantAndMachineryCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.AlternativeBasis.ShouldBe(TestData.PreviousPlantAndMachineryAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.ReducingBalanceBasis.ShouldBe(11);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.StraightLineBasis.ShouldBe(11);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.CategoryDescription.ShouldBe(TestData.PreviousComputerEquipmentCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.AlternativeBasis.ShouldBe(TestData.PreviousComputerEquipmentAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.ReducingBalanceBasis.ShouldBe(22);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.StraightLineBasis.ShouldBe(22);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.CategoryDescription.ShouldBe(TestData.PreviousFixturesAndFittingsCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.AlternativeBasis.ShouldBe(TestData.PreviousFixturesAndFittingsAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.ReducingBalanceBasis.ShouldBe(33);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.StraightLineBasis.ShouldBe(33);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.CategoryDescription.ShouldBe(TestData.PreviousImprovementsToPropertyCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.AlternativeBasis.ShouldBe(TestData.PreviousImprovementsToPropertyAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.ReducingBalanceBasis.ShouldBe(44);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.StraightLineBasis.ShouldBe(44);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.CategoryDescription.ShouldBe(TestData.PreviousMotorVehiclesCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.AlternativeBasis.ShouldBe(TestData.PreviousMotorVehiclesAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.ReducingBalanceBasis.ShouldBe(55);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.StraightLineBasis.ShouldBe(55);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.CategoryDescription.ShouldBe(TestData.PreviousFreeholdPropertyCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.AlternativeBasis.ShouldBe(TestData.PreviousFreeholdPropertyAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.ReducingBalanceBasis.ShouldBe(66);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.StraightLineBasis.ShouldBe(66);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.CategoryDescription.ShouldBe(TestData.PreviousShortLeaseholdPropertyCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.AlternativeBasis.ShouldBe(TestData.PreviousShortLeaseholdPropertyAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.ReducingBalanceBasis.ShouldBe(77);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.StraightLineBasis.ShouldBe(77);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.CategoryDescription.ShouldBe(TestData.PreviousLongLeaseholdPropertyCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.AlternativeBasis.ShouldBe(TestData.PreviousLongLeaseholdPropertyAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.ReducingBalanceBasis.ShouldBe(88);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.StraightLineBasis.ShouldBe(88);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.ShouldBeNull();
                    mappedAccountingPolicy.FinancialInstrumentsAccountingPolicy.ShouldBeNull();
                    mappedAccountingPolicy.GovernmentGrantsAccountingPolicy.ShouldBeNull();
                    mappedAccountingPolicy.MembersTransactionsWithTheLlpText.ShouldBeNull();
                    mappedAccountingPolicy.ChangesInAccountingPolicies.ShouldBeNull();
                    mappedAccountingPolicy.TangibleAssetsPlantAndMachinery.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery);
                    mappedAccountingPolicy.TangibleAssetsComputerEquipment.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment);
                    mappedAccountingPolicy.TangibleAssetsFixturesAndFittings.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings);
                    mappedAccountingPolicy.TangibleAssetsImprovementsToProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty);
                    mappedAccountingPolicy.TangibleAssetsMotorVehicles.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles);
                    mappedAccountingPolicy.TangibleAssetsFreeholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty);
                    mappedAccountingPolicy.TangibleAssetsShortLeaseholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty);
                    mappedAccountingPolicy.TangibleAssetsLongLeaseholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty);
                    mappedAccountingPolicy.IntangibleAssetsGoodwill.ShouldBeNull();
                    break;
                default:
                    mappedAccountingPolicy.CurrentPeriodAccountingPolicies.ShouldBeNull();
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.ShouldNotBeNull();
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.ExemptionsFinancialStatements.ParentName.ShouldBe(TestData.PreviousExemptionsParentName);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.ExemptionsFinancialStatements.ParentAddress.ShouldBe(TestData.PreviousExemptionsParentAddress);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.ExemptionsFinancialStatements.Section.ShouldBe(ExemptionsSectionEnum.Section_400);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.FinancialInstrumentsAccountingPolicy.ShouldBe(TestData.PreviousFinancialInstrumentsAccountingPolicy);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.ChangesInAccountingPolicies.ShouldBe(TestData.PreviousChangesInAccountingPolicies);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.GovernmentGrantsAccountingPolicy.ShouldBe(TestData.PreviousGovernmentGrantsAccountingPolicy);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.MembersTransactionsWithTheLlpText.ShouldBe(TestData.PreviousMembersTransactionsWithTheLlpText);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.CategoryDescription.ShouldBe(TestData.PreviousPlantAndMachineryCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.AlternativeBasis.ShouldBe(TestData.PreviousPlantAndMachineryAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.ReducingBalanceBasis.ShouldBe(11);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.StraightLineBasis.ShouldBe(11);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.CategoryDescription.ShouldBe(TestData.PreviousComputerEquipmentCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.AlternativeBasis.ShouldBe(TestData.PreviousComputerEquipmentAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.ReducingBalanceBasis.ShouldBe(22);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.StraightLineBasis.ShouldBe(22);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.CategoryDescription.ShouldBe(TestData.PreviousFixturesAndFittingsCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.AlternativeBasis.ShouldBe(TestData.PreviousFixturesAndFittingsAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.ReducingBalanceBasis.ShouldBe(33);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.StraightLineBasis.ShouldBe(33);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.CategoryDescription.ShouldBe(TestData.PreviousImprovementsToPropertyCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.AlternativeBasis.ShouldBe(TestData.PreviousImprovementsToPropertyAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.ReducingBalanceBasis.ShouldBe(44);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.StraightLineBasis.ShouldBe(44);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.CategoryDescription.ShouldBe(TestData.PreviousMotorVehiclesCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.AlternativeBasis.ShouldBe(TestData.PreviousMotorVehiclesAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.ReducingBalanceBasis.ShouldBe(55);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.StraightLineBasis.ShouldBe(55);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.CategoryDescription.ShouldBe(TestData.PreviousFreeholdPropertyCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.AlternativeBasis.ShouldBe(TestData.PreviousFreeholdPropertyAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.ReducingBalanceBasis.ShouldBe(66);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.StraightLineBasis.ShouldBe(66);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.CategoryDescription.ShouldBe(TestData.PreviousShortLeaseholdPropertyCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.AlternativeBasis.ShouldBe(TestData.PreviousShortLeaseholdPropertyAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.ReducingBalanceBasis.ShouldBe(77);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.StraightLineBasis.ShouldBe(77);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.CategoryDescription.ShouldBe(TestData.PreviousLongLeaseholdPropertyCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.AlternativeBasis.ShouldBe(TestData.PreviousLongLeaseholdPropertyAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.ReducingBalanceBasis.ShouldBe(88);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.StraightLineBasis.ShouldBe(88);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.Goodwill.CategoryDescription.ShouldBe(TestData.PreviousGoodwillCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.Goodwill.AlternativeBasis.ShouldBe(TestData.PreviousGoodwillAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.Goodwill.ReducingBalanceBasis.ShouldBe(11);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.Goodwill.StraightLineBasis.ShouldBe(11);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.CategoryDescription.ShouldBe(TestData.PreviousPatentsAndLicensesCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.AlternativeBasis.ShouldBe(TestData.PreviousPatentsAndLicensesAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.ReducingBalanceBasis.ShouldBe(22);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.StraightLineBasis.ShouldBe(22);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.CategoryDescription.ShouldBe(TestData.PreviousDevelopmentCostsCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.AlternativeBasis.ShouldBe(TestData.PreviousDevelopmentCostsAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.ReducingBalanceBasis.ShouldBe(33);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.StraightLineBasis.ShouldBe(33);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.CategoryDescription.ShouldBe(TestData.PreviousComputerSoftwareCategoryDescription);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.AlternativeBasis.ShouldBe(TestData.PreviousComputerSoftwareAlternativeBasis);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.ReducingBalanceBasis.ShouldBe(44);
                    mappedAccountingPolicy.PreviousPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.StraightLineBasis.ShouldBe(44);
                    mappedAccountingPolicy.FinancialInstrumentsAccountingPolicy.ShouldBe(TestData.PreviousFinancialInstrumentsAccountingPolicy);
                    mappedAccountingPolicy.GovernmentGrantsAccountingPolicy.ShouldBe(TestData.PreviousGovernmentGrantsAccountingPolicy);
                    mappedAccountingPolicy.MembersTransactionsWithTheLlpText.ShouldBe(TestData.PreviousMembersTransactionsWithTheLlpText);
                    mappedAccountingPolicy.ChangesInAccountingPolicies.ShouldBeNull();
                    mappedAccountingPolicy.TangibleAssetsPlantAndMachinery.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery);
                    mappedAccountingPolicy.TangibleAssetsComputerEquipment.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment);
                    mappedAccountingPolicy.TangibleAssetsFixturesAndFittings.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings);
                    mappedAccountingPolicy.TangibleAssetsImprovementsToProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty);
                    mappedAccountingPolicy.TangibleAssetsMotorVehicles.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles);
                    mappedAccountingPolicy.TangibleAssetsFreeholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty);
                    mappedAccountingPolicy.TangibleAssetsShortLeaseholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty);
                    mappedAccountingPolicy.TangibleAssetsLongLeaseholdProperty.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty);
                    mappedAccountingPolicy.IntangibleAssetsGoodwill.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.IntangibleAssets.Goodwill);
                    mappedAccountingPolicy.IntangibleAssetsPatentsAndLicenses.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses);
                    mappedAccountingPolicy.IntangibleAssetsDevelopmentCosts.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts);
                    mappedAccountingPolicy.IntangibleAssetsComputerSoftware.ShouldBeEquivalentTo(TestData.AbFRS1021ACompleted.AccountingPolicies.PreviousPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware);

                    break;
            }
        }

        [Fact]
        public void Should_map_no_accounting_policies_when_no_accounting_policies_exists()
        {
            var accountingPolicies = new AccountingPoliciesResponseMessage
            {
                PreviousPeriodId = TestHelpers.Guids.GuidOne,
                PeriodId = TestHelpers.Guids.GuidTwo,
                ClientId = TestHelpers.Guids.GuidThree,
                TenantId = TestHelpers.Guids.GuidFour,
                CorrelationId = TestHelpers.Guids.GuidFour,
                IsSuccessful = false,
                Error = "error"
            };

            var mappedAccountingPolicies = _mapper.Map<AccountingPolicies>(accountingPolicies);
            mappedAccountingPolicies.ClientId.ShouldBe(TestHelpers.Guids.GuidThree);
            mappedAccountingPolicies.PeriodId.ShouldBe(TestHelpers.Guids.GuidTwo);
            mappedAccountingPolicies.TenantId.ShouldBe(TestHelpers.Guids.GuidFour);
            mappedAccountingPolicies.PreviousPeriodId.ShouldBe(TestHelpers.Guids.GuidOne);
            mappedAccountingPolicies.CorrelationId.ShouldBe(TestHelpers.Guids.GuidFour);
            mappedAccountingPolicies.IsSuccessful.ShouldBe(false);
            mappedAccountingPolicies.Error.ShouldNotBeNull("error");
            mappedAccountingPolicies.CurrentPeriodAccountingPolicies.ShouldBeNull();
            mappedAccountingPolicies.PreviousPeriodAccountingPolicies.ShouldBeNull();
        }
    }
}
