﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Signatory;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.AutoMapper.Mappers
{
    public class SignatoryMapperTests
    {
        private readonly IMapper _mapper;

        public SignatoryMapperTests()
        {
            var mapperConfig = new MapperConfiguration(cfg => { cfg.AddProfile<SignatoryMapper>(); });

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();
        }

        [Fact]
        public void Should_convert_to_signatory_dto()
        {
            var clientId = TestHelpers.Guids.GuidOne;
            var periodId = TestHelpers.Guids.GuidTwo;
            var signatory1Id = TestHelpers.Guids.GuidThree;
            var signatory2Id = TestHelpers.Guids.GuidFour;
            var signatory3Id = TestHelpers.Guids.GuidFive;

            var signatory = new Signatory
            {
                ClientId = clientId,
                EntityModificationTime = DateTime.Now,
                PeriodId = periodId,
                Signature = new SignatureDetail
                {
                    AccountantSigningDate = DateTime.Now,
                    IncludeAccountantsReport = true,
                    Signatures = new List<AccountsProduction.AccountsBuilder.Domain.Signature>
                    {
                        new AccountsProduction.AccountsBuilder.Domain.Signature
                            {
                                SignatoryId = signatory1Id,
                                OrderNumber = 1,
                                SigningDate = DateTime.Now,
                                SignatureType = SignatureType.BalanceSheet
                            },
                        new AccountsProduction.AccountsBuilder.Domain.Signature
                        {
                            SignatoryId = signatory2Id,
                            OrderNumber = 2,
                            SignatureType = SignatureType.DirectorsReport
                        },
                        new AccountsProduction.AccountsBuilder.Domain.Signature
                        {
                            SignatoryId = signatory3Id,
                            OrderNumber = 3,
                            SignatureType = SignatureType.CIC34Report
                        },
                        new Signature
                        {
                            SignatoryId = TestHelpers.Guids.GuidSix,
                            OrderNumber = 4,
                            SignatureType = SignatureType.SupplementaryNote
                        }
                    }
                }
            };

            var result = _mapper.Map<Signatory, SignatoryResponseDto>(signatory);

            result.ShouldNotBeNull();
            result.ShouldBeOfType(typeof(SignatoryResponseDto));
            result.ClientId.ShouldBe(signatory.ClientId);
            result.PeriodId.ShouldBe(signatory.PeriodId);
            result.Signatory.AccountantSigningDate.ShouldBe(signatory.Signature.AccountantSigningDate);
            result.Signatory.IncludeAccountantsReport.ShouldBe(signatory.Signature.IncludeAccountantsReport);

            var signatures = result.Signatory.Signatures;

            for (int i = 0; i < signatures.Count; i++)
            {
                signatures[i].SignatoryId.ShouldBe(signatory.Signature.Signatures[i].SignatoryId);
                signatures[i].SignatureType.ShouldBe(signatory.Signature.Signatures[i].SignatureType);
            }
        }

        [Fact]
        public void Should_convert_to_reporting_signatory_dto()
        {
            var signatory1Id = Guid.NewGuid();
            var signatory2Id = Guid.NewGuid();
            var signatory3Id = Guid.NewGuid();
            var signatory = new Signatory
            {
                Signature = new SignatureDetail
                {
                    AccountantSigningDate = DateTime.Now,
                    IncludeAccountantsReport = true,
                    Signatures = new List<AccountsProduction.AccountsBuilder.Domain.Signature>
                    {
                        new AccountsProduction.AccountsBuilder.Domain.Signature
                        {
                            SignatoryId = signatory1Id,
                            SignatureType = SignatureType.BalanceSheet,
                        },
                        new AccountsProduction.AccountsBuilder.Domain.Signature
                        {
                            SignatoryId = signatory2Id,
                            OrderNumber = 2,
                            SignatureType = SignatureType.DirectorsReport,
                        },
                        new AccountsProduction.AccountsBuilder.Domain.Signature
                        { 
                            SignatoryId = signatory3Id,
                            OrderNumber = 3,
                            SignatureType = SignatureType.CIC34Report
                        },
                        new AccountsProduction.AccountsBuilder.Domain.Signature
                        {
                            SignatoryId = TestHelpers.Guids.GuidSix,
                            OrderNumber = 4,
                            SignatureType = SignatureType.SupplementaryNote
                        }
                    }
                }
            };

            var result = _mapper.Map<SignatureDetail, ReportingSignatureDto>(signatory.Signature);

            result.ShouldNotBeNull();

            result.AccountantSigningDate.ShouldBe(signatory.Signature.AccountantSigningDate);
            result.IncludeAccountantsReport.ShouldBe(signatory.Signature.IncludeAccountantsReport);

            var signatures = result.Signatures;

            for (int i = 0; i < signatures.Count; i++)
            {
                signatures[i].SignatureType.ShouldBe(signatory.Signature.Signatures[i].SignatureType);
                signatures[i].InvolvementUUID.ShouldBe(signatory.Signature.Signatures[i].SignatoryId);
            }
        }
    }
}