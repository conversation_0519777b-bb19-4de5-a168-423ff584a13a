﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.AutoMapper.Mappers
{
    public class EntitySetupMapperTests
    {

        private readonly IMapper _mapper;
        public EntitySetupMapperTests()
        {
            var mapperConfig = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<EntitySetupMapper>();
            }
            );

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();
        }

        [Fact]
        public void Should_map_entitySetup_to_EntitySetupDto()
        {
            var entitySetup = new EntitySetup
            {
                EntitySize = "entitySize",
                IndependentReviewType = "independentReviewType",
                Terminology = "terminology",
                ChoiceOfStatement = "choiceOfStatement",
                DormantStatus = "dormantStatus",
                ReportingStandard = "reportingStandard",
                TradingStatus = "tradingStatus",
                CIC34Report = "Simple",
                PracticeAddress = "practiceAddress",
                CharitySize = "charitySize"
            };

            var result = _mapper.Map<EntitySetupDto>(entitySetup);

            result.EntitySize.ShouldBe(entitySetup.EntitySize);
            result.IndependentReviewType.ShouldBe(entitySetup.IndependentReviewType);
            result.Terminology.ShouldBe(entitySetup.Terminology);
            result.ChoiceOfStatement.ShouldBe(entitySetup.ChoiceOfStatement);
            result.DormantStatus.ShouldBe(entitySetup.DormantStatus);
            result.ReportingStandard.ShouldBe(entitySetup.ReportingStandard);
            result.TradingStatus.ShouldBe(entitySetup.TradingStatus);
            result.CIC34Report.ShouldBe(entitySetup.CIC34Report);
            result.PracticeAddress.ShouldBe(entitySetup.PracticeAddress);
            result.CharitySize.ShouldBe(entitySetup.CharitySize);
        }

        [Fact]
        public void Should_map_entitySetupDto_to_EntitySetup()
        {
            var entitySetup = new EntitySetupDto
            {
                EntitySize = "entitySize",
                IndependentReviewType = "independentReviewType",
                Terminology = "terminology",
                ChoiceOfStatement = "choiceOfStatement",
                DormantStatus = "dormantStatus",
                ReportingStandard = "reportingStandard",
                TradingStatus = "tradingStatus",
                CIC34Report = "Simple",
                PracticeAddress = "practiceAddress",
                CharitySize = "charitySize"
            };

            var result = _mapper.Map<EntitySetup>(entitySetup);

            result.EntitySize.ShouldBe(entitySetup.EntitySize);
            result.IndependentReviewType.ShouldBe(entitySetup.IndependentReviewType);
            result.Terminology.ShouldBe(entitySetup.Terminology);
            result.ChoiceOfStatement.ShouldBe(entitySetup.ChoiceOfStatement);
            result.DormantStatus.ShouldBe(entitySetup.DormantStatus);
            result.ReportingStandard.ShouldBe(entitySetup.ReportingStandard);
            result.TradingStatus.ShouldBe(entitySetup.TradingStatus);
            result.CIC34Report.ShouldBe(entitySetup.CIC34Report);
            result.PracticeAddress.ShouldBe(entitySetup.PracticeAddress);
            result.CharitySize.ShouldBe(entitySetup.CharitySize);
        }
    }
}
