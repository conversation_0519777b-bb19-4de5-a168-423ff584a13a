﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Domain;
using Amazon.DynamoDBv2.Model;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.Involvements;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.AggregationStrategies
{
    public class InvolvementsDataEventStrategyTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<ILogger<InvolvementsDataEventStrategy>> _logger;
        private readonly IMapper _mapper;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = Guid.NewGuid();
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;

        public InvolvementsDataEventStrategyTests()
        {
            var mapperConfig = new MapperConfiguration(cfg =>
                {
                    cfg.AddProfile<InvolvementMapper>();
                }
            );

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();

            _repository = new Mock<IAccountsBuilderRepository>();
            _logger = new Mock<ILogger<InvolvementsDataEventStrategy>>();
        }

        [Fact]
        public async Task Should_update_accounts_production_data()
        {
            _repository.Setup(repository => repository.GetAll(_clientId, CancellationToken.None))
                .ReturnsAsync(new List<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(){
                    new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId,
                    _periodId, null)
                });
            var requestMessage = GetRequestMessage();
            var strategy = new InvolvementsDataEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(
                repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Once);

        }

        [Fact]
        public async Task Should_not_update_accounts_production_data_if_concurreny_occures()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId);
            _repository.Setup(repository => repository.GetAll(_clientId, CancellationToken.None))
                .ReturnsAsync(new List<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(){
                    accountsBuilder
                });

            _repository.Setup(repository => repository.Get(It.IsAny<Guid>(), It.IsAny<Guid>(), CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            _repository.Setup(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None))
                .Callback(() => throw new ConditionalCheckFailedException("test"));

            var requestMessage = GetRequestMessage();
            var strategy = new InvolvementsDataEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(
                repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Exactly(4));

        }

        private string GetRequestMessage()
        {
            var message = JsonSerializer.Serialize(new List<InvolvementMessage>()
            {
                new InvolvementMessage
                {
                    PdoCode = 1,
                    InvolvementId = 1,
                    InvolvedClientDateOfDeath = DateTime.UtcNow,
                    InvolvedClientGuid = Guid.NewGuid(),
                    StartDate = DateTime.UtcNow.AddYears(-1),
                    InvolvementType = "Director"
                },
                new InvolvementMessage
                {
                    PdoCode = 2,
                    InvolvementId = 2,
                    InvolvedClientDateOfDeath = DateTime.UtcNow,
                    InvolvedClientGuid = Guid.NewGuid(),
                    StartDate = DateTime.UtcNow.AddYears(-1),
                    InvolvementType = "Director"
                }
            }, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return message;
        }
    }
}
