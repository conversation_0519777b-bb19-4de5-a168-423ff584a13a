﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Iris.AccountsProduction.AccountsBuilder.Messages.RoundingOptions;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.AggregationStrategies
{
    public class RoundingOptionsChangedEventStrategyTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<ILogger<RoundingOptionsChangedEventStrategy>> _logger;
        private readonly IMapper _mapper;
        private readonly Mock<UserContext> _userContext;
        private readonly Guid _clientId = Guid.NewGuid();
        private readonly Guid _periodId = Guid.NewGuid();
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;
        private readonly DateTime _periodStartDate = new DateTime(2022, 01, 01);
        private readonly DateTime _periodEndDate = new DateTime(2022, 12, 31);
        private readonly ReportingStandard _reportingStandard;
        private readonly EntitySetup _entitySetup;
        private readonly Mock<IDomainEventService> _domainEventService;

        public RoundingOptionsChangedEventStrategyTests()
        {
            var mapperConfig = new MapperConfiguration(cfg => { cfg.AddProfile<TrialBalanceMapper>(); });
            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();
            _repository = new Mock<IAccountsBuilderRepository>();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _logger = new Mock<ILogger<RoundingOptionsChangedEventStrategy>>();
            _userContext = new Mock<UserContext>();
            _userContext.Setup(q => q.TenantId).Returns(_tenantId.ToString());

            _entitySetup = new EntitySetup
            {
                EntitySize = "Small",
                IndependentReviewType = "Accountants",
                Terminology = "Companies Act",
                ReportingStandard = ReportStandardType.FRS102_1A
            };

            _reportingStandard = new ReportingStandard(Guid.NewGuid().ToString(),
                "dpl.accountsbuilder.frs105.process", "frs 102 full", ReportingStandardVersion.Full);

            _domainEventService = new Mock<IDomainEventService>();
        }

        [Fact]
        public async Task Should_trigger_dpl_calculations()
        {
            var ab = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId, _entitySetup, null, _reportingStandard)
            {
                TrialBalance = new TrialBalance()
                {
                    TrialBalances = new List<PeriodTrialBalance>()
                    {
                        new PeriodTrialBalance()
                        {
                            AccountCode = 1,
                            Amount = 10,
                            PeriodId = _periodId,
                            Description = "Test",
                            Year = _periodEndDate,
                        }
                    }
                }
            };

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
            .ReturnsAsync(ab);
            var requestMessage = GetRequestMessage();

            var strategy = new RoundingOptionsChangedEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _domainEventService.Verify(s => s.Publish(It.IsAny<IDomainEventNotification>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        private string GetRequestMessage()
        {
            var message = JsonSerializer.Serialize(new RoundingOptionsChangedNotificationMessage
            {
                ClientId = _clientId,
                PeriodId = _periodId,
                DateOccured = DateTime.Today,
                EventType = EventTypes.RoundingOptionsChangedEvent,
                TenantId = _tenantId

            }, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return message;
        }
    }
}