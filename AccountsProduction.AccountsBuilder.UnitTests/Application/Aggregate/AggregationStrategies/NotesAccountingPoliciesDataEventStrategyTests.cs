﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.AggregationStrategies
{
    public class NotesAccountingPoliciesDataEventStrategyTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<ILogger<NotesAccountingPoliciesDataEventStrategy>> _logger;
        private readonly IMapper _mapper;
        private readonly Mock<UserContext> _userContext;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = TestHelpers.Guids.GuidTwo;
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;

        public NotesAccountingPoliciesDataEventStrategyTests()
        {
            _repository = new Mock<IAccountsBuilderRepository>();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _logger = new Mock<ILogger<NotesAccountingPoliciesDataEventStrategy>>();
            _userContext = new Mock<UserContext>();
            _userContext.Setup(q => q.TenantId).Returns(_tenantId.ToString());
        }

        [Theory]
        [InlineData(ReportStandardType.FRS105)]
        [InlineData(ReportStandardType.FRS102_1A)]
        [InlineData(ReportStandardType.FRS102)]
        public async Task Should_update_accounting_policies_data(string reportType)
        {
            var reportingStandardDetail = new ReportingStandard
            {
                Id = "1",
                Name = reportType,
                Version = ReportingStandardVersion.Full,
                Type = reportType
            };

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId, null, null, reportingStandardDetail));

            var strategy = new NotesAccountingPoliciesDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Once);

        }

        [Theory]
        [InlineData(ReportStandardType.FRS105)]
        [InlineData(ReportStandardType.FRS102_1A)]
        [InlineData(ReportStandardType.FRS102)]
        public async Task Should_update_in_error_state(string reportType)
        {

            var reportingStandardDetail = new ReportingStandard
            {
                Id = "1",
                Name = reportType,
                Version = ReportingStandardVersion.Full,
                Type = reportType
            };

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId, null, null, reportingStandardDetail));

            var strategy = new NotesAccountingPoliciesDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetFailedRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Once);
        }

        [Fact]
        public async Task Should_not_update_when_no_process_found()
        {
            var strategy = new NotesAccountingPoliciesDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_not_update_when_invalid_tenant_process_found()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId));
            _userContext.Setup(q => q.TenantId).Returns(TestHelpers.Guids.GuidFive.ToString());
            var strategy = new NotesAccountingPoliciesDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_not_update_when_no_accounts_builder_process_found()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(() => null);
            var strategy = new NotesAccountingPoliciesDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);

        }

        [Fact]
        public async Task Should_not_update_when_no_accounts_builder_found()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(() => null);
            var strategy = new NotesAccountingPoliciesDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);

            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);

        }

        [Fact]
        public async Task Should_not_update_when_invalid_tenant_accounts_builder_found()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(TestHelpers.Guids.GuidFive.ToString(), _clientId, _periodId));

            _userContext.Setup(q => q.TenantId).Returns(TestHelpers.Guids.GuidFour.ToString());
            var strategy = new NotesAccountingPoliciesDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_throw_on_exception()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId));
            _repository.Setup(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None)).Throws<Exception>();
            var strategy = new NotesAccountingPoliciesDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await Should.ThrowAsync<Exception>(async () => { await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None); });
        }

        private string GetSuccessRequestMessage()
        {
            var message = new AccountingPoliciesResponseMessage
            {
                PreviousPeriodId = TestHelpers.Guids.GuidOne,
                PeriodId = _periodId,
                ClientId = _clientId,
                TenantId = TestHelpers.Guids.GuidFour,
                CorrelationId = TestHelpers.Guids.GuidFour,
                Error = "",
                IsSuccessful = true,
                CurrentPeriodAccountingPolicies = new AccountingPoliciesResponseDataMessage
                {
                    ExemptionsFinancialStatements = new ExemptionsFinancialStatementsMessage
                    {
                        ParentAddress = "parent address",
                        ParentName = "parent name",
                        Section = ExemptionsFinancialStatementsEnum.Section_399_2A
                    }
                }
            };

            return JsonSerializer.Serialize(message, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
        }

        private static string GetFailedRequestMessage()
        {
            var message = new AccountingPoliciesResponseMessage { IsSuccessful = false, Error = "error" };

            return JsonSerializer.Serialize(message, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
        }
    }
}
