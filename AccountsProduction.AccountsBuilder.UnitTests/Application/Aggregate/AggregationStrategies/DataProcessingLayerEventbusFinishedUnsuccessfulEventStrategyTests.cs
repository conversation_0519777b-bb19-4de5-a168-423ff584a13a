﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AutoMapper;
using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.Message;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.AggregationStrategies
{
    public class DataProcessingLayerEventbusFinishedUnsuccessfulEventStrategyTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<IDomainEventService> _domainEventService;
        private readonly Mock<ILogger<DataProcessingLayerEventbusFinishedUnsuccessfulEventStrategy>> _logger;
        private readonly Mock<UserContext> _userContext;
        private readonly IMapper _mapper;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = TestHelpers.Guids.GuidTwo;
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;

        public DataProcessingLayerEventbusFinishedUnsuccessfulEventStrategyTests()
        {
            var mapperConfig = new MapperConfiguration(cfg => { cfg.AddProfile<TrialBalanceMapper>(); });
            mapperConfig.AssertConfigurationIsValid();
            _repository = new Mock<IAccountsBuilderRepository>();
            _mapper = mapperConfig.CreateMapper();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _logger = new Mock<ILogger<DataProcessingLayerEventbusFinishedUnsuccessfulEventStrategy>>();
            _userContext = new Mock<UserContext>();
            _userContext.Setup(q => q.TenantId).Returns(_tenantId.ToString());
            _domainEventService = new Mock<IDomainEventService>();
        }

        [Fact]
        public async Task Should_update_financial_data_with_error_status_when_retry_is_reached()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, null!)
            {
                FinancialData = new FinancialData
                {
                    Status = (int)FinancialDataStatusDto.Error,
                    Retry = 3,
                    Financials = []
                }
            };
            _repository.Setup(repository => repository.Get(It.IsAny<Guid>(), CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            var requestMessage = GetFailedRequestMessage(_processId);
            var strategy = new DataProcessingLayerEventbusFinishedUnsuccessfulEventStrategy(_repository.Object, _logger.Object, _mapper, _domainEventService.Object);

            await strategy.ExecuteAsync(requestMessage);

            _repository.Verify(
                repository => repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(entity =>
                    entity.FinancialData.StatusCode == Status.Fail &&
                    entity.FinancialData.ErrorCode == Error.TechnicalError),
                    CancellationToken.None),
                Times.Once);

        }

        [Theory]
        [InlineData(ReportStandardType.FRS105)]
        [InlineData(ReportStandardType.FRS102_1A)]
        [InlineData(ReportStandardType.FRS102)]
        public async Task Should_retry_sending_event_to_dpl_if_error_and_retry_not_reached(string reportingStandard)
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, null!)
            {
                EntitySetup = new EntitySetup
                {
                    EntitySize = "Small",
                    Terminology = "Companies Act",
                    ReportingStandard = reportingStandard
                },
                FinancialData = new FinancialData
                {
                    Status = (int)FinancialDataStatusDto.Error,
                    Retry = 1,
                    Financials = []
                },
                TrialBalance = new TrialBalance
                {
                    ReportingPeriods =
                    [
                        new() { Id = Guid.NewGuid(), EndDate = new DateTime(2020, 1, 1) },
                        new() { Id = Guid.NewGuid(), EndDate = new DateTime(2021, 1, 1) }
                    ],
                    TrialBalances =
                    [
                        new()
                        {
                            AccountCode = 1,
                            Amount = 2000,
                            Description = "no description",
                            SubAccountCode = 1,
                            Year = DateTime.UtcNow
                        }
                    ],
                }
            };
            _repository.Setup(repository => repository.Get(It.IsAny<Guid>(), CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            var requestMessage = GetFailedRequestMessage(_processId);
            var strategy = new DataProcessingLayerEventbusFinishedUnsuccessfulEventStrategy(_repository.Object, _logger.Object, _mapper, _domainEventService.Object);

            await strategy.ExecuteAsync(requestMessage);

            _repository.Verify(
                repository => repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(entity =>
                    entity.FinancialData.Retry == 2),
                    CancellationToken.None),
                    Times.Once);

        }

        [Fact]
        public async Task Should_not_update_financial_data_when_no_process_found()
        {
            var requestMessage = GetFailedRequestMessage(_processId);
            var strategy = new DataProcessingLayerEventbusFinishedUnsuccessfulEventStrategy(_repository.Object, _logger.Object, _mapper, _domainEventService.Object);

            await strategy.ExecuteAsync(requestMessage);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_not_update_financial_data_when_no_accounts_builder_found()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => null!);
            var requestMessage = GetFailedRequestMessage(_processId);
            var strategy = new DataProcessingLayerEventbusFinishedUnsuccessfulEventStrategy(_repository.Object, _logger.Object, _mapper, _domainEventService.Object);

            await strategy.ExecuteAsync(requestMessage);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_throw_when_exception()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, null!)
            {
                FinancialData = new FinancialData
                {
                    Status = (int)FinancialDataStatusDto.Success,
                    Financials = []
                }
            };
            _repository.Setup(repository => repository.Get(It.IsAny<Guid>(), CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            _repository.Setup(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None)).Throws<Exception>();
            var requestMessage = GetFailedRequestMessage(_processId);
            var strategy = new DataProcessingLayerEventbusFinishedUnsuccessfulEventStrategy(_repository.Object, _logger.Object, _mapper, _domainEventService.Object);

            await Should.ThrowAsync<Exception>(async () => { await strategy.ExecuteAsync(requestMessage); });
        }

        private static EventBusMessage<ErrorFinancialDataEventbusDto> GetFailedRequestMessage(Guid processId)
        {
            var message = new EventBusMessage<ErrorFinancialDataEventbusDto>()
            {
                Payload = new ErrorFinancialDataEventbusDto
                {
                    ProcessId = processId,
                    ErrorMessage = new FinancialDataExceptionDetailsDto()
                    {
                        StatusCode = ExceptionDetailsStatusCode.Exception,
                        ExecutionStep = "some step",
                        Message = "some error"
                    }
                }
            };

            return message;
        }
    }
}