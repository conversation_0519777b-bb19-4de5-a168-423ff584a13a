﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.AggregationStrategies
{
    public class TrialBalanceDataEventStrategyTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<IDomainEventService> _domainEventService;
        private readonly Mock<ILogger<TrialBalanceDataEventStrategy>> _logger;
        private readonly IMapper _mapper;
        private readonly Mock<ISnsServiceClient> _snsServiceClient;
        private readonly Mock<UserContext> _userContext;
        private readonly Mock<IEnvVariableProvider> _envVariableProvider;
        private readonly Guid _clientId = Guid.NewGuid();
        private readonly Guid _periodId = Guid.NewGuid();
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;
        private readonly DateTime _periodStartDate = new DateTime(2022, 01, 01);
        private readonly DateTime _periodEndDate = new DateTime(2022, 12, 31);
        private readonly ReportingStandard _reportingStandard;
        private readonly EntitySetup _entitySetup;
        private readonly Mock<IAccountPeriodService> _accountPeriodService;

        public TrialBalanceDataEventStrategyTests()
        {
            var mapperConfig = new MapperConfiguration(cfg => { cfg.AddProfile<TrialBalanceMapper>(); });
            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();
            _repository = new Mock<IAccountsBuilderRepository>();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _logger = new Mock<ILogger<TrialBalanceDataEventStrategy>>();
            _snsServiceClient = new Mock<ISnsServiceClient>();
            _userContext = new Mock<UserContext>();
            _envVariableProvider = new Mock<IEnvVariableProvider>();
            _userContext.Setup(q => q.TenantId).Returns(_tenantId.ToString());

            _entitySetup = new EntitySetup
            {
                EntitySize = "Small",
                IndependentReviewType = "Accountants",
                Terminology = "Companies Act",
                ReportingStandard = ReportStandardType.FRS102_1A
            };

            _reportingStandard = new ReportingStandard(Guid.NewGuid().ToString(),
                "dpl.accountsbuilder.frs105.process", "frs 102 full", ReportingStandardVersion.Full);
            _domainEventService = new Mock<IDomainEventService>();
        }
        [Fact]
        public async Task Should_update_accounts_production_data()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, _entitySetup, null, _reportingStandard));
            var requestMessage = GetRequestMessage();

            var strategy = new TrialBalanceDataEventStrategy(_repository.Object, _logger.Object, _mapper, _userContext.Object, _snsServiceClient.Object, _envVariableProvider.Object, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(
                ab => ab.TrialBalance.ReportingPeriods.First().StartDate == _periodStartDate), CancellationToken.None),
                Times.Once);

            _repository.Verify(repository => repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(
                ab => ab.TrialBalance.SectorsInformation.First().DisplayId == "Fund1"
                && ab.TrialBalance.SectorsInformation.First().SubType == "Restricted"
                && ab.TrialBalance.SectorsInformation.First().Id == TestHelpers.Guids.GuidOne
                && ab.TrialBalance.SectorsInformation.First().Type == "Fund"
                && ab.TrialBalance.SectorsInformation.First().Order == 1), CancellationToken.None), Times.Once);
        }

        [Fact]
        public async Task Should_update_accounts_production_in_error_state()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, _entitySetup, null, _reportingStandard));

            var requestMessage = GetRequestMessage(false);
            var strategy = new TrialBalanceDataEventStrategy(_repository.Object, _logger.Object, _mapper, _userContext.Object, _snsServiceClient.Object, _envVariableProvider.Object, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(x => x.TrialBalance.IsSuccessful == false), CancellationToken.None), Times.Once);
        }

        [Fact]
        public async Task Should_add_account_builder_if_not_exist()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => null);
            var requestMessage = GetRequestMessage();
            var strategy = new TrialBalanceDataEventStrategy(_repository.Object, _logger.Object, _mapper, _userContext.Object, _snsServiceClient.Object, _envVariableProvider.Object, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);
            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }


        private string GetRequestMessage(bool isSuccessful = true)
        {
            var message = JsonSerializer.Serialize(new TrialBalanceDto
            {
                ClientId = _clientId,
                PeriodId = _periodId,
                TrialBalances = new List<PeriodTrialBalanceDto>
                {
                    new()
                    {
                        AccountCode = 1,
                        Amount = 2000,
                        Description = "no description",
                        SubAccountCode = 1,
                        Year = DateTime.UtcNow
                    }
                },
                ReportingPeriods = new List<ReportingPeriodDto>
                {
                    new()
                    {
                        Id = _periodId,
                        StartDate = _periodStartDate,
                        EndDate = _periodEndDate
                    }
                },
                SectorsInformation =
                [
                    new SectorInfoDto()
                    {
                        DisplayId = "Fund1",
                        SubType = "Restricted",
                        Id = TestHelpers.Guids.GuidOne,
                        Type = "Fund",
                        Order = 1
                    }
                ],
                IsSuccessful = isSuccessful,
            }, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return message;
        }
    }
}