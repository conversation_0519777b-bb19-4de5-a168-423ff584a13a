﻿using AccountsProduction.AccountsBuilder.Application.Common;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Common
{
    public class ListSearchExtensionTests
    {
        public class When_using_in_method : ListSearchExtensionTests
        {
            [Fact]
            public void Should_return_true_if_searched_element_is_found_in_list()
            {
                var currentElement = 1;
                var targetList = new[] { 1, 2, 3, 4, 5 };
                currentElement.In(targetList).ShouldBeTrue();
            }

            [Fact]
            public void Should_return_false_if_searched_element_is_not_found_in_list()
            {
                var currentElement = 10;
                var targetList = new[] { 1, 2, 3, 4, 5 };
                currentElement.In(targetList).ShouldBeFalse();
            }
        }
    }
}