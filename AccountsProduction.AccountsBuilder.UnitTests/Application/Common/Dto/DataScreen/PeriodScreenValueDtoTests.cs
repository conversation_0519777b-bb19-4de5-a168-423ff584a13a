using AccountsProduction.AccountsBuilder.Application.Common.Dto.DataScreen;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Common.Dto.DataScreen
{
    public class PeriodScreenValueDtoTests
    {
        [Fact]
        public void PeriodScreenValueDto_ShouldInitializeWithDefaultValues()
        {
            // Arrange & Act
            var dto = new PeriodScreenValueDto();

            // Assert
            Assert.Null(dto.ScreenId);
            Assert.Null(dto.ReportMappingTable);
            Assert.Null(dto.ScreenFields);
        }

        [Fact]
        public void ScreenId_ShouldGetAndSet()
        {
            // Arrange
            var dto = new PeriodScreenValueDto();
            var expectedScreenId = "TestScreenId123";

            // Act
            dto.ScreenId = expectedScreenId;

            // Assert
            Assert.Equal(expectedScreenId, dto.ScreenId);
        }

        [Fact]
        public void ReportMappingTable_ShouldGetAndSet()
        {
            // Arrange
            var dto = new PeriodScreenValueDto();
            var expectedTable = "NoteOther";

            // Act
            dto.ReportMappingTable = expectedTable;

            // Assert
            Assert.Equal(expectedTable, dto.ReportMappingTable);
        }

        [Fact]
        public void ScreenFields_ShouldGetAndSet()
        {
            // Arrange
            var dto = new PeriodScreenValueDto();
            var expectedFields = new List<ScreenFieldDto>
            {
                new ScreenFieldDto { Name = "Field1", Value = "Value1" },
                new ScreenFieldDto { Name = "Field2", Value = 123 },
                new ScreenFieldDto { Name = "Field3", Value = true }
            };

            // Act
            dto.ScreenFields = expectedFields;

            // Assert
            Assert.Equal(expectedFields, dto.ScreenFields);
            Assert.Equal(3, dto.ScreenFields.Count);
            Assert.Equal("Field1", dto.ScreenFields[0].Name);
            Assert.Equal("Value1", dto.ScreenFields[0].Value);
        }

        [Fact]
        public void ScreenFields_ShouldAcceptEmptyList()
        {
            // Arrange
            var dto = new PeriodScreenValueDto();
            var emptyList = new List<ScreenFieldDto>();

            // Act
            dto.ScreenFields = emptyList;

            // Assert
            Assert.NotNull(dto.ScreenFields);
            Assert.Empty(dto.ScreenFields);
        }

        [Fact]
        public void ScreenFields_ShouldAcceptNull()
        {
            // Arrange
            var dto = new PeriodScreenValueDto();
            dto.ScreenFields = new List<ScreenFieldDto>();

            // Act
            dto.ScreenFields = null;

            // Assert
            Assert.Null(dto.ScreenFields);
        }

        [Theory]
        [InlineData("NoteOther")]
        [InlineData("NoteAcctgPols")]
        [InlineData("NoteProfitAndLoss")]
        [InlineData("NoteBalanceSheet")]
        [InlineData("Reports")]
        [InlineData("NoteSofa")]
        public void ReportMappingTable_ShouldAcceptVariousValues(string tableName)
        {
            // Arrange
            var dto = new PeriodScreenValueDto();

            // Act
            dto.ReportMappingTable = tableName;

            // Assert
            Assert.Equal(tableName, dto.ReportMappingTable);
        }

        [Fact]
        public void PeriodScreenValueDto_ShouldBeFullyPopulated()
        {
            // Arrange
            var screenId = "ScreenId123";
            var reportMappingTable = "NoteOther";
            var screenFields = new List<ScreenFieldDto>
            {
                new ScreenFieldDto 
                { 
                    Name = "FieldName1", 
                    Value = "StringValue" 
                },
                new ScreenFieldDto 
                { 
                    Name = "FieldName2", 
                    Value = 12345 
                },
                new ScreenFieldDto 
                { 
                    Name = "FieldName3", 
                    Value = true 
                },
                new ScreenFieldDto 
                { 
                    Name = "FieldName4", 
                    Value = null 
                }
            };

            // Act
            var dto = new PeriodScreenValueDto
            {
                ScreenId = screenId,
                ReportMappingTable = reportMappingTable,
                ScreenFields = screenFields
            };

            // Assert
            Assert.Equal(screenId, dto.ScreenId);
            Assert.Equal(reportMappingTable, dto.ReportMappingTable);
            Assert.Equal(screenFields, dto.ScreenFields);
            Assert.Equal(4, dto.ScreenFields.Count);
            Assert.Equal("FieldName1", dto.ScreenFields[0].Name);
            Assert.Equal("StringValue", dto.ScreenFields[0].Value);
            Assert.Equal("FieldName2", dto.ScreenFields[1].Name);
            Assert.Equal(12345, dto.ScreenFields[1].Value);
            Assert.Equal("FieldName3", dto.ScreenFields[2].Name);
            Assert.Equal(true, dto.ScreenFields[2].Value);
            Assert.Equal("FieldName4", dto.ScreenFields[3].Name);
            Assert.Null(dto.ScreenFields[3].Value);
        }

        [Fact]
        public void PeriodScreenValueDto_ShouldHandleComplexScreenFieldValues()
        {
            // Arrange
            var dto = new PeriodScreenValueDto
            {
                ScreenId = "ComplexScreen",
                ReportMappingTable = "ComplexTable",
                ScreenFields = new List<ScreenFieldDto>
                {
                    new ScreenFieldDto { Name = "DecimalField", Value = 123.45m },
                    new ScreenFieldDto { Name = "DoubleField", Value = 456.78d },
                    new ScreenFieldDto { Name = "FloatField", Value = 789.01f },
                    new ScreenFieldDto { Name = "LongField", Value = 9876543210L },
                    new ScreenFieldDto { Name = "BoolField", Value = false }
                }
            };

            // Assert
            Assert.Equal("ComplexScreen", dto.ScreenId);
            Assert.Equal("ComplexTable", dto.ReportMappingTable);
            Assert.Equal(5, dto.ScreenFields.Count);
            Assert.Equal(123.45m, dto.ScreenFields[0].Value);
            Assert.Equal(456.78d, dto.ScreenFields[1].Value);
            Assert.Equal(789.01f, dto.ScreenFields[2].Value);
            Assert.Equal(9876543210L, dto.ScreenFields[3].Value);
            Assert.Equal(false, dto.ScreenFields[4].Value);
        }
    }
} 