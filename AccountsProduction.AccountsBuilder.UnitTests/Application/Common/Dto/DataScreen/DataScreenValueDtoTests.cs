using AccountsProduction.AccountsBuilder.Application.Common.Dto.DataScreen;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Common.Dto.DataScreen
{
    public class DataScreenValueDtoTests
    {
        [Fact]
        public void DataScreenValueDto_ShouldInitializeWithDefaultValues()
        {
            // Arrange & Act
            var dto = new DataScreenValueDto();

            // Assert
            Assert.Equal(default(Guid), dto.TenantId);
            Assert.Equal(default(Guid), dto.ClientId);
            Assert.Equal(default(Guid), dto.PeriodId);
            Assert.Null(dto.PreviousPeriodId);
            Assert.NotEqual(default(Guid), dto.CorrelationId); // Should have a generated value
            Assert.NotNull(dto.CurrentPeriod);
            Assert.Empty(dto.CurrentPeriod);
            Assert.NotNull(dto.PreviousPeriod);
            Assert.Empty(dto.PreviousPeriod);
            Assert.Null(dto.Error);
            Assert.Null(dto.IsSuccessful);
        }

        [Fact]
        public void TenantId_ShouldGetAndSet()
        {
            // Arrange
            var dto = new DataScreenValueDto();
            var expectedGuid = Guid.NewGuid();

            // Act
            dto.TenantId = expectedGuid;

            // Assert
            Assert.Equal(expectedGuid, dto.TenantId);
        }

        [Fact]
        public void ClientId_ShouldGetAndSet()
        {
            // Arrange
            var dto = new DataScreenValueDto();
            var expectedGuid = Guid.NewGuid();

            // Act
            dto.ClientId = expectedGuid;

            // Assert
            Assert.Equal(expectedGuid, dto.ClientId);
        }

        [Fact]
        public void PeriodId_ShouldGetAndSet()
        {
            // Arrange
            var dto = new DataScreenValueDto();
            var expectedGuid = Guid.NewGuid();

            // Act
            dto.PeriodId = expectedGuid;

            // Assert
            Assert.Equal(expectedGuid, dto.PeriodId);
        }

        [Fact]
        public void PreviousPeriodId_ShouldGetAndSet()
        {
            // Arrange
            var dto = new DataScreenValueDto();
            var expectedGuid = Guid.NewGuid();

            // Act
            dto.PreviousPeriodId = expectedGuid;

            // Assert
            Assert.Equal(expectedGuid, dto.PreviousPeriodId);
        }

        [Fact]
        public void PreviousPeriodId_ShouldAcceptNull()
        {
            // Arrange
            var dto = new DataScreenValueDto();
            dto.PreviousPeriodId = Guid.NewGuid();

            // Act
            dto.PreviousPeriodId = null;

            // Assert
            Assert.Null(dto.PreviousPeriodId);
        }

        [Fact]
        public void CorrelationId_ShouldHaveDefaultValue()
        {
            // Arrange & Act
            var dto1 = new DataScreenValueDto();
            var dto2 = new DataScreenValueDto();

            // Assert
            Assert.NotEqual(default(Guid), dto1.CorrelationId);
            Assert.NotEqual(default(Guid), dto2.CorrelationId);
            Assert.NotEqual(dto1.CorrelationId, dto2.CorrelationId); // Each instance should have unique correlation ID
        }

        [Fact]
        public void CorrelationId_ShouldGetAndSet()
        {
            // Arrange
            var dto = new DataScreenValueDto();
            var expectedGuid = Guid.NewGuid();

            // Act
            dto.CorrelationId = expectedGuid;

            // Assert
            Assert.Equal(expectedGuid, dto.CorrelationId);
        }

        [Fact]
        public void CurrentPeriod_ShouldGetAndSet()
        {
            // Arrange
            var dto = new DataScreenValueDto();
            var expectedList = new List<PeriodScreenValueDto>
            {
                new PeriodScreenValueDto { ScreenId = "Screen1" },
                new PeriodScreenValueDto { ScreenId = "Screen2" }
            };

            // Act
            dto.CurrentPeriod = expectedList;

            // Assert
            Assert.Equal(expectedList, dto.CurrentPeriod);
            Assert.Equal(2, dto.CurrentPeriod.Count);
        }

        [Fact]
        public void PreviousPeriod_ShouldGetAndSet()
        {
            // Arrange
            var dto = new DataScreenValueDto();
            var expectedList = new List<PeriodScreenValueDto>
            {
                new PeriodScreenValueDto { ScreenId = "Screen1" },
                new PeriodScreenValueDto { ScreenId = "Screen2" }
            };

            // Act
            dto.PreviousPeriod = expectedList;

            // Assert
            Assert.Equal(expectedList, dto.PreviousPeriod);
            Assert.Equal(2, dto.PreviousPeriod.Count);
        }

        [Fact]
        public void Error_ShouldGetAndSet()
        {
            // Arrange
            var dto = new DataScreenValueDto();
            var expectedError = "Test error message";

            // Act
            dto.Error = expectedError;

            // Assert
            Assert.Equal(expectedError, dto.Error);
        }

        [Fact]
        public void IsSuccessful_ShouldGetAndSet()
        {
            // Arrange
            var dto = new DataScreenValueDto();

            // Act & Assert - Test true value
            dto.IsSuccessful = true;
            Assert.True(dto.IsSuccessful);

            // Act & Assert - Test false value
            dto.IsSuccessful = false;
            Assert.False(dto.IsSuccessful);

            // Act & Assert - Test null value
            dto.IsSuccessful = null;
            Assert.Null(dto.IsSuccessful);
        }

        [Fact]
        public void DataScreenValueDto_ShouldBeFullyPopulated()
        {
            // Arrange
            var tenantId = Guid.NewGuid();
            var clientId = Guid.NewGuid();
            var periodId = Guid.NewGuid();
            var previousPeriodId = Guid.NewGuid();
            var correlationId = Guid.NewGuid();
            var currentPeriod = new List<PeriodScreenValueDto>
            {
                new PeriodScreenValueDto
                {
                    ScreenId = "Screen1",
                    ReportMappingTable = "Table1",
                    ScreenFields = new List<ScreenFieldDto>()
                }
            };
            var previousPeriod = new List<PeriodScreenValueDto>
            {
                new PeriodScreenValueDto
                {
                    ScreenId = "Screen2",
                    ReportMappingTable = "Table2",
                    ScreenFields = new List<ScreenFieldDto>()
                }
            };

            // Act
            var dto = new DataScreenValueDto
            {
                TenantId = tenantId,
                ClientId = clientId,
                PeriodId = periodId,
                PreviousPeriodId = previousPeriodId,
                CorrelationId = correlationId,
                CurrentPeriod = currentPeriod,
                PreviousPeriod = previousPeriod,
                Error = "Test error",
                IsSuccessful = false
            };

            // Assert
            Assert.Equal(tenantId, dto.TenantId);
            Assert.Equal(clientId, dto.ClientId);
            Assert.Equal(periodId, dto.PeriodId);
            Assert.Equal(previousPeriodId, dto.PreviousPeriodId);
            Assert.Equal(correlationId, dto.CorrelationId);
            Assert.Equal(currentPeriod, dto.CurrentPeriod);
            Assert.Equal(previousPeriod, dto.PreviousPeriod);
            Assert.Equal("Test error", dto.Error);
            Assert.False(dto.IsSuccessful);
        }
    }
} 