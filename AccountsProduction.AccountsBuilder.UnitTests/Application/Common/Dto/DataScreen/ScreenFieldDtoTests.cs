using AccountsProduction.AccountsBuilder.Application.Common.Dto.DataScreen;
using AccountsProduction.AccountsBuilder.Domain.DataScreens;
using Amazon.DynamoDBv2.DataModel;
using System.Reflection;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Common.Dto.DataScreen
{
    public class ScreenFieldDtoTests
    {
        [Fact]
        public void ScreenFieldDto_ShouldInitializeWithDefaultValues()
        {
            // Arrange & Act
            var dto = new ScreenFieldDto();

            // Assert
            Assert.Null(dto.Name);
            Assert.Null(dto.Value);
        }

        [Fact]
        public void Name_ShouldGetAndSet()
        {
            // Arrange
            var dto = new ScreenFieldDto();
            var expectedName = "TestFieldName";

            // Act
            dto.Name = expectedName;

            // Assert
            Assert.Equal(expectedName, dto.Name);
        }

        [Theory]
        [InlineData("StringValue")]
        [InlineData(123)]
        [InlineData(123L)]
        [InlineData(123.45)]
        [InlineData(123.45f)]
        [InlineData(true)]
        [InlineData(false)]
        public void Value_ShouldGetAndSetVariousTypes(object value)
        {
            // Arrange
            var dto = new ScreenFieldDto();

            // Act
            dto.Value = value;

            // Assert
            Assert.Equal(value, dto.Value);
            Assert.Equal(value.GetType(), dto.Value.GetType());
        }

        [Fact]
        public void Value_ShouldAcceptNull()
        {
            // Arrange
            var dto = new ScreenFieldDto();
            dto.Value = "InitialValue";

            // Act
            dto.Value = null;

            // Assert
            Assert.Null(dto.Value);
        }

        [Fact]
        public void Value_ShouldAcceptDecimalType()
        {
            // Arrange
            var dto = new ScreenFieldDto();
            decimal decimalValue = 123.45m;

            // Act
            dto.Value = decimalValue;

            // Assert
            Assert.Equal(decimalValue, dto.Value);
            Assert.IsType<decimal>(dto.Value);
        }

        [Fact]
        public void Value_ShouldHaveDynamoDBPropertyAttribute()
        {
            // Arrange
            var propertyInfo = typeof(ScreenFieldDto).GetProperty("Value");

            // Act
            var attribute = propertyInfo.GetCustomAttribute<DynamoDBPropertyAttribute>();

            // Assert
            Assert.NotNull(attribute);
            Assert.Equal(typeof(PolymorphicConverter), attribute.Converter);
        }

        [Fact]
        public void ScreenFieldDto_ShouldBeFullyPopulated()
        {
            // Arrange
            var name = "ComplexFieldName";
            var value = new { Id = 1, Name = "Complex Object" };

            // Act
            var dto = new ScreenFieldDto
            {
                Name = name,
                Value = value
            };

            // Assert
            Assert.Equal(name, dto.Name);
            Assert.Equal(value, dto.Value);
        }

        [Theory]
        [InlineData("Field1", "Value1")]
        [InlineData("Field2", 123)]
        [InlineData("Field3", true)]
        [InlineData("Field4", null)]
        public void ScreenFieldDto_ShouldHandleCommonScenarios(string fieldName, object fieldValue)
        {
            // Arrange & Act
            var dto = new ScreenFieldDto
            {
                Name = fieldName,
                Value = fieldValue
            };

            // Assert
            Assert.Equal(fieldName, dto.Name);
            Assert.Equal(fieldValue, dto.Value);
        }

        [Fact]
        public void ScreenFieldDto_ShouldHandleNumericTypes()
        {
            // Arrange
            var testCases = new[]
            {
                new ScreenFieldDto { Name = "IntField", Value = 42 },
                new ScreenFieldDto { Name = "LongField", Value = 42L },
                new ScreenFieldDto { Name = "FloatField", Value = 42.5f },
                new ScreenFieldDto { Name = "DoubleField", Value = 42.5d },
                new ScreenFieldDto { Name = "DecimalField", Value = 42.5m }
            };

            // Assert
            Assert.Equal(42, testCases[0].Value);
            Assert.IsType<int>(testCases[0].Value);

            Assert.Equal(42L, testCases[1].Value);
            Assert.IsType<long>(testCases[1].Value);

            Assert.Equal(42.5f, testCases[2].Value);
            Assert.IsType<float>(testCases[2].Value);

            Assert.Equal(42.5d, testCases[3].Value);
            Assert.IsType<double>(testCases[3].Value);

            Assert.Equal(42.5m, testCases[4].Value);
            Assert.IsType<decimal>(testCases[4].Value);
        }

        [Fact]
        public void ScreenFieldDto_ShouldHandleEmptyString()
        {
            // Arrange
            var dto = new ScreenFieldDto();

            // Act
            dto.Name = "";
            dto.Value = "";

            // Assert
            Assert.Equal("", dto.Name);
            Assert.Equal("", dto.Value);
        }

        [Fact]
        public void ScreenFieldDto_ShouldHandleWhitespaceString()
        {
            // Arrange
            var dto = new ScreenFieldDto();

            // Act
            dto.Name = "   ";
            dto.Value = "   ";

            // Assert
            Assert.Equal("   ", dto.Name);
            Assert.Equal("   ", dto.Value);
        }

        [Fact]
        public void ScreenFieldDto_ShouldHandleSpecialCharactersInName()
        {
            // Arrange
            var dto = new ScreenFieldDto();
            var specialName = "Field_Name-123!@#$%^&*()";

            // Act
            dto.Name = specialName;

            // Assert
            Assert.Equal(specialName, dto.Name);
        }

        [Fact]
        public void ScreenFieldDto_ShouldHandleLargeNumbers()
        {
            // Arrange
            var dto = new ScreenFieldDto();
            var largeNumber = long.MaxValue;

            // Act
            dto.Value = largeNumber;

            // Assert
            Assert.Equal(largeNumber, dto.Value);
            Assert.IsType<long>(dto.Value);
        }

        [Fact]
        public void ScreenFieldDto_ShouldHandleNegativeNumbers()
        {
            // Arrange
            var dto = new ScreenFieldDto();

            // Act
            dto.Value = -123.45m;

            // Assert
            Assert.Equal(-123.45m, dto.Value);
            Assert.IsType<decimal>(dto.Value);
        }
    }
} 