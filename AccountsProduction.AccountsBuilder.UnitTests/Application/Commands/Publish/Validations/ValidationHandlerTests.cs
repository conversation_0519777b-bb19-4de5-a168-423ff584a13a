﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Domain;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations
{
    public class ValidationHandlerTests
    {
        public class When_validating_accounts_builder : ValidationHandlerTests

        {
            [Fact]
            public async Task Should_run_validation()
            {
                var validationRunnerMock = new Mock<IValidationRunnerBuilder>();

                validationRunnerMock
                    .Setup(mock =>
                        mock.GetValidationRunners(It
                            .IsAny<AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(),
                            It.IsAny<string>()))
                    .Returns(new List<ValidationRunner>());

                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    NonFinancialData = new NonFinancialData(),
                    ClientId = Guid.NewGuid(),
                    TenantId = Guid.NewGuid()
                };

                var validationHandler = new ValidationHandler(validationRunnerMock.Object, new Mock<IGroupAccountSubAccountIntervalRepository>().Object);

                var result = await validationHandler.Validate(accountsBuilder, string.Empty);

                result.ValidationIssues.Count.ShouldBe(0);
            }

            [Fact]
            public async Task Should_return_validation_issues_for_FRS105()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    NonFinancialData = new NonFinancialData
                    {
                        BusinessType = "Limited"
                    },
                    ClientId = Guid.NewGuid(),
                    TenantId = Guid.NewGuid(),
                    InvolvementsData = new InvolvementsData()
                };

                var validationHandler = new ValidationHandler(new ValidationRunnerBuilder(new Mock<IGroupAccountSubAccountIntervalRepository>().Object), new Mock<IGroupAccountSubAccountIntervalRepository>().Object);

                var result = await validationHandler.Validate(accountsBuilder, ReportStandardType.FRS105);

                result.ValidationIssues.Count.ShouldBe(8);
            }
        }
    }
}