﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.BalanceSheetRunner;
using AccountsProduction.AccountsBuilder.Domain;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations.ValidationRunners
{
    public class BalanceSheetValidationRunnerTests
    {
        private readonly AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder _accountsBuilder;

        public BalanceSheetValidationRunnerTests()
        {
            _accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder { TrialBalance = new TrialBalance() };
        }

        [Fact]
        public void Should_trigger_validation_if_no_accounts_found()
        {
            var issues = new BalanceSheetValidationRunner().Validate(_accountsBuilder);

            var balanceSheetIssue = issues.SingleOrDefault(x => x.Name == BalanceSheetValidation.BalanceSheet);
            balanceSheetIssue.ShouldNotBeNull();
            balanceSheetIssue.ErrorCode.ShouldBe(BalanceSheetValidation.BalanceSheetConfig.ErrorCode);
            balanceSheetIssue.DisplayName.ShouldBe(BalanceSheetValidation.BalanceSheetConfig.DisplayName);
            balanceSheetIssue.ErrorCategory.ShouldBe(BalanceSheetValidation.BalanceSheetConfig.ErrorCategory);
            balanceSheetIssue.Target.ShouldBe(BalanceSheetValidation.BalanceSheetConfig.Target);
            balanceSheetIssue.Type.ShouldBe(BalanceSheetValidation.BalanceSheetConfig.Type);
            balanceSheetIssue.Name.ShouldBe(BalanceSheetValidation.BalanceSheetConfig.Name);
            balanceSheetIssue.Description.ShouldBe(BalanceSheetValidation.BalanceSheetConfig.Description);
            balanceSheetIssue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Balance sheet");
        }

        [Fact]
        public void Should_not_trigger_validation_if_accounts_are_found_for_balancesheet()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 502,
                    Amount = 200
                }
            };

            var issues = new BalanceSheetValidationRunner().Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == BalanceSheetValidation.BalanceSheet);
            issue.ShouldBeNull();
        }
    }
}
