﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.TangibleFixedAssetsRunner;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels;
using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels.TangibleFixedAsset;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations.ValidationRunners
{
    public class TangibleFixedAssetsValidationRunnerTests
    {
        private readonly AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder _accountsBuilder;
        private readonly Mock<IGroupAccountSubAccountIntervalRepository> _repository;

        public TangibleFixedAssetsValidationRunnerTests()
        {
            _accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder { TrialBalance = new TrialBalance() };
            _repository = new Mock<IGroupAccountSubAccountIntervalRepository>();
            _repository.Setup(m => m.GetCachedGroupAccountSubAccountIntervalList()).Returns(new[]
          {
                 new GroupAccountSubAccountInterval
                {
                    GroupNo = 430,
                    AccountIntervalFrom = 512,
                    AccountIntervalTo = 514
                },
                      new GroupAccountSubAccountInterval
                {
                    GroupNo = 430,
                    AccountIntervalFrom = 522,
                    AccountIntervalTo = 526
                },
                new GroupAccountSubAccountInterval
                {
                    GroupNo = 441,
                    AccountIntervalFrom = 512,
                    AccountIntervalTo = 514
                },
                new GroupAccountSubAccountInterval
                {
                    GroupNo = 451,
                    AccountIntervalFrom = 522,
                    AccountIntervalTo = 526
                }
            });
        }

        [Fact]
        public void Should_trigger_validation_if_no_accounts_found()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>();

            var issues = new TangibleFixedAssetsValidationRunner(new Mock<IGroupAccountSubAccountIntervalRepository>().Object).Validate(_accountsBuilder);

            var tangibleFixedAssetsIssue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssets);
            tangibleFixedAssetsIssue.ShouldNotBeNull();
            tangibleFixedAssetsIssue.ErrorCode.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsConfig.ErrorCode);
            tangibleFixedAssetsIssue.DisplayName.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsConfig.DisplayName);
            tangibleFixedAssetsIssue.ErrorCategory.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsConfig.ErrorCategory);
            tangibleFixedAssetsIssue.Target.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsConfig.Target);
            tangibleFixedAssetsIssue.Type.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsConfig.Type);
            tangibleFixedAssetsIssue.Name.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsConfig.Name);
            tangibleFixedAssetsIssue.Description.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsConfig.Description);
            tangibleFixedAssetsIssue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Tangible Fixed Assets");

            var landAndBuildingsIssue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsLandAndBuildings);
            landAndBuildingsIssue.ShouldNotBeNull();
            landAndBuildingsIssue.ErrorCode.ShouldBe(TangibleFixedAssetsValidations.LandAndBuildingsConfig.ErrorCode);
            landAndBuildingsIssue.DisplayName.ShouldBe(TangibleFixedAssetsValidations.LandAndBuildingsConfig.DisplayName);
            landAndBuildingsIssue.ErrorCategory.ShouldBe(TangibleFixedAssetsValidations.LandAndBuildingsConfig.ErrorCategory);
            landAndBuildingsIssue.Target.ShouldBe(TangibleFixedAssetsValidations.LandAndBuildingsConfig.Target);
            landAndBuildingsIssue.Type.ShouldBe(TangibleFixedAssetsValidations.LandAndBuildingsConfig.Type);
            landAndBuildingsIssue.Name.ShouldBe(TangibleFixedAssetsValidations.LandAndBuildingsConfig.Name);
            landAndBuildingsIssue.Description.ShouldBe(TangibleFixedAssetsValidations.LandAndBuildingsConfig.Description);
            landAndBuildingsIssue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Tangible Fixed Assets > Land and Buildings");

            var plantAndMachineryIssue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsPlantAndMachineries);
            plantAndMachineryIssue.ShouldNotBeNull();
            plantAndMachineryIssue.ErrorCode.ShouldBe(TangibleFixedAssetsValidations.PlantAndMachineriesConfig.ErrorCode);
            plantAndMachineryIssue.DisplayName.ShouldBe(TangibleFixedAssetsValidations.PlantAndMachineriesConfig.DisplayName);
            plantAndMachineryIssue.ErrorCategory.ShouldBe(TangibleFixedAssetsValidations.PlantAndMachineriesConfig.ErrorCategory);
            plantAndMachineryIssue.Target.ShouldBe(TangibleFixedAssetsValidations.PlantAndMachineriesConfig.Target);
            plantAndMachineryIssue.Type.ShouldBe(TangibleFixedAssetsValidations.PlantAndMachineriesConfig.Type);
            plantAndMachineryIssue.Name.ShouldBe(TangibleFixedAssetsValidations.PlantAndMachineriesConfig.Name);
            plantAndMachineryIssue.Description.ShouldBe(TangibleFixedAssetsValidations.PlantAndMachineriesConfig.Description);
            plantAndMachineryIssue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Tangible Fixed Assets > Plant and Machinery");
        }

        [Fact]
        public void Should_not_trigger_section_validation_if_accounts_are_found_for_tangible_fixed_assets()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 512,
                    Amount = 200
                }
            };

            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);
            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssets);

            issue.ShouldBeNull();
        }

        [Fact]
        public void Should_not_trigger_validation_if_accounts_are_found_for_land_and_buildings()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 512,
                    Amount = 200
                }
            };

            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);
            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsLandAndBuildings);

            issue.ShouldBeNull();
        }

        [Fact]
        public void Should_trigger_issue_log_validation_if_accounts_are_found_for_freehold_property()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 512,
                    Amount = 200
                }
            };

            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);
            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsFreeholdProperty);

            issue.ShouldNotBeNull();
            issue.ErrorCode.ShouldBe(TangibleFixedAssetsValidations.FreeholdPropertyConfig.ErrorCode);
            issue.DisplayName.ShouldBe(TangibleFixedAssetsValidations.FreeholdPropertyConfig.DisplayName);
            issue.ErrorCategory.ShouldBe(TangibleFixedAssetsValidations.FreeholdPropertyConfig.ErrorCategory);
            issue.Target.ShouldBe(TangibleFixedAssetsValidations.FreeholdPropertyConfig.Target);
            issue.Type.ShouldBe(TangibleFixedAssetsValidations.FreeholdPropertyConfig.Type);
            issue.Name.ShouldBe(TangibleFixedAssetsValidations.FreeholdPropertyConfig.Name);
            issue.Description.ShouldBe(TangibleFixedAssetsValidations.FreeholdPropertyConfig.Description);
            issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Tangible Fixed Assets > Land and Buildings > Freehold Property");
        }

        [Fact]
        public void Should_trigger_issue_log_validation_if_accounts_are_found_for_shortleasehold_property()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 513,
                    Amount = 200
                }
            };


            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);
            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsShortLeaseholdProperty);

            issue.ShouldNotBeNull();
            issue.ErrorCode.ShouldBe(TangibleFixedAssetsValidations.ShortLeaseholdPropertyConfig.ErrorCode);
            issue.DisplayName.ShouldBe(TangibleFixedAssetsValidations.ShortLeaseholdPropertyConfig.DisplayName);
            issue.ErrorCategory.ShouldBe(TangibleFixedAssetsValidations.ShortLeaseholdPropertyConfig.ErrorCategory);
            issue.Target.ShouldBe(TangibleFixedAssetsValidations.ShortLeaseholdPropertyConfig.Target);
            issue.Type.ShouldBe(TangibleFixedAssetsValidations.ShortLeaseholdPropertyConfig.Type);
            issue.Name.ShouldBe(TangibleFixedAssetsValidations.ShortLeaseholdPropertyConfig.Name);
            issue.Description.ShouldBe(TangibleFixedAssetsValidations.ShortLeaseholdPropertyConfig.Description);
            issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Tangible Fixed Assets > Land and Buildings > Short leasehold property");
        }

        [Fact]
        public void Should_trigger_issue_log_validation_if_accounts_are_found_for_longleasehold_property()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 514,
                    Amount = 200
                }
            };

            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);
            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsLongLeaseholdProperty);

            issue.ShouldNotBeNull();
            issue.ErrorCode.ShouldBe(TangibleFixedAssetsValidations.LongLeaseholdPropertyConfig.ErrorCode);
            issue.DisplayName.ShouldBe(TangibleFixedAssetsValidations.LongLeaseholdPropertyConfig.DisplayName);
            issue.ErrorCategory.ShouldBe(TangibleFixedAssetsValidations.LongLeaseholdPropertyConfig.ErrorCategory);
            issue.Target.ShouldBe(TangibleFixedAssetsValidations.LongLeaseholdPropertyConfig.Target);
            issue.Type.ShouldBe(TangibleFixedAssetsValidations.LongLeaseholdPropertyConfig.Type);
            issue.Name.ShouldBe(TangibleFixedAssetsValidations.LongLeaseholdPropertyConfig.Name);
            issue.Description.ShouldBe(TangibleFixedAssetsValidations.LongLeaseholdPropertyConfig.Description);
            issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Tangible Fixed Assets > Land and Buildings > Long leasehold property");
        }

        [Fact]
        public void Should_not_trigger_validation_if_accounts_are_found_for_plant_and_machineries()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 523,
                    Amount = 200
                }
            };

            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);
            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsPlantAndMachineries);

            issue.ShouldBeNull();
        }

        [Fact]
        public void Should_trigger_ImprovementsToProperty_validation_when_found_account_522()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 522,
                    Amount = 100
                }
            };
            _repository.Setup(m => m.GetCachedGroupAccountSubAccountIntervalList()).Returns(new[]
            {
                new GroupAccountSubAccountInterval
                {
                    GroupNo = 430,
                    AccountIntervalFrom = 522,
                    AccountIntervalTo = 526
                }
            });


            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsImprovementsToProperty);
            issue.ShouldNotBeNull();
            issue.ErrorCode.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsImprovementsToPropertyConfig.ErrorCode);
            issue.DisplayName.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsImprovementsToPropertyConfig.DisplayName);
            issue.ErrorCategory.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsImprovementsToPropertyConfig.ErrorCategory);
            issue.Target.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsImprovementsToPropertyConfig.Target);
            issue.Type.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsImprovementsToPropertyConfig.Type);
            issue.Name.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsImprovementsToPropertyConfig.Name);
            issue.Description.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsImprovementsToPropertyConfig.Description);
            issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Tangible Fixed Assets > Plant and Machinery > Improvements To Property");
        }

        [Fact]
        public void Should_not_trigger_ImprovementsToProperty_validation_when_found_account_522_and_ImprovementsToProperty_has_value()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 522,
                    Amount = 100
                }
            };
            _accountsBuilder.AccountingPolicies = new AccountingPolicies
            {
                CurrentPeriodAccountingPolicies = new AccountingPoliciesData
                {
                    TangibleFixedAssets = new TangibleFixedAssets
                    {
                        PlantAndMachinery = new PlantAndMachineries
                        {
                            ImprovementsToProperty = new AssetsAdjustment
                            {
                                AlternativeBasis = "text",
                                ReducingBalanceBasis = 1,
                                StraightLineBasis = 2
                            }
                        }
                    }
                }
            };

            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsImprovementsToProperty);
            issue.ShouldBeNull();
        }

        [Fact]
        public void Should_trigger_PlantAndMachinery_validation_when_found_account_523()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 523,
                    Amount = 100
                }
            };

            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsPlantAndMachinery);
            issue.ShouldNotBeNull();
            issue.ErrorCode.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsPlantAndMachineryConfig.ErrorCode);
            issue.DisplayName.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsPlantAndMachineryConfig.DisplayName);
            issue.ErrorCategory.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsPlantAndMachineryConfig.ErrorCategory);
            issue.Target.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsPlantAndMachineryConfig.Target);
            issue.Type.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsPlantAndMachineryConfig.Type);
            issue.Name.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsPlantAndMachineryConfig.Name);
            issue.Description.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsPlantAndMachineryConfig.Description);
            issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Tangible Fixed Assets > Plant and Machinery > Plant And Machinery");
        }

        [Fact]
        public void Should_not_trigger_PlantAndMachinery_validation_when_found_account_523_and_PlantAndMachinery_has_value()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 523,
                    Amount = 100
                }
            };
            _accountsBuilder.AccountingPolicies = new AccountingPolicies
            {
                CurrentPeriodAccountingPolicies = new AccountingPoliciesData
                {
                    TangibleFixedAssets = new TangibleFixedAssets
                    {
                        PlantAndMachinery = new PlantAndMachineries
                        {
                            PlantAndMachinery = new AssetsAdjustment
                            {
                                AlternativeBasis = "text",
                                ReducingBalanceBasis = 1,
                                StraightLineBasis = 2
                            }
                        }
                    }
                }
            };

            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsPlantAndMachinery);
            issue.ShouldBeNull();
        }

        [Fact]
        public void Should_trigger_FixturesAndFittings_validation_when_found_account_524()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 524,
                    Amount = 100
                }
            };

            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsFixturesAndFittings);
            issue.ShouldNotBeNull();
            issue.ErrorCode.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsFixturesAndFittingsConfig.ErrorCode);
            issue.DisplayName.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsFixturesAndFittingsConfig.DisplayName);
            issue.ErrorCategory.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsFixturesAndFittingsConfig.ErrorCategory);
            issue.Target.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsFixturesAndFittingsConfig.Target);
            issue.Type.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsFixturesAndFittingsConfig.Type);
            issue.Name.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsFixturesAndFittingsConfig.Name);
            issue.Description.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsFixturesAndFittingsConfig.Description);
            issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Tangible Fixed Assets > Plant and Machinery > Fixtures And Fittings");
        }

        [Fact]
        public void Should_not_trigger_FixturesAndFittings_validation_when_found_account_524_and_FixturesAndFittings_has_value()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 524,
                    Amount = 100
                }
            };
            _accountsBuilder.AccountingPolicies = new AccountingPolicies
            {
                CurrentPeriodAccountingPolicies = new AccountingPoliciesData
                {
                    TangibleFixedAssets = new TangibleFixedAssets
                    {
                        PlantAndMachinery = new PlantAndMachineries
                        {
                            FixturesAndFittings = new AssetsAdjustment
                            {
                                AlternativeBasis = "text",
                                ReducingBalanceBasis = 1,
                                StraightLineBasis = 2
                            }
                        }
                    }
                }
            };

            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsFixturesAndFittings);
            issue.ShouldBeNull();
        }

        [Fact]
        public void Should_trigger_MotorVehicles_validation_when_found_account_525()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 525,
                    Amount = 100
                }
            };

            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            issues.SingleOrDefault(x => x.Target == Target.IssueLog).ShouldNotBeNull();
            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsMotorVehicles);
            issue.ShouldNotBeNull();
            issue.ErrorCode.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsMotorVehiclesConfig.ErrorCode);
            issue.DisplayName.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsMotorVehiclesConfig.DisplayName);
            issue.ErrorCategory.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsMotorVehiclesConfig.ErrorCategory);
            issue.Target.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsMotorVehiclesConfig.Target);
            issue.Type.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsMotorVehiclesConfig.Type);
            issue.Name.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsMotorVehiclesConfig.Name);
            issue.Description.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsMotorVehiclesConfig.Description);
            issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Tangible Fixed Assets > Plant and Machinery > Motor Vehicles");
        }

        [Fact]
        public void Should_not_trigger_MotorVehicles_validation_when_found_account_525_and_MotorVehicles_has_value()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 525,
                    Amount = 100
                }
            };
            _accountsBuilder.AccountingPolicies = new AccountingPolicies
            {
                CurrentPeriodAccountingPolicies = new AccountingPoliciesData
                {
                    TangibleFixedAssets = new TangibleFixedAssets
                    {
                        PlantAndMachinery = new PlantAndMachineries
                        {
                            MotorVehicles = new AssetsAdjustment
                            {
                                AlternativeBasis = "text",
                                ReducingBalanceBasis = 1,
                                StraightLineBasis = 2
                            }
                        }
                    }
                }
            };

            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsMotorVehicles);
            issue.ShouldBeNull();
        }

        [Fact]
        public void Should_trigger_ComputerEquipment_validation_when_found_account_526()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 526,
                    Amount = 100
                }
            };
            _repository.Setup(m => m.GetCachedGroupAccountSubAccountIntervalList()).Returns(new[]
            {
                new GroupAccountSubAccountInterval
                {
                    GroupNo = 430,
                    AccountIntervalFrom = 512,
                    AccountIntervalTo = 514
                },
                new GroupAccountSubAccountInterval
                {
                    GroupNo = 451,
                    AccountIntervalFrom = 522,
                    AccountIntervalTo = 526
                },
                new GroupAccountSubAccountInterval
                {
                    GroupNo = 430,
                    AccountIntervalFrom = 522,
                    AccountIntervalTo = 526
                },
                new GroupAccountSubAccountInterval
                {
                    GroupNo = 441,
                    AccountIntervalFrom = 512,
                    AccountIntervalTo = 514
                }
            });

            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            issues.SingleOrDefault(x => x.Target == Target.IssueLog).ShouldNotBeNull();
            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsComputerEquipment);
            issue.ShouldNotBeNull();
            issue.ErrorCode.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsComputerEquipmentConfig.ErrorCode);
            issue.DisplayName.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsComputerEquipmentConfig.DisplayName);
            issue.ErrorCategory.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsComputerEquipmentConfig.ErrorCategory);
            issue.Target.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsComputerEquipmentConfig.Target);
            issue.Type.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsComputerEquipmentConfig.Type);
            issue.Name.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsComputerEquipmentConfig.Name);
            issue.Description.ShouldBe(TangibleFixedAssetsValidations.TangibleFixedAssetsComputerEquipmentConfig.Description);
            issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Tangible Fixed Assets > Plant and Machinery > Computer Equipment");
        }

        [Fact]
        public void Should_not_trigger_ComputerEquipment_validation_when_found_account_526_and_ComputerEquipment_has_value()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 526,
                    Amount = 100
                }
            };
            _accountsBuilder.AccountingPolicies = new AccountingPolicies
            {
                CurrentPeriodAccountingPolicies = new AccountingPoliciesData
                {
                    TangibleFixedAssets = new TangibleFixedAssets
                    {
                        PlantAndMachinery = new PlantAndMachineries
                        {
                            ComputerEquipment = new AssetsAdjustment
                            {
                                AlternativeBasis = "text",
                                ReducingBalanceBasis = 1,
                                StraightLineBasis = 2
                            }
                        }
                    }
                }
            };

            var issues = new TangibleFixedAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == TangibleFixedAssetsValidations.TangibleFixedAssetsComputerEquipment);
            issue.ShouldBeNull();
        }
    }
}
