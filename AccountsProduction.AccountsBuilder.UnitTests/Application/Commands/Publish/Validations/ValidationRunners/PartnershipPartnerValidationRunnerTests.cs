﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.PartnershipPartnersRunner;
using AccountsProduction.AccountsBuilder.Domain;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations.ValidationRunners;

public class PartnershipPartnerValidationRunnerTests
{
    private readonly PartnershipPartnerValidationRunner _partnershipValidationRunner = new();
    public class When_validating_mandatory_missing_issues : PartnershipPartnerValidationRunnerTests
    {
        [Fact]
        public void Should_return_validation_issues_for_no_partners()
        {
            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
            {
                InvolvementsData = new InvolvementsData
                {
                    Involvements = new List<Involvement>
                    {
                        new()
                    }
                },
            };

            var issues = _partnershipValidationRunner.Validate(accountsBuilder);

            this.ShouldSatisfyAllConditions(() => issues.Count.ShouldBe(2),
                () => issues[0].Name.ShouldBe(PartnershipPartnerValidations.Partner),
                () => issues[1].Name.ShouldBe(PartnershipPartnerValidations.ActivePartner));
        }
        [Fact]
        public void Should_return_validation_issues_for_not_enough_active_partners()
        {
            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
            {
                InvolvementsData = new InvolvementsData
                {
                    Involvements = new List<Involvement>
                    {
                        new()
                        {
                            InvolvementType = "Proprietor/Partner",
                            InvolvedClientType = "business",
                            StartDate = DateTime.Today.AddMonths(-1)
                        },
                        new()
                        {
                            InvolvementType = "Proprietor/Partner",
                            InvolvedClientType = "business",
                            StartDate = DateTime.Today.AddMonths(-3),
                            EndDate = DateTime.Today
                        },
                    }
                },
            };

            var issues = _partnershipValidationRunner.Validate(accountsBuilder);

            this.ShouldSatisfyAllConditions(() => issues.Count.ShouldBe(1),
                () => issues[0].Name.ShouldBe(PartnershipPartnerValidations.ActivePartner));
        }

        [Fact]
        public void Should_not_return_validation_issues()
        {
            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
            {
                InvolvementsData = new InvolvementsData
                {
                    Involvements = new List<Involvement>
                    {
                        new()
                        {
                            InvolvementType = "Proprietor/Partner",
                            InvolvedClientType = "business",
                            StartDate = DateTime.Today.AddMonths(-1)
                        },
                        new()
                        {
                            InvolvementType = "Proprietor/Partner",
                            InvolvedClientType = "business",
                            StartDate = DateTime.Today.AddMonths(-3),
                        },
                    }
                },
                ProfitShareData = new ProfitShareData()
                {
                    UnallocatedAmount = 0.0m
                }
            };

            var issues = _partnershipValidationRunner.Validate(accountsBuilder);

            this.ShouldSatisfyAllConditions(() => issues.Count.ShouldBe(0));
        }

        [Theory]
        [InlineData(0.25)]
        [InlineData(-0.25)]
        public void Should_return_advisory_validation_issue_for_unallocated_profit(decimal unallocatedAmount)
        {
            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
            {
                InvolvementsData = new InvolvementsData
                {
                    Involvements = new List<Involvement>
                    {
                        new()
                        {
                            InvolvementType = "Proprietor/Partner",
                            InvolvedClientType = "business",
                            StartDate = DateTime.Today.AddMonths(-1)
                        },
                        new()
                        {
                            InvolvementType = "Proprietor/Partner",
                            InvolvedClientType = "business",
                            StartDate = DateTime.Today.AddMonths(-3),
                        },
                    }
                },
                ProfitShareData = new ProfitShareData()
                {
                    UnallocatedAmount = unallocatedAmount
                }
            };

            var issues = _partnershipValidationRunner.Validate(accountsBuilder);

            this.ShouldSatisfyAllConditions(() => issues.Count.ShouldBe(1),
                () =>
                {
                    issues[0].Name.ShouldBe(PartnershipPartnerValidations.UnallocatedProfit);
                    issues[0].ErrorCategory.ShouldBe(ErrorCategoryType.Advisory);
                });
        }


        [Theory]
        [InlineData(0.5)]
        [InlineData(-0.5)]
        public void Should_return_mandatory_validation_issue_for_unallocated_profit(decimal unallocatedAmount)
        {
            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
            {
                InvolvementsData = new InvolvementsData
                {
                    Involvements = new List<Involvement>
                    {
                        new()
                        {
                            InvolvementType = "Proprietor/Partner",
                            InvolvedClientType = "business",
                            StartDate = DateTime.Today.AddMonths(-1)
                        },
                        new()
                        {
                            InvolvementType = "Proprietor/Partner",
                            InvolvedClientType = "business",
                            StartDate = DateTime.Today.AddMonths(-3),
                        },
                    }
                },
                ProfitShareData = new ProfitShareData()
                {
                    UnallocatedAmount = unallocatedAmount
                }
            };

            var issues = _partnershipValidationRunner.Validate(accountsBuilder);

            this.ShouldSatisfyAllConditions(() => issues.Count.ShouldBe(1),
                () =>
                {
                    issues[0].Name.ShouldBe(PartnershipPartnerValidations.UnallocatedProfit);
                    issues[0].ErrorCategory.ShouldBe(ErrorCategoryType.Mandatory);
                });
        }
    }
}