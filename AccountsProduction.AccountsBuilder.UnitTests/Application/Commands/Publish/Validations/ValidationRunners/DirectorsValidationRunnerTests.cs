﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.DirectorsRunner;
using AccountsProduction.AccountsBuilder.Domain;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations.ValidationRunners
{
    public class DirectorsValidationRunnerTests
    {
        public class When_validating_mandatory_missing_issues : DirectorsValidationRunnerTests
        {
            [Fact]
            public void Should_return_validation_issues_for_empty_active_directors_data()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    InvolvementsData = new InvolvementsData
                    {
                        Involvements = new List<Involvement>
                        {
                            new Involvement
                            {
                                InvolvementType = "Director",
                                StartDate = DateTime.Today.AddMonths(3)
                            }
                        }
                    },
                    CreatedTimeUtc = DateTime.Today.AddMonths(-3)
                };

                var directorsValidationRunner = new DirectorsValidationRunner();
                var issues = directorsValidationRunner.Validate(accountsBuilder);

                issues.Count.ShouldBe(1);
                issues[0].ErrorCode.ShouldBe(ValidationCodes.DirectorActive);
                issues[0].Name.ShouldBe(DirectorValidations.ActiveDirector);
                issues[0].Type.ShouldBe(ValidationRuleType.Invalid);
                issues[0].Target.ShouldBe(Target.IssueLog);
                issues[0].ErrorCategory.ShouldBe(ErrorCategoryType.Mandatory);
                issues[0].DisplayName.ShouldBe("Director");
                issues[0].Breadcrumb.ShouldBe("Client management > Relationships tab");
                issues[0].Description.ShouldBe("The company director must be active at the time of the approval and submission of financial statements.");
            }

            [Fact]
            public void Should_return_validation_issues_for_empty_directors_data()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    InvolvementsData = new InvolvementsData
                    {
                        Involvements = new List<Involvement>
                        {
                            new Involvement()
                        }
                    },
                    CreatedTimeUtc = DateTime.Today.AddMonths(-3)
                };

                var directorsValidationRunner = new DirectorsValidationRunner();
                var issues = directorsValidationRunner.Validate(accountsBuilder);

                issues.Count.ShouldBe(2);
                issues[0].ErrorCode.ShouldBe(ValidationCodes.Director);
                issues[0].Name.ShouldBe(DirectorValidations.Director);
                issues[0].Type.ShouldBe(ValidationRuleType.Missing);
                issues[0].Target.ShouldBe(Target.IssueLog);
                issues[0].ErrorCategory.ShouldBe(ErrorCategoryType.Mandatory);
                issues[0].DisplayName.ShouldBe("Director");
                issues[0].Breadcrumb.ShouldBe("Client management > Relationships tab");
                issues[0].Description.ShouldBe("A company must have at least one director to be able to approve and submit financial statements.");
                issues[1].Name.ShouldBe(DirectorValidations.ActiveDirector);
                issues[1].ErrorCode.ShouldBe(ValidationCodes.DirectorActive);
            }

            [Fact]
            public void Should_not_return_validation_issues_director_data()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    InvolvementsData = new InvolvementsData
                    {
                        Involvements = new List<Involvement>
                        {
                            new Involvement
                            {
                                InvolvementType = "Director",
                                StartDate = DateTime.Today.AddMonths(-1)
                            }
                        }
                    },
                    CreatedTimeUtc = DateTime.Today.AddMonths(-3)
                };

                var directorsValidationRunner = new DirectorsValidationRunner();
                var issues = directorsValidationRunner.Validate(accountsBuilder);

                issues.ShouldBeEmpty();
            }
        }
    }
}