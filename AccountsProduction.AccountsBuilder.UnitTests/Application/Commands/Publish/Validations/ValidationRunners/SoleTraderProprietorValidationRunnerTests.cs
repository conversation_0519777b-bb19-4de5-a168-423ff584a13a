﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.SoleTraderProprietorRunner;
using AccountsProduction.AccountsBuilder.Domain;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations.ValidationRunners;

public class SoleTraderProprietorValidationRunnerTests
{
    private readonly SoleTraderProprietorValidationRunner _proprietorValidationRunner = new();
    public class When_validating_mandatory_missing_issues : SoleTraderProprietorValidationRunnerTests
    {
        [Fact]
        public void Should_return_validation_issues_for_no_partners()
        {
            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
            {
                InvolvementsData = new InvolvementsData
                {
                    Involvements = new List<Involvement>
                    {
                        new()
                    }
                },
            };

            var issues = _proprietorValidationRunner.Validate(accountsBuilder);

            this.ShouldSatisfyAllConditions(() => issues.Count.ShouldBe(1),
                () => issues[0].Name.ShouldBe(SoleTraderProprietorValidations.Proprietor));
        }

        [Fact]
        public void Should_not_return_validation_issues()
        {
            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
            {
                InvolvementsData = new InvolvementsData
                {
                    Involvements = new List<Involvement>
                    {
                        new()
                        {
                            InvolvementType = "Proprietor/Partner",
                            InvolvedClientType = "business",
                            StartDate = DateTime.Today.AddMonths(-1)
                        },
                        new()
                        {
                            InvolvementType = "Proprietor/Partner",
                            InvolvedClientType = "business",
                            StartDate = DateTime.Today.AddMonths(-3),
                        },
                    }
                },
            };

            var issues = _proprietorValidationRunner.Validate(accountsBuilder);

            this.ShouldSatisfyAllConditions(() => issues.Count.ShouldBe(0));
        }
    }
}