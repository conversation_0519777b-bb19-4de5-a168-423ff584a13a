﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.FRS105;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using DeepEqual.Syntax;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Iris.AccountsProduction.Common.Toolkit.Utils;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.ReportingDomain.FRS105
{
    public class FRS105ReportingDomainMapperTests
    {
        private readonly Mock<UserContext> _userContext;
        private readonly IMapper _mapper;

        public FRS105ReportingDomainMapperTests()
        {
            _userContext = new Mock<UserContext>();
            _userContext.Setup(x => x.TenantId).Returns(TestHelpers.Guids.GuidFour.ToString());
            _userContext.Setup(x => x.CorrelationId).Returns(TestHelpers.Guids.GuidFive.ToString());
            _userContext.Setup(x => x.UserId).Returns(TestHelpers.Guids.GuidSix.ToString());
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
        }
        [Theory]
        [InlineData("Active", null)]
        [InlineData("Trial", "IRIS Elements Accounts Production Trial Version")]
        public void Should_map_correct_data(string licenseStatus, string watermarkText)
        {
            var isTrialAPLicense = licenseStatus.Equals("Trial");

            _userContext.Setup(x => x.Licenses).Returns(new System.Collections.Generic.List<Iris.Platform.WebApi.Infrastructure.Licenses.License>
                {
                    new Iris.Platform.WebApi.Infrastructure.Licenses.License() { Code = APLicense.Name, IsTrial = isTrialAPLicense}
                });


            var entitySetup = new EntitySetup { IndependentReviewType = "Accountants" };
            var reportingStandardDetail = new ReportingStandard
            {
                Id = "62fe4ee96d17f93b787c23c0",
                Name = "FRS105 - Filleted",
                Type = "FRS105",
                Version = ReportingStandardVersion.Filleted
            };
            var licenseData = new LicenseData(isTrialAPLicense);

            var accountsBuilder = ReportingDomainMapperTestData.GetAccountsBuilderData(BusinessTypes.LimitedBusinessType, entitySetup, reportingStandardDetail);

            accountsBuilder.UpdateLicenseData(licenseData);

            var frs105ReportingDomainMapper = new FRS105ReportingDomainMapper(_userContext.Object, _mapper);

            var message = (FRS105ReportingMessage)frs105ReportingDomainMapper.Map(accountsBuilder);

            var expectedProfitAndLoss = _mapper.Map<ProfitAndLossMessage>(accountsBuilder.FinancialData.Financials.First());
            var expectedBalanceSheet = _mapper.Map<BalanceSheetMessage>(accountsBuilder.FinancialData.Financials.First());


            message.ReportType.ShouldBe(ReportStandardType.FRS105);
            message.ClientId.ShouldBe(accountsBuilder.ClientId);
            message.PeriodId.ShouldBe(accountsBuilder.PeriodId);
            message.TenantId.ShouldBe(accountsBuilder.TenantId);
            message.WatermarkText.ShouldBe(watermarkText);

            message.ProfitAndLossData.First().PeriodId.ShouldBe(TestHelpers.Guids.GuidSeven);
            message.ProfitAndLossData.First().Period.ShouldBe(new DateTime(2022, 2, 2));
            message.ProfitAndLossData.First().WithDeepEqual(expectedProfitAndLoss).IgnoreDestinationProperty(x => x.PeriodId).Assert();

            message.BalanceSheetData.First().PeriodId.ShouldBe(TestHelpers.Guids.GuidSeven);
            message.BalanceSheetData.First().Period.ShouldBe(new DateTime(2022, 2, 2));
            message.BalanceSheetData.First().WithDeepEqual(expectedBalanceSheet).IgnoreDestinationProperty(x => x.PeriodId).Assert();

            message.Signatures.AccountantSigningDate.ShouldBe(new DateTime(2022, 3, 3));
            message.Signatures.IncludeAccountantsReport?.ShouldBeTrue();
            message.Signatures.Signatures[0].SignatureType.ShouldBe(SignatureType.BalanceSheet);
            message.Signatures.Signatures[0].SignatureDate.ShouldBe(new DateTime(2022, 3, 3));
            message.Signatures.Signatures[0].DisplayOrder.ShouldBe(1);
            message.Signatures.Signatures[0].InvolvementUUID.ShouldBe(TestHelpers.Guids.GuidSeven);
            message.Signatures.Signatures[0].InvolvementType.ShouldBe("Director");
            message.Signatures.Signatures[0].SignatoryTitle.ShouldBe("Mr");
            message.Signatures.Signatures[0].SignatoryFirstName.ShouldBe("Bob");
            message.Signatures.Signatures[0].SignatorySurname.ShouldBe("Smith");
            message.Signatures.Signatures[1].SignatureType.ShouldBe(SignatureType.CIC34Report);
            message.Signatures.Signatures[1].SignatureDate.ShouldBe(new DateTime(2022, 3, 3));
            message.Signatures.Signatures[1].DisplayOrder.ShouldBe(1);
            message.Signatures.Signatures[1].InvolvementUUID.ShouldBe(TestHelpers.Guids.GuidEight);
            message.Signatures.Signatures[1].InvolvementType.ShouldBe("Director");
            message.Signatures.Signatures[1].SignatoryTitle.ShouldBe("Mr");
            message.Signatures.Signatures[1].SignatoryFirstName.ShouldBe("Bob");
            message.Signatures.Signatures[1].SignatorySurname.ShouldBe("Smith");
            message.Signatures.Signatures[2].SignatureType.ShouldBe(SignatureType.BalanceSheet);
            message.Signatures.Signatures[2].SignatureDate.ShouldBeNull();
            message.Signatures.Signatures[2].DisplayOrder.ShouldBe(1);
            message.Signatures.Signatures[2].InvolvementUUID.ShouldBe(TestHelpers.Guids.GuidNine);
            message.Signatures.Signatures[2].InvolvementType.ShouldBe("Secretary");
            message.Signatures.Signatures[2].SignatoryTitle.ShouldBe("Mr");
            message.Signatures.Signatures[2].SignatoryFirstName.ShouldBe("Smith");
            message.Signatures.Signatures[2].SignatorySurname.ShouldBe("Bob");
            message.Signatures.Signatures[3].SignatureType.ShouldBe(SignatureType.SupplementaryNote);
            message.Signatures.Signatures[3].SignatureDate.ShouldBe(new DateTime(2024, 12, 3));
            message.Signatures.Signatures[3].DisplayOrder.ShouldBe(1);
            message.Signatures.Signatures[3].InvolvementUUID.ShouldBe(TestHelpers.Guids.GuidSeven);
            message.Signatures.Signatures[3].InvolvementType.ShouldBe("Director");
            message.Signatures.Signatures[3].SignatoryTitle.ShouldBe("Mr");
            message.Signatures.Signatures[3].SignatoryFirstName.ShouldBe("Bob");
            message.Signatures.Signatures[3].SignatorySurname.ShouldBe("Smith");

            message.ClientData.CompanyName.ShouldBe("CompanyName");
            message.ClientData.CompanyType.ShouldBe(BusinessTypes.LimitedBusinessType);

            message.Involvements[0].InvolvementTitle.ShouldBe("Mr");
            message.Involvements[0].InvolvementFirstName.ShouldBe("Bob");
            message.Involvements[0].InvolvementSurname.ShouldBe("Smith");
            message.Involvements[0].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidSeven);
            message.Involvements[0].InvolvementType.ShouldBe("Director");
            message.Involvements[1].InvolvementTitle.ShouldBe("Mr");
            message.Involvements[1].InvolvementFirstName.ShouldBe("Bob");
            message.Involvements[1].InvolvementSurname.ShouldBe("Smith");
            message.Involvements[1].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidEight);
            message.Involvements[1].InvolvementType.ShouldBe("Director");
            message.Involvements[2].InvolvementTitle.ShouldBe("Mr");
            message.Involvements[2].InvolvementFirstName.ShouldBe("Smith");
            message.Involvements[2].InvolvementSurname.ShouldBe("Bob");
            message.Involvements[2].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidNine);
            message.Involvements[2].InvolvementType.ShouldBe("Secretary");

            message.ReportingPeriods.First().Id.ShouldBe(TestHelpers.Guids.GuidSeven);
            message.ReportingPeriods.First().EndDate.ShouldBe(new DateTime(2022, 2, 2));

            message.Notes.AdditionalNote1.NoteText.ShouldBe("text");
            message.Notes.AdditionalNote1.NoteTitle.ShouldBe("title");

            message.PracticeDetails.AddressTown.ShouldBe("town");
            message.PracticeDetails.AddressCountry.ShouldBe("country");

            message.EntitySetup.IndependentReviewType.ShouldBe("Accountants");

            message.ReportVersion.ShouldBe(accountsBuilder.ReportingStandard?.Version);

            message.DataScreenValue.ClientId.ShouldBe(TestHelpers.Guids.GuidOne);
            message.DataScreenValue.PeriodId.ShouldBe(TestHelpers.Guids.GuidTwo);
            message.DataScreenValue.PreviousPeriodId.ShouldBe(TestHelpers.Guids.GuidThree);
            message.DataScreenValue.TenantId.ShouldBe(TestHelpers.Guids.GuidFour);
            message.DataScreenValue.CurrentPeriod.Count.ShouldBe(2);
            message.DataScreenValue.PreviousPeriod.Count.ShouldBe(2);

            message.AccountPeriod.ReviseType.ShouldBe("SupplementaryNote");
        }
    }
}