﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.Charity;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using DeepEqual.Syntax;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Iris.AccountsProduction.Common.Toolkit.Utils;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.ReportingDomain.Charity;

public class CharityReportingDomainMapperTests
{
    private readonly Mock<UserContext> _userContext;
    private readonly IMapper _mapper;

    public CharityReportingDomainMapperTests()
    {
        _userContext = new Mock<UserContext>();
        _userContext.Setup(x => x.TenantId).Returns(TestHelpers.Guids.GuidFour.ToString());
        _userContext.Setup(x => x.CorrelationId).Returns(TestHelpers.Guids.GuidFive.ToString());
        _userContext.Setup(x => x.UserId).Returns(TestHelpers.Guids.GuidSix.ToString());
        _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
    }

    [Theory]
    [InlineData("Active", null)]
    [InlineData("Trial", "IRIS Elements Accounts Production Trial Version")]
    public void Should_map_correct_data(string licenseStatus, string watermarkText)
    {
        var isTrialAPLicense = licenseStatus.Equals("Trial");

        _userContext.Setup(x => x.Licenses).Returns(new List<Iris.Platform.WebApi.Infrastructure.Licenses.License>
            {
                new Iris.Platform.WebApi.Infrastructure.Licenses.License() { Code = APLicense.Name, IsTrial = isTrialAPLicense}
            });

        var entitySetup = new EntitySetup { IndependentReviewType = "Accountants" };
        var reportingStandardDetail = new ReportingStandard
        {
            Id = "679767ada054710b064f02d0",
            Name = "Charity - Full",
            Type = "Charity",
            Version = ReportingStandardVersion.Full
        };
        var licenseData = new LicenseData(isTrialAPLicense);

        var accountsBuilder = ReportingDomainMapperTestData.GetAccountsBuilderData(BusinessTypes.LimitedBusinessType, entitySetup, reportingStandardDetail);
        accountsBuilder.UpdateLicenseData(licenseData);

        var charityReportingDomainMapper = new CharityReportingDomainMapper(_userContext.Object, _mapper);

        var message = (FRS1021AAndFRS102SharedReportingMessage)charityReportingDomainMapper.Map(accountsBuilder);

        var expectedProfitAndLoss = _mapper.Map<ProfitAndLossMessage>(accountsBuilder.FinancialData.Financials.First());
        var expectedBalanceSheet = _mapper.Map<BalanceSheetMessage>(accountsBuilder.FinancialData.Financials.First());

        message.TenantId.ShouldBe(TestHelpers.Guids.GuidFour);

        message.ReportType.ShouldBe(ReportStandardType.CHARITY_SORP_FRS102);
        message.ClientId.ShouldBe(accountsBuilder.ClientId);
        message.PeriodId.ShouldBe(accountsBuilder.PeriodId);
        message.TenantId.ShouldBe(accountsBuilder.TenantId);
        message.WatermarkText.ShouldBe(watermarkText);

        message.ProfitAndLossData.First().PeriodId.ShouldBe(TestHelpers.Guids.GuidSeven);
        message.ProfitAndLossData.First().Period.ShouldBe(new DateTime(2022, 2, 2));
        message.ProfitAndLossData.First().WithDeepEqual(expectedProfitAndLoss).IgnoreDestinationProperty(x => x.PeriodId).Assert();

        message.BalanceSheetData.First().PeriodId.ShouldBe(TestHelpers.Guids.GuidSeven);
        message.BalanceSheetData.First().Period.ShouldBe(new DateTime(2022, 2, 2));
        message.BalanceSheetData.First().WithDeepEqual(expectedBalanceSheet).IgnoreDestinationProperty(x => x.PeriodId).Assert();
    }
}
