﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands
{
    public class ProcessAggregationCommandTests
    {
        private readonly Mock<ILogger<ProcessAggregationCommandHandler>> _logger;
        private readonly Mock<UserContext> _userContext;
        private readonly Mock<IDomainEventService> _domainEventService;
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<IGenerateReportDataService> _generateReportDataService;
        private readonly IMapper _mapper;
        private readonly List<IAggregationStrategy> _strategies;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = TestHelpers.Guids.GuidTwo;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;
        private readonly Mock<IAccountPeriodService> _accountPeriodService;

        public ProcessAggregationCommandTests()
        {
            _logger = new Mock<ILogger<ProcessAggregationCommandHandler>>();
            _strategies = new List<IAggregationStrategy>();
            _userContext = new Mock<UserContext>();
            _userContext.Setup(q => q.TenantId).Returns(_tenantId.ToString());
            _repository = new Mock<IAccountsBuilderRepository>();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _generateReportDataService = new Mock<IGenerateReportDataService>();
            _generateReportDataService.Setup(c => c.GetReportData(_clientId, _periodId)).ReturnsAsync(new AccountsBuilder.Application.Common.Dto.Report.GenerateReportDto()
            {
                ClientAddressDto = new AccountsBuilder.Application.Common.Dto.Address.ClientAddressDto(),
                ClientResponse = new AccountsBuilder.Application.Common.Dto.Client.ClientResponse(),
                InvolvementDtos = new List<AccountsBuilder.Application.Common.Dto.Client.InvolvementDto>(),
                EntitySetupDto = new AccountsBuilder.Application.Common.Dto.EntitySetup.EntitySetupDto(),
                PracticeDetailsDto = new AccountsBuilder.Application.Common.Dto.PracticeDetails.PracticeDetailMessage(),
                TrialBalanceDto = new AccountsBuilder.Application.Common.Dto.TrialBalance.TrialBalanceDto()
                {
                    TrialBalances = new List<AccountsBuilder.Application.Common.Dto.TrialBalance.PeriodTrialBalanceDto>
                    {
                        new AccountsBuilder.Application.Common.Dto.TrialBalance.PeriodTrialBalanceDto
                        {
                            AccountCode = 1,
                            Amount = 10,
                            Description = "test",
                            Year = DateTime.UtcNow
                        }
                    }
                }
            });
            _domainEventService = new Mock<IDomainEventService>();
        }

        [Fact]
        public async Task Should_not_process_when_no_strategy_found_for_messageType()
        {
            var request = new ProcessAggregationCommand { MessageType = "invalid", ClientId = _clientId, PeriodId = _periodId };
            var handler = new ProcessAggregationCommandHandler(_logger.Object, _strategies);

            var response = await handler.Handle(request, CancellationToken.None);

            response.ShouldBe(Unit.Value);
        }

        [Fact]
        public async Task Should_process_event_when_strategy_found_for_messageType()
        {
            var requestMessage = GetRequestMessage();
            var request = new ProcessAggregationCommand
            {
                Message = requestMessage,
                MessageType = EventTypes.EntitySetupDataEvent,
                ClientId = _clientId,
                PeriodId = _periodId
            };
            _strategies.Add(new EntitySetupDataEventStrategy(_repository.Object, Mock.Of<ILogger<EntitySetupDataEventStrategy>>(), _mapper, _userContext.Object, _generateReportDataService.Object, _domainEventService.Object));

            var handler = new ProcessAggregationCommandHandler(_logger.Object, _strategies);

            var response = await handler.Handle(request, CancellationToken.None);

            response.ShouldBe(Unit.Value);
        }

        [Fact]
        public async Task Should_send_UpdateAccountsBuilderProcessCommand_with_Failed_status_when_exception_occurs_on_process()
        {
            var mockStrategy = new Mock<IAggregationStrategy>();
            mockStrategy.Setup(x => x.IsMatch(It.IsAny<string>())).Returns(true);
            mockStrategy.Setup(x => x.Process(It.IsAny<string>(), It.IsAny<Guid>(), It.IsAny<Guid>(), It.IsAny<Guid>(), It.IsAny<Guid>(), CancellationToken.None)).Throws(new Exception());
            _strategies.Add(mockStrategy.Object);

            var handler = new ProcessAggregationCommandHandler(_logger.Object, _strategies);
            await Should.ThrowAsync<Exception>(async () => { await handler.Handle(new ProcessAggregationCommand { ClientId = _clientId, PeriodId = _periodId }, CancellationToken.None); });
        }

        private static string GetRequestMessage()
        {
            var message = JsonSerializer.Serialize(new ReportingCompleteDto
            {
                Status = "Successful"
            }, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return message;
        }
    }
}