﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder;
using AccountsProduction.AccountsBuilder.Application.Queries.AccountsBuilder;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.Common.Toolkit.Exceptions;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Queries.AccountsBuilder
{
    public class GetReportQueryTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repositoryMock;
        private readonly IMapper _mapper;
        private readonly Mock<UserContext> _userContext;

        private readonly Guid _clientId = Guid.NewGuid();
        private readonly Guid _periodId = Guid.NewGuid();
        private readonly string _tenantId = Guid.NewGuid().ToString();

        public GetReportQueryTests()
        {
            _repositoryMock = new Mock<IAccountsBuilderRepository>();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _userContext = new Mock<UserContext>();
        }

        [Fact]
        public async Task Should_return_report_data()
        {
            _userContext.SetupGet(u => u.TenantId).Returns(_tenantId);
            var accountsBuilder = GetAccountsBuilder();
            var previousPeriodId = Guid.NewGuid();
            AddTrialBalance(accountsBuilder, previousPeriodId);
            AddValidationData(accountsBuilder);
            ModifyProfitShare(accountsBuilder);

            _repositoryMock.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetReportQuery
            {
                ClientId = _clientId,
                PeriodId = _periodId
            };

            var queryHandler = new GetReportQuery.GetReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);
            var response = await queryHandler.Handle(query, CancellationToken.None);
            response.ShouldNotBeNull();

            response.ClientId.ShouldBe(_clientId);
            response.PeriodId.ShouldBe(_periodId);
            response.PreviousPeriodId.ShouldBe(previousPeriodId);
            response.LastSuccessfullProcessId.ShouldBeNull();
            response.LicenseData.ShouldBeNull();
            response.LastSuccessfullTimeUtc.ShouldBe(DateTime.MinValue);
            response.ReportingStandard.ShouldNotBeNull();

            AssertValidationDataResponse(response.ValidationData);

        }

        [Fact]
        public async Task Should_return_report_data_with_no_previous_period()
        {
            _userContext.SetupGet(u => u.TenantId).Returns(_tenantId);

            var accountsBuilder = GetAccountsBuilder();

            _repositoryMock.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetReportQuery
            {
                ClientId = _clientId,
                PeriodId = _periodId
            };

            var queryHandler =
                new GetReportQuery.GetReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);
            var response = await queryHandler.Handle(query, CancellationToken.None);
            response.ShouldNotBeNull();

            response.ClientId.ShouldBe(_clientId);
            response.PeriodId.ShouldBe(_periodId);
            response.PreviousPeriodId.ShouldBe(Guid.Empty);

        }

        private void AddValidationData(AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            accountsBuilder.ValidationData = new ValidationData
            {
                ValidationIssues = new List<ValidationIssue>
                {
                    ValidationIssue.BuildValidationIssue("errorcode","name","displayname","breadcrumb","description",
                    "type","target","category")
                }
            };
        }

        private void AddTrialBalance(AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder,
            Guid previousPeriodId)
        {
            accountsBuilder.AddTrialBalance(new TrialBalance
            {
                AccountsChartIdentifier = "eltd",
                IsSuccessful = true,
                AccountsChartId = 1,
                PeriodId = Guid.NewGuid(),
                TrialBalances = new List<PeriodTrialBalance>
                {
                    new PeriodTrialBalance
                    {
                        AccountCode = 1,
                        Amount = 2000,
                        Description = "no description",
                        SubAccountCode = 1, Year = DateTime.UtcNow
                    }
                },
                ReportingPeriods = new List<ReportingPeriod>
                {
                    new ReportingPeriod
                    {
                        EndDate = DateTime.UtcNow,
                        Id = _periodId
                    },
                    new ReportingPeriod
                    {
                        EndDate = DateTime.UtcNow,
                        Id = previousPeriodId
                    }
                }
            });
        }

        [Fact]
        public async Task Should_return_null_when_period_not_found()
        {
            var accountsBuilder = GetAccountsBuilder();
            _repositoryMock.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetReportQuery
            {
                ClientId = _clientId,
                PeriodId = Guid.NewGuid()
            };

            var queryHandler =
                new GetReportQuery.GetReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);
            var response = await queryHandler.Handle(query, CancellationToken.None);
            response.ShouldBeNull();
        }

        [Fact]
        public async Task Should_return_null_when_client_not_found()
        {
            var accountsBuilder = GetAccountsBuilder();

            _repositoryMock.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetFullReportQuery
            {
                ClientId = Guid.NewGuid(),
                PeriodId = _periodId
            };

            var queryHandler = new GetFullReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);
            var response = await queryHandler.Handle(query, CancellationToken.None);
            response.ShouldBeNull();
        }

        private AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder GetAccountsBuilder()
        {
            var reportingStandard = new ReportingStandard
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = "FRS 105",
                Version = ReportingStandardVersion.Full
            };

            var entitySetup = new EntitySetup
            {
                EntitySize = "entitySize",
                IndependentReviewType = "independentReviewType",
                Terminology = "terminology",
                ReportingStandard = "reporting standard",
                DormantStatus = "dormant status"
            };

            return new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId, _clientId,
                _periodId, entitySetup, null, reportingStandard);
        }

        [Fact]
        public async Task Should_throw_exception()
        {
            _repositoryMock.Setup(repository =>
                    repository.Get(It.IsAny<Guid>(), It.IsAny<Guid>(), CancellationToken.None))
                .ThrowsAsync(new InternalException());
            var query = new GetFullReportQuery
            {
                ClientId = Guid.NewGuid(),
                PeriodId = Guid.NewGuid()
            };

            var queryHandler = new GetFullReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);

            await Should.ThrowAsync<InternalException>(async () =>
            {
                await queryHandler.Handle(query, CancellationToken.None);
            });
        }

        [Fact]
        public async Task Should_throw_invalid_tenant_exception()
        {
            _userContext.SetupGet(u => u.TenantId).Returns(TestHelpers.Guids.GuidFour.ToString);

            var accountsBuilder = GetAccountsBuilder();

            _repositoryMock.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetFullReportQuery
            {
                ClientId = _clientId,
                PeriodId = _periodId
            };

            var queryHandler = new GetFullReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);

            await Should.ThrowAsync<InvalidTenantException>(async () =>
            {
                await queryHandler.Handle(query, CancellationToken.None);
            });
        }

        private static void AssertValidationDataResponse(ValidationDataDto validationData)
        {
            validationData.ValidationIssues.Count.ShouldBe(1);

            var issue = validationData.ValidationIssues[0];
            issue.ErrorCode.ShouldBe("errorcode");
            issue.Name.ShouldBe("name");
            issue.DisplayName.ShouldBe("displayname");
            issue.Breadcrumb.ShouldBe("breadcrumb");
            issue.Description.ShouldBe("description");
            issue.Type.ShouldBe("type");
            issue.Target.ShouldBe("target");
            issue.ErrorCategory.ShouldBe("category");
        }

        private static void ModifyProfitShare(AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            accountsBuilder.AddPartnerProfitShares(new ProfitShareData());
        }
    }
}
