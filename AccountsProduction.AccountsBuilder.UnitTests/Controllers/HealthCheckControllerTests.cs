﻿using AccountsProduction.AccountsBuilder.Api.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Controllers
{
    public class HealthCheckControllerTests
    {
        private readonly HealthCheckController _healthcheckController;

        public HealthCheckControllerTests()
        {
            _healthcheckController = new HealthCheckController(new Mock<IConfiguration>().Object);
        }

        [Fact]
        public void Should_return_ok()
        {
            var response = _healthcheckController.CheckHealth();

            response.ShouldBeAssignableTo<OkObjectResult>();
        }
    }
}
