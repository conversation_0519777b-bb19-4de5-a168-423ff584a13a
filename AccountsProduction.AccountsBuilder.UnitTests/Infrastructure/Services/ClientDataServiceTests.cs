﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Address;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.NonFinancialData;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Infrastructure.Services;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using Shouldly;
using System.Net;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Infrastructure.Services
{
    public class ClientDataServiceTests
    {
        private readonly Mock<IEnvVariableProvider> _envVariableProvider;
        private readonly Mock<ILogger<ClientDataService>> _logger;
        private readonly UserContext _userContext;
        private Mock<HttpMessageHandler> mockHttpMessageHandler;

        public ClientDataServiceTests()
        {
            _logger = new Mock<ILogger<ClientDataService>>();
            _envVariableProvider = new Mock<IEnvVariableProvider>();
            _userContext = Mock.Of<UserContext>();
        }

        [Fact]
        public async Task Should_get_involvement_client_details()
        {
            var nonFinancialDataResponse = new NonFinancialDataDto
            {
                Document = new ClientDocumentDto
                {
                    Title = "Test Title",
                    Forenames = "Test Forenames",
                    Surname = "Test Surname",
                    RegisteredNo = "Test RegisteredNo",
                    BusinessType = "Test BusinessType",
                    BusinessSubType = "Test BusinessSubType",
                    LimitedCompanyType = "Test LimitedCompanyType"
                }
            };
            var responseMessage = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(nonFinancialDataResponse))
            };
            _envVariableProvider.Setup(obj => obj.ClientApiUrl).Returns("client/");
            var clientDataService = SetupMockData(responseMessage);
            var response = await clientDataService.GetNonFinancialDataFromClient(TestHelpers.Guids.GuidOne, CancellationToken.None);

            response.ShouldNotBeNull();
            response.ShouldBeOfType(typeof(NonFinancialDataDto));
            response.Document.Title.ShouldBe(nonFinancialDataResponse.Document.Title);
            response.Document.Forenames.ShouldBe(nonFinancialDataResponse.Document.Forenames);
            response.Document.Surname.ShouldBe(nonFinancialDataResponse.Document.Surname);
            response.Document.RegisteredNo.ShouldBe(nonFinancialDataResponse.Document.RegisteredNo);
            response.Document.BusinessType.ShouldBe(nonFinancialDataResponse.Document.BusinessType);
            response.Document.BusinessSubType.ShouldBe(nonFinancialDataResponse.Document.BusinessSubType);
            response.Document.LimitedCompanyType.ShouldBe(nonFinancialDataResponse.Document.LimitedCompanyType);
        }

        [Fact]
        public async Task Should_get_involvement_data()
        {
            var involvementDto = new List<ClientInvolvementDto>
            {
                new()
                {
                    InvolvedClientType = "InvolvedClientType",
                    InvolvementClientName = "InvolvementClientName",
                    InvolvementType = "InvolvementType",
                    InvolvedDateOfDeath = DateTime.UtcNow,
                }
            };
            var responseMessage = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(involvementDto,
                    new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase }))
            };
            _envVariableProvider.Setup(obj => obj.ClientInvolvementApiUrl).Returns("involvement/client/");
            var clientDataService = SetupMockData(responseMessage);
            var response = await clientDataService.GetClientInvolvements(TestHelpers.Guids.GuidOne, CancellationToken.None);

            response.ShouldNotBeNull();
            response.ShouldBeOfType(typeof(List<ClientInvolvementDto>));
            var involvement = response.FirstOrDefault();
            var expected = involvementDto.FirstOrDefault();
            involvement.ShouldNotBeNull();
            involvement.InvolvedClientType.ShouldBe(expected.InvolvedClientType);
            involvement.InvolvementClientName.ShouldBe(expected.InvolvementClientName);
            involvement.InvolvementType.ShouldBe(expected.InvolvementType);
            involvement.InvolvedDateOfDeath.ShouldBe(expected.InvolvedDateOfDeath);
        }


        [Fact]
        public async Task Should_get_address_data()
        {
            var addressResponse = new List<AddressResponseDto>
            {
                new AddressResponseDto()
                {
                    Document = new AddressDocumentResponseDto
                    {
                        Document =
                            "{\"Line\": [\"test\", \"\", \"\"], \"Town\": \"test\", \"Audit\": {\"Hub\": {\"Change\": {\"tenant_user\": \"64bd364c-ff99-4d00-8a73-6465912e9310\"}, \"Create\": {\"tenant_user\": \"64bd364c-ff99-4d00-8a73-6465912e9310\"}}}, \"County\": \"\", \"Country\": \"Andorra\", \"EntityId\": \"23d91839-6fd3-4d81-a7eb-1db1c0bfabc7\", \"Postcode\": \"222\", \"EntityType\": \"Address\", \"PhoneNumber\": [], \"EmailAddress\": [], \"EntityChangeType\": \"Update\", \"EntityModificationTime\": \"2021-09-03T11:40:07.4497518Z\"}",
                    },
                    Context = new AddressContextResponseDto
                    {
                        AddressContext =
                            "{\"Name\": \"Test\", \"Sail\": false, \"Prime\": false, \"Shared\": false, \"Billing\": false, \"EntityId\": \"9347d838-adc0-4fbc-89f8-15aca867df99\", \"EntityType\": \"AddressMapping\", \"Registered\": true, \"HomeAddress\": false, \"Service2006\": false, \"ClientEntityId\": \"b561c1e7-aa55-4c15-b7c4-7534beccdb84\", \"AddressEntityId\": \"23d91839-6fd3-4d81-a7eb-1db1c0bfabc7\", \"EntityChangeType\": \"Insert\", \"EntityModificationTime\": \"2021-09-03T06:37:13.1929527Z\"}"
                    }
                }
            };
            var responseMessage = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(addressResponse,
                    new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase }))
            };
            _envVariableProvider.Setup(obj => obj.ClientAddressApiUrl).Returns("address/client/");
            var clientDataService = SetupMockData(responseMessage);
            var response = await clientDataService.GetClientAddress(TestHelpers.Guids.GuidTwo, CancellationToken.None);

            response.ShouldNotBeNull();
            response.ShouldBeOfType(typeof(List<AddressResponseDto>));
            response.FirstOrDefault()?.Document.Document.ShouldNotBe(string.Empty);
            response.FirstOrDefault()?.Context.AddressContext.ShouldNotBe(string.Empty);
            response.ShouldBeEquivalentTo(addressResponse);
        }

        [Fact]
        public async Task Should_return_enpty_list_when_address_not_found()
        {
            var responseMessage = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound
            };
            _envVariableProvider.Setup(obj => obj.ClientAddressApiUrl).Returns("address/client/");
            var clientDataService = SetupMockData(responseMessage);

            var response = await clientDataService.GetClientAddress(TestHelpers.Guids.GuidTwo, CancellationToken.None);

            response.ShouldBeEmpty<AddressResponseDto>();
        }

        private IClientDataService SetupMockData(HttpResponseMessage responseMessage)
        {
            _envVariableProvider.Setup(obj => obj.ClientApiKey).Returns("test");
            _envVariableProvider.Setup(obj => obj.ClientApiScheme).Returns("https");
            _envVariableProvider.Setup(obj => obj.ClientApiHost).Returns("api.elements-development.iris.co.uk/accountsproduction-accountperiod/v1");
            var mockFactory = new Mock<IHttpClientFactory>();
            mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(responseMessage);
            var client = new HttpClient(mockHttpMessageHandler.Object);
            mockFactory.Setup(_ => _.CreateClient(It.IsAny<string>())).Returns(client);

            IClientDataService clientDataService =
                new ClientDataService(client, _envVariableProvider.Object, _userContext, _logger.Object);
            return clientDataService;
        }
    }
}
