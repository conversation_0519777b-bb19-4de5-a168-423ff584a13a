﻿using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Infrastructure.Services;
using Amazon.SimpleNotificationService;
using Amazon.SimpleNotificationService.Model;
using Iris.AccountsProduction.Common.Toolkit.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Infrastructure.Services
{
    public class SnsServiceClientTests
    {
        private readonly ISnsServiceClient _snsServiceClient;
        private readonly Mock<IAmazonSimpleNotificationService> _awsClient;
        private readonly Mock<ISnsServiceClient> _snsServiceMoq;

        private Dictionary<string, string> MessageAttributes =>
            new Dictionary<string, string> { { "clientId", Guid.NewGuid().ToString() }, { "periodId", Guid.NewGuid().ToString() } };

        public SnsServiceClientTests()
        {
            _awsClient = new Mock<IAmazonSimpleNotificationService>();
            var logger = Mock.Of<ILogger<SnsServiceClient>>();

            _snsServiceClient = new SnsServiceClient(_awsClient.Object, logger);
            _snsServiceMoq = new Mock<ISnsServiceClient>();
        }

        [Fact]
        public async Task Should_throw_exception_for_empty_topic_arn()
        {
            await Should.ThrowAsync<ArgumentNullException>(async () =>
            {
                await _snsServiceClient.PublishMessage("", "test message", "", CancellationToken.None,
                    MessageAttributes);
            });
        }

        [Fact]
        public async Task Should_publish_message()
        {
            _awsClient.Setup(x =>
                    x.PublishAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), CancellationToken.None))
                .ReturnsAsync(new PublishResponse());

            await _snsServiceMoq.Object.PublishMessage("topicArn", "test message", "", CancellationToken.None,
                MessageAttributes);

            _snsServiceMoq.Verify(
                x => x.PublishMessage(It.IsAny<string>(), It.IsAny<string>(), "", CancellationToken.None,
                    It.IsAny<Dictionary<string, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task Should_throw_sns_custom_exception()
        {
            _awsClient.Setup(x =>
                    x.PublishAsync(It.IsAny<PublishRequest>(), CancellationToken.None))
                .Throws(new Exception("sns exception"));

            await Should.ThrowAsync<ExternalException>(async () =>
            {
                await _snsServiceClient.PublishMessage("topicArn", "test message", "subject",
                    CancellationToken.None, MessageAttributes);
            });
        }
    }
}
