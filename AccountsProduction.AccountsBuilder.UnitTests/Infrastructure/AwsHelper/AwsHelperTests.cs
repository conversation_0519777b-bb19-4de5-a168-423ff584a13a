﻿using AccountsProduction.AccountsBuilder.Infrastructure.AwsHelper;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Infrastructure.AwsHelper
{
    public class AwsHelperTests
    {
        [Fact]
        public void Should_throw_InvalidFormatException_if_url_is_not_valid()
        {
            string endpointUrl = "https://api.invalidendpoint.co.uk/abc/v1/def/1";
            Action call = () => AwsSigningHelper.GenerateAwsSignedGetRequest(endpointUrl, string.Empty,
            string.Empty, string.Empty, string.Empty);

            call.ShouldThrow<FormatException>();
        }
    }
}
