﻿using AccountsProduction.AccountsBuilder.Infrastructure.Persistence.Repositories;
using Amazon.DynamoDBv2.DataModel;
using Amazon.Runtime;
using Iris.AccountsProduction.Common.Toolkit.Exceptions;
using Iris.Elements.DynamoDb;
using Moq;
using Shouldly;
using System.Net;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Infrastructure.Repositories
{
    public class AccountsBuilderRepositoryTests
    {
        private readonly Mock<IDynamoDbContextWrapper> _dynamoDbContextMock;
        private readonly AccountsBuilderRepository _repository;
        private readonly Guid _clientId = Guid.NewGuid();
        private readonly Guid _periodId = Guid.NewGuid();
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;

        public AccountsBuilderRepositoryTests()
        {
            _dynamoDbContextMock = new Mock<IDynamoDbContextWrapper>();
            _repository = new AccountsBuilderRepository(_dynamoDbContextMock.Object);
        }

        [Fact]
        public async Task Should_return_accounts_builder()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
            { ClientId = _clientId, PeriodId = _periodId };
            _dynamoDbContextMock.Setup(x => x.LoadAsync<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(It.IsAny<Guid>(),
                It.IsAny<Guid>(),
                It.IsAny<DynamoDBOperationConfig>(), CancellationToken.None)).ReturnsAsync(accountsBuilder);

            var result = await _repository.Get(_clientId, _periodId, CancellationToken.None);

            result.ShouldBe(accountsBuilder);
            result.PeriodId.ShouldBe(_periodId);
            result.ClientId.ShouldBe(_clientId);
        }

        [Fact]
        public async Task Should_return_accounts_builders()
        {
            var accountsBuilders = new List<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder> {
                new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder{ ClientId = _clientId, PeriodId = _periodId }
            };
            _dynamoDbContextMock.Setup(x => x.QueryAsync<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(It.IsAny<Guid>(),
                It.IsAny<DynamoDBOperationConfig>()).GetRemainingAsync(CancellationToken.None)).ReturnsAsync(accountsBuilders);

            var result = await _repository.Get(_processId, CancellationToken.None);

            result.ShouldBe(accountsBuilders.FirstOrDefault());
            result.PeriodId.ShouldBe(_periodId);
            result.ClientId.ShouldBe(_clientId);
        }

        [Fact]
        public async Task Should_save_accounts_builder()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
            { ClientId = _clientId, PeriodId = _periodId };

            await _repository.Save(accountsBuilder, CancellationToken.None);

            _dynamoDbContextMock.Verify(m =>
                m.SaveAsync(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), It.IsAny<DynamoDBOperationConfig>(),
                    CancellationToken.None), Times.Once);
        }


        [Fact]
        public async Task Should_catch_amazon_service_exception_on_get()
        {
            _dynamoDbContextMock.Setup(x =>
                    x.LoadAsync<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(_clientId, _periodId,
                        It.IsAny<DynamoDBOperationConfig>(), CancellationToken.None))
                .ThrowsAsync(new AmazonServiceException("Rate of requests exceeds the allowed throughput."));

            await Should.ThrowAsync<InternalException>(async () =>
            {
                await _repository.Get(_clientId, _periodId, CancellationToken.None);
            });
        }

        [Fact]
        public async Task Should_catch_service_unavailable_exception_on_get()
        {
            _dynamoDbContextMock.Setup(x =>
                    x.LoadAsync<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(_clientId, _periodId,
                        It.IsAny<DynamoDBOperationConfig>(), CancellationToken.None))
                .ThrowsAsync(new AmazonServiceException("ServiceUnavailable")
                { StatusCode = HttpStatusCode.ServiceUnavailable });

            await Should.ThrowAsync<InternalException>(async () =>
            {
                await _repository.Get(_clientId, _periodId, CancellationToken.None);
            });
        }

        [Fact]
        public async Task Should_catch_internal_server_error_on_get()
        {
            _dynamoDbContextMock.Setup(x =>
                    x.LoadAsync<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(_clientId, _periodId,
                        It.IsAny<DynamoDBOperationConfig>(), CancellationToken.None))
                .ThrowsAsync(new AmazonServiceException("InternalServerErrorException")
                { StatusCode = HttpStatusCode.ServiceUnavailable });

            await Should.ThrowAsync<InternalException>(async () =>
            {
                await _repository.Get(_clientId, _periodId, CancellationToken.None);
            });
        }

        [Fact]
        public async Task Should_catch_amazon_service_exception_on_get_all()
        {
            _dynamoDbContextMock.Setup(x => x.QueryAsync<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(It.IsAny<Guid>(),
                It.IsAny<DynamoDBOperationConfig>()).GetRemainingAsync(CancellationToken.None))
                .ThrowsAsync(new AmazonServiceException("Rate of requests exceeds the allowed throughput."));


            await Should.ThrowAsync<InternalException>(async () =>
            {
                await _repository.Get(_processId, CancellationToken.None);
            });
        }

        [Fact]
        public async Task Should_catch_service_unavailable_exception_on_get_all()
        {
            _dynamoDbContextMock.Setup(x => x.QueryAsync<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(It.IsAny<Guid>(),
                It.IsAny<DynamoDBOperationConfig>()).GetRemainingAsync(CancellationToken.None))
                 .ThrowsAsync(new AmazonServiceException("ServiceUnavailable")
                 { StatusCode = HttpStatusCode.ServiceUnavailable });

            await Should.ThrowAsync<InternalException>(async () =>
            {
                await _repository.Get(_processId, CancellationToken.None);
            });
        }

        [Fact]
        public async Task Should_catch_internal_server_error_on_get_all()
        {
            _dynamoDbContextMock.Setup(x => x.QueryAsync<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(It.IsAny<Guid>(),
                It.IsAny<DynamoDBOperationConfig>()).GetRemainingAsync(CancellationToken.None))
               .ThrowsAsync(new AmazonServiceException("InternalServerErrorException")
               { StatusCode = HttpStatusCode.ServiceUnavailable });

            await Should.ThrowAsync<InternalException>(async () =>
            {
                await _repository.Get(_processId, CancellationToken.None);
            });
        }

        [Fact]
        public async Task Should_catch_amazon_service_exception_on_save()
        {
            var accountsBuilder =
                new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder();

            _dynamoDbContextMock.Setup(x => x.SaveAsync(accountsBuilder,
                    It.IsAny<DynamoDBOperationConfig>(), CancellationToken.None))
                .ThrowsAsync(new AmazonServiceException("Rate of requests exceeds the allowed throughput."));

            await Should.ThrowAsync<InternalException>(async () =>
            {
                await _repository.Save(
                    accountsBuilder,
                    CancellationToken.None);
            });
        }

        [Fact]
        public async Task Should_catch_service_unavailable_exception_on_save()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder();

            _dynamoDbContextMock.Setup(x => x.SaveAsync(accountsBuilder,
                    It.IsAny<DynamoDBOperationConfig>(), CancellationToken.None))
                .ThrowsAsync(new AmazonServiceException("ServiceUnavailable")
                { StatusCode = HttpStatusCode.ServiceUnavailable });

            await Should.ThrowAsync<InternalException>(async () =>
            {
                await _repository.Save(
                    accountsBuilder,
                    CancellationToken.None);
            });
        }

        [Fact]
        public async Task Should_catch_internal_server_error_on_save()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder();

            _dynamoDbContextMock.Setup(x => x.SaveAsync(accountsBuilder,
                    It.IsAny<DynamoDBOperationConfig>(), CancellationToken.None))
                .ThrowsAsync(new AmazonServiceException("InternalServerErrorException")
                { StatusCode = HttpStatusCode.ServiceUnavailable });

            await Should.ThrowAsync<InternalException>(async () =>
            {
                await _repository.Save(
                    accountsBuilder,
                    CancellationToken.None);
            });
        }
       
    }
}
