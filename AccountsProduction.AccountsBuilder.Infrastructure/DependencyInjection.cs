﻿using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Infrastructure.IntegrationEventHandler;
using AccountsProduction.AccountsBuilder.Infrastructure.Persistence.Configurations;
using AccountsProduction.AccountsBuilder.Infrastructure.Persistence.Repositories;
using AccountsProduction.AccountsBuilder.Infrastructure.Services;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Amazon.Lambda;
using Amazon.SimpleNotificationService;
using Amazon.SQS;
using Dapper.FluentMap;
using Iris.AccountsProduction.Common.Toolkit.ExtensionMethods;
using Iris.Elements.DynamoDb;
using Iris.Platform.Eventbus.Client.Dotnet.Helpers;
using Iris.Platform.WebApi.Infrastructure;
using Iris.Subscription.Featuretoggle.Client.Helpers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;

namespace AccountsProduction.AccountsBuilder.Infrastructure
{
    [ExcludeFromCodeCoverage]
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(this IServiceCollection services,
            IConfiguration configuration, string appName)
        {
            services.AddAWSServices(configuration)
                .AddDapperConfig()
                .AddGeneralServices()
                .AddHttpClient()
                .AddEventBusServices(configuration, appName)
                .AddServices(configuration);

            return services;
        }

        public static IServiceCollection AddGeneralServices(this IServiceCollection services)
        {
            services.AddScoped<IEnvVariableProvider, EnvVariableProvider>();
            services.AddScoped<IAccountsBuilderRepository, AccountsBuilderRepository>();
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssemblies(Assembly.GetExecutingAssembly(), Assembly.GetAssembly(typeof(TrialBalanceChangeNotificationEventHandler))));
            return services;
        }

        public static IServiceCollection AddServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IClientDataService, ClientDataService>();
            services.AddScoped<IAccountPeriodService, AccountPeriodService>();
            services.AddScoped<ITrialBalanceService, TrialBalanceService>();
            services.AddScoped<IAccountsBuilderService, AccountsBuilderService>();
            services.AddScoped<IAccountsProductionService, AccountsProductionService>();
            services.AddScoped<IGenerateReportDataService, GenerateReportDataService>();
            services.AddTransient<IDomainEventService, DomainEventService>();
            services.AddLambdaFeatureClient();
            services.AddLicenseServices();
            services.AddClientSecurityService(configuration);
            services.AddFeatureServiceClient();
            services.AddPlatformInfrastructureLibrary(configuration, true);

            return services;
        }

        public static IServiceCollection AddAWSServices(this IServiceCollection services, IConfiguration configuration)
        {
            var awsOptions = configuration.GetAWSOptions();

            services.AddDefaultAWSOptions(awsOptions);
            services.AddAWSService<IAmazonDynamoDB>();
            services.AddAWSService<IAmazonSimpleNotificationService>();
            services.AddAWSService<IAmazonLambda>();
            services.AddAWSService<IAmazonSQS>();

            services.AddScoped<IDynamoDBContext, DynamoDBContext>();
            services.AddScoped<IDynamoDbContextWrapper, DynamoDbContextWrapper>();
            services.AddScoped<ISnsServiceClient, SnsServiceClient>();
            services.AddScoped<ISqsServiceClient, SqsServiceClient>();

            services.AddSingleton(typeof(ILogger), typeof(Logger<Iris.Elements.LambdaClients.Core.AwsLambdaClient>));
            return services;
        }


        public static IServiceCollection AddDapperConfig(this IServiceCollection services)
        {
#if DEBUG
            FluentMapper.EntityMaps.Clear();
#endif

            FluentMapper.Initialize(configuration => { configuration.AddMap(new GroupAccountSubAccountIntervalConfiguration()); });
            services.AddScoped<IGroupAccountSubAccountIntervalRepository, GroupAccountSubAccountIntervalRepository>();
            return services;
        }

        public static IServiceCollection AddEventBusServices(this IServiceCollection services, IConfiguration configuration, string appName)
        {
            services.AddEventBus(configuration, appName);
            var responseTopics = new List<string> { "practice" };
            services.EnableFireAndWaitForResponse(responseTopics);
            return services;
        }
    }
}
