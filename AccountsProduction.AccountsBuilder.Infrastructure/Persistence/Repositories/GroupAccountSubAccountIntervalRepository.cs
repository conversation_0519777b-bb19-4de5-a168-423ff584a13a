﻿using AccountsProduction.AccountsBuilder.Domain;
using Dapper;
using Iris.AccountsProduction.Common.Toolkit.Exceptions;
using Microsoft.Extensions.Configuration;

namespace AccountsProduction.AccountsBuilder.Infrastructure.Persistence.Repositories
{
    public class GroupAccountSubAccountIntervalRepository : IGroupAccountSubAccountIntervalRepository
    {
        private const string LoadSql =
            "select * from accounts_builder.group_account_subaccount_interval where account_chart_identifier = @accountsChartIdentifier and group_structure_code = @groupStructureCode";

        private readonly IConfiguration _configuration;

        private IEnumerable<GroupAccountSubAccountInterval> _cachedGroupAccountSubAccountIntervalList;

        public GroupAccountSubAccountIntervalRepository(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public IEnumerable<GroupAccountSubAccountInterval> GetCachedGroupAccountSubAccountIntervalList()
        {
            if (_cachedGroupAccountSubAccountIntervalList == null)
                throw new InternalException("GroupAccountSubAccount cache is not initialised");
            return _cachedGroupAccountSubAccountIntervalList;
        }

        public async Task LoadGroupAccountSubAccountIntervalListInCacheAsync(string accountsChartIdentifier, int groupStructureCode)
        {
            await using var conn = DbConnectionHandler.CreateDbConnection(_configuration);
            await conn.OpenAsync();
            try
            {
                var groupAccountSubAccountIntervals = await conn.QueryAsync<GroupAccountSubAccountInterval>(LoadSql,
                    new { accountsChartIdentifier, groupStructureCode });
                _cachedGroupAccountSubAccountIntervalList = groupAccountSubAccountIntervals;
            }
            finally
            {
                await conn.CloseAsync();
            }
        }
    }
}