﻿using AccountsProduction.AccountsBuilder.Domain;
using Dapper.FluentMap.Mapping;
using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.Infrastructure.Persistence.Configurations
{
    [ExcludeFromCodeCoverage]
    public class GroupAccountSubAccountIntervalConfiguration : EntityMap<GroupAccountSubAccountInterval>
    {
        public GroupAccountSubAccountIntervalConfiguration()
        {
            Map(interval => interval.Id).ToColumn("id");
            Map(interval => interval.GroupNo).ToColumn("group_no");
            Map(interval => interval.PartNo).ToColumn("part_no");
            Map(interval => interval.AccountIntervalFrom).ToColumn("account_interval_from");
            Map(interval => interval.SubAccountIntervalFrom).ToColumn("subaccount_interval_from");
            Map(interval => interval.AccountIntervalTo).ToColumn("account_interval_to");
            Map(interval => interval.SubAccountIntervalTo).ToColumn("subaccount_interval_to");
            Map(interval => interval.GroupStructureCode).ToColumn("group_structure_code");
            Map(interval => interval.AccountChartIdentifier).ToColumn("account_chart_identifier");
        }
    }
}