﻿using AccountsProduction.AccountsBuilder.Domain.Reporting.Other;
using AccountsProduction.AccountsBuilder.Reporting.Application;
using AccountsProduction.AccountsBuilder.Reporting.Domain;
using AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet;
using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using AccountsProduction.AccountsBuilder.Reporting.Domain.ProfitAndLoss;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence
{
    [ExcludeFromCodeCoverage]
    public class AccountsProductionReportingDbContext : DbContext, IAccountsProductionReportingDbContext
    {
        public virtual DbSet<Tenant> Tenants { get; set; } = null!;

        public virtual DbSet<Client> Clients { get; set; } = null!;

        public virtual DbSet<ReportingPeriod> ReportingPeriods { get; set; } = null!;

        public virtual DbSet<ProfitAndLossFRS105> ProfitAndLossesFRS105 { get; set; } = null!;

        public virtual DbSet<ProfitAndLossCompaniesAct> ProfitAndLossesCompaniesActs { get; set; } = null!;

        public virtual DbSet<ProfitAndLossNonCorp> ProfitAndLossesNonCorp { get; set; } = null!;

        public virtual DbSet<BalanceSheetFRS105> BalanceSheetsFRS105 { get; set; } = null!;

        public virtual DbSet<BalanceSheetGeneral> BalanceSheetsGeneral { get; set; } = null!;

        public virtual DbSet<BalanceSheetLLP> BalanceSheetsLLP { get; set; } = null!;

        public virtual DbSet<BalanceSheetCH> BalanceSheetsCH { get; set; } = null!;

        public virtual DbSet<BalanceSheetNonCorp> BalanceSheetsNonCorp { get; set; } = null!;

        public virtual DbSet<BalanceSheetNonCorp> BalanceSheetsIFRS { get; set; } = null!;

        public virtual DbSet<Member> Members { get; set; } = null!;

        public virtual DbSet<Signature> Signatures { get; set; } = null!;

        public virtual DbSet<NoteAccountingPolicies> NotesAccountingPolicies { get; set; } = null!;

        public virtual DbSet<NoteProfitAndLoss> NotesProfitAndLoss { get; set; } = null!;

        public virtual DbSet<NoteBalanceSheet> NotesBalanceSheet { get; set; } = null!;

        public virtual DbSet<NoteOther> NotesOther { get; set; } = null!;

        public virtual DbSet<Reports> Reports { get; set; } = null;

        public virtual DbSet<NoteSofa> NoteSofa { get; set; } = null;

        public virtual DbSet<MultiColumnToken> MultiColumnToken { get; set; } = null;

        public virtual DbSet<DplSummaryCalcs> DplSummaryCalcs { get; set; } = null;

        public virtual DbSet<LineItem> LineItems { get; set; } = null!;
        public AccountsProductionReportingDbContext() { }

        public AccountsProductionReportingDbContext(DbContextOptions<AccountsProductionReportingDbContext> options) :
            base(options)
        {
            AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
            AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", true);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        }
    }
}
