﻿using AccountsProduction.AccountsBuilder.Reporting.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations
{
    public class MemberConfiguration : IEntityTypeConfiguration<Member>
    {
        public void Configure(EntityTypeBuilder<Member> builder)
        {
            builder.ToTable("Member", "public");

            builder.Property(e => e.Id).HasDefaultValueSql("nextval('\"Member_id_seq\"'::regclass)");
            builder.Property(e => e.InvolvementUUID).IsRequired(true);
            builder.Property(e => e.EntityName).HasColumnType("character varying");
            builder.Property(e => e.MemberSubtype).HasColumnType("character varying");
            builder.Property(e => e.MemberType).HasColumnType("character varying");
            builder.Property(e => e.PersonSurname).HasColumnType("character varying");
            builder.Property(e => e.PersonTitle).HasColumnType("character varying");
            builder.Property(e => e.ActiveFrom).HasColumnType("date");
            builder.Property(e => e.ActiveTo).HasColumnType("date");
            builder.Property(e => e.DateOfDeath).HasColumnType("date");
            builder.Property(e => e.InvolvementId).IsRequired(true).HasColumnType("integer");
            builder.Property(e => e.PDOCode).IsRequired(false).HasColumnType("integer");
            builder.Property(e => e.IsDeleted).IsRequired(true).HasColumnType("boolean");

            builder.HasOne(d => d.Client)
                .WithMany(p => p!.Member)
                .HasForeignKey(d => d.ClientId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("member_fk");
        }
    }
}