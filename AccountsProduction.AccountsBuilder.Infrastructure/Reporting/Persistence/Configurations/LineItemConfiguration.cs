﻿using AccountsProduction.AccountsBuilder.Reporting.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations
{
    public class LineItemConfiguration : IEntityTypeConfiguration<LineItem>
    {
        public void Configure(EntityTypeBuilder<LineItem> builder)
        {
            builder.ToTable("LineItem", "public");

            builder.Property(e => e.Id).ValueGeneratedOnAdd();

            builder.Property(e => e.Category).HasColumnType("character varying");

            builder.Property(e => e.AccountCode).HasColumnType("character varying");

            builder.Property(e => e.AccountDescription).HasColumnType("character varying");

            builder.Property(e => e.CurrentValue).HasColumnType("numeric");

            builder.Property(e => e.PreviousValue).HasColumnType("numeric");

            builder.Property(e => e.DisplayOrder).HasColumnType("numeric");

            builder.Property(e => e.SubAccountCode).HasColumnType("character varying");

            builder.Property(e => e.InvolvementId).IsRequired(false).HasColumnType("integer");

            builder.Property(e => e.SectorId).IsRequired(false).HasColumnType("uuid");

            builder.HasOne(d => d.ReportingPeriod)
                .WithMany(p => p!.LineItem)
                .HasForeignKey(d => new { d.ClientId, d.AccountPeriodId })
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("lineitem_fk");
        }
    }
}
