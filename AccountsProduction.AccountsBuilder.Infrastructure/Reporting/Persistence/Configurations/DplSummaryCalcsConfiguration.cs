﻿using AccountsProduction.AccountsBuilder.Domain.Reporting.Other;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations
{
    public class DplSummaryCalcsConfiguration : IEntityTypeConfiguration<DplSummaryCalcs>
        {
        public void Configure(EntityTypeBuilder<DplSummaryCalcs> builder)
        {
            builder.ToTable("DPLSummaryCalcs", "public");

            builder.Property(e => e.Id).ValueGeneratedOnAdd();
            builder.Property(e => e.DPLCalcType).HasColumnType("character");
            builder.Property(e => e.DPLValue).HasColumnType("numeric");

                builder.HasOne(d => d.ReportingPeriod)
                .WithMany(p => p!.DplSummaryCalcs)
                .HasForeignKey(d => new { d.ClientId, d.AccountPeriodId })
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("dplsummarycalcs_fk");
        }
    }
}
