﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.BalanceSheet
{
    public class BalanceSheetFRS105Configuration : IEntityTypeConfiguration<BalanceSheetFRS105>
    {
        public void Configure(EntityTypeBuilder<BalanceSheetFRS105> builder)
        {
            builder.ToTable("BalanceSheetFRS105", "public");

            builder.HasKey(e => new { e.ClientId, e.AccountPeriodId })
                .HasName("balancesheetfrs105_pk");

            builder.Property(e => e.CalledUpShareCapitalNotPaid).HasColumnType("numeric");

            builder.Property(e => e.FixedAssets).HasColumnType("numeric");

            builder.Property(e => e.CurrentAssets).HasColumnType("numeric");

            builder.Property(e => e.PrepaymentsAndAccruedIncome).HasColumnType("numeric");

            builder.Property(e => e.CreditorsAmountsFallingDueWithinOneYear).HasColumnType("numeric");

            builder.Property(e => e.NetCurrentAssetsOrLiabilities).HasColumnType("numeric");

            builder.Property(e => e.TotalAssetsLessCurrentLiabilities).HasColumnType("numeric");

            builder.Property(e => e.CreditorsAmountsFallingAfterMoreThanOneYear).HasColumnType("numeric");

            builder.Property(e => e.ProvisionsForLiabilities).HasColumnType("numeric");

            builder.Property(e => e.AccrualsAndDeferredIncome).HasColumnType("numeric");

            builder.Property(e => e.NetAssets).HasColumnType("numeric");

            builder.Property(e => e.CapitalAndReserves).HasColumnType("numeric");

            builder.HasOne(d => d.ReportingPeriod)
               .WithOne(p => p!.BalanceSheetFRS105!)
               .HasForeignKey<BalanceSheetFRS105>(d => new { d.ClientId, d.AccountPeriodId })
               .OnDelete(DeleteBehavior.ClientSetNull)
               .HasConstraintName("balancesheetfrs105_fk");
        }
    }
}
