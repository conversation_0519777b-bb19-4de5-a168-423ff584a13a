﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.BalanceSheet
{
    public class BalanceSheetGeneralConfiguration : IEntityTypeConfiguration<BalanceSheetGeneral>
    {
        public void Configure(EntityTypeBuilder<BalanceSheetGeneral> builder)
        {
            builder.ToTable("BalanceSheetGeneral", "public");

            builder.HasKey(e => new { e.ClientId, e.AccountPeriodId })
                .HasName("balancesheetgeneral_pk");

            builder.Property(e => e.CalledUpShareCapitalNotPaid).HasColumnType("numeric");

            builder.Property(e => e.Goodwill).HasColumnType("numeric");

            builder.Property(e => e.IntangibleAssets).HasColumnType("numeric");

            builder.Property(e => e.TangibleFixedAssets).HasColumnType("numeric");

            builder.Property(e => e.FixedAssetInvestments).HasColumnType("numeric");

            builder.Property(e => e.CurrentAssetInvestments).HasColumnType("numeric");

            builder.Property(e => e.InvestmentProperty).HasColumnType("numeric");

            builder.Property(e => e.Stock).HasColumnType("numeric");

            builder.Property(e => e.Debtors).HasColumnType("numeric");

            builder.Property(e => e.CashAtBankAndInHand).HasColumnType("numeric");

            builder.Property(e => e.PrepaymentsAndAccruedIncome).HasColumnType("numeric");

            builder.Property(e => e.CreditorsAmountsFallingDueWithinOneYear).HasColumnType("numeric");

            builder.Property(e => e.CreditorsAmountsFallingAfterMoreThanOneYear).HasColumnType("numeric");

            builder.Property(e => e.ProvisionsForLiabilities).HasColumnType("numeric");

            builder.Property(e => e.PensionSchemeAssetsLiabilities).HasColumnType("numeric");

            builder.Property(e => e.HealthcareObligatons).HasColumnType("numeric");

            builder.Property(e => e.AccrualsAndDeferredIncome).HasColumnType("numeric");

            builder.Property(e => e.CalledUpShareCapital).HasColumnType("numeric");

            builder.Property(e => e.SharePremiumReserve).HasColumnType("numeric");

            builder.Property(e => e.RevaluationReserve).HasColumnType("numeric");

            builder.Property(e => e.CapitalRedemptionReserve).HasColumnType("numeric");

            builder.Property(e => e.OtherReserves1).HasColumnType("numeric");

            builder.Property(e => e.OtherReserves2).HasColumnType("numeric");

            builder.Property(e => e.FairValueReserve).HasColumnType("numeric");

            builder.Property(e => e.ProfitAndLossReserve).HasColumnType("numeric");

            builder.Property(e => e.NonControllingInterests).HasColumnType("numeric");

            builder.Property(e => e.HerdBasis).HasColumnType("numeric");

            builder.HasOne(d => d.ReportingPeriod)
                   .WithOne(p => p.BalanceSheetGeneral!)
                   .HasForeignKey<BalanceSheetGeneral>(d => new { d.ClientId, d.AccountPeriodId })
                   .OnDelete(DeleteBehavior.ClientSetNull)
                   .HasConstraintName("balancesheetgeneral_fk");
        }
    }
}
