﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.BalanceSheet
{
    public class BalanceSheetLLPConfiguration : IEntityTypeConfiguration<BalanceSheetLLP>
    {
        public void Configure(EntityTypeBuilder<BalanceSheetLLP> builder)
        {
            builder.ToTable("BalanceSheetLLP", "public");

            builder.HasKey(e => new { e.ClientId, e.AccountPeriodId })
                .HasName("balancesheetllp_pk");

            builder.Property(e => e.TotalMembersInterests).HasColumnType("numeric");

            builder.Property(e => e.MembersOtherInterests).HasColumnType("numeric");

            builder.Property(e => e.MembersCapital).HasColumnType("numeric");

            builder.Property(e => e.OtherDebtsDueToMembers).HasColumnType("numeric");

            builder.Property(e => e.LoansAndOtherDebtsDueToMembers).HasColumnType("numeric");

            builder.HasOne(d => d.ReportingPeriod)
                   .WithOne(p => p!.BalanceSheetLLP!)
                   .HasForeignKey<BalanceSheetLLP>(d => new { d.ClientId, d.AccountPeriodId })
                   .OnDelete(DeleteBehavior.Cascade)
                   .HasConstraintName("balancesheetllp_fk");
        }
    }
}
