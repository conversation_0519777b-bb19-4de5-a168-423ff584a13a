﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.ProfitAndLoss;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.ProfitAndLoss
{
    public class ProfitAndLossNonCorpConfiguration : IEntityTypeConfiguration<ProfitAndLossNonCorp>
    {
        public void Configure(EntityTypeBuilder<ProfitAndLossNonCorp> builder)
        {
            builder.ToTable("ProfitAndLossNonCorp", "public");

            builder.HasKey(e => new { e.ClientId, e.AccountPeriodId })
                .HasName("profitandlossnoncorp_pk");

            builder.Property(e => e.Sales).HasColumnType("numeric");

            builder.Property(e => e.CostOfSales).HasColumnType("numeric");

            builder.Property(e => e.OtherIncome).HasColumnType("numeric");

            builder.Property(e => e.Expenses).HasColumnType("numeric");

            builder.Property(e => e.FinanceCosts).HasColumnType("numeric");

            builder.Property(e => e.PartnerAppropriations).HasColumnType("numeric");

            builder.Property(e => e.Depreciation).HasColumnType("numeric");


            builder.HasOne(d => d.ReportingPeriod)
                .WithOne(p => p!.ProfitAndLossNonCorp!)
                .HasForeignKey<ProfitAndLossNonCorp>(d => new { d.ClientId, d.AccountPeriodId })
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("profitandlossnoncorp_fk");
        }
    }
}
