﻿using AccountsProduction.AccountsBuilder.Reporting.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations
{
    public class ReportingPeriodConfiguration : IEntityTypeConfiguration<ReportingPeriod>
    {
        public void Configure(EntityTypeBuilder<ReportingPeriod> builder)
        {
            builder.ToTable("ReportingPeriod", "public");

            builder.HasKey(e => new { e.ClientId, e.AccountPeriodId })
                .HasName("reportingperiod_pk");

            builder.Property(c => c.ReportingPeriodStartDate).HasColumnType("date");
            builder.Property(c => c.ReportingPeriodEndDate).HasColumnType("date");
            builder.Property(c => c.TenantAccountantsReportSignatureDate).HasColumnType("date");
            builder.Property(e => e.ReportingStandard).HasColumnType("character varying");
            builder.Property(e => e.EntitySize).HasColumnType("character varying");
            builder.Property(e => e.TradingStatus).HasColumnType("character varying");
            builder.Property(e => e.Terminology).HasColumnType("character varying");
            builder.Property(e => e.DormantStatus).HasColumnType("character varying");
            builder.Property(e => e.ChoiceOfStatement).HasColumnType("character varying");
            builder.Property(e => e.ReportVersion).IsRequired(false).HasColumnType("character varying");
            builder.Property(e => e.WatermarkText).IsRequired(false).HasColumnType("character varying");
            builder.Property(e => e.IncludeAccountantsReport).IsRequired().HasColumnType("bool");
            builder.Property(e => e.ReviseType).IsRequired(false).HasColumnType("character varying");
            builder.Property(e => e.CharitySize).IsRequired(false).HasColumnType("character varying");

            builder.HasOne(d => d.Client)
                .WithMany(p => p!.ReportingPeriod!)
                .HasForeignKey(d => d.ClientId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("reportingperiod_fk");
        }
    }
}
