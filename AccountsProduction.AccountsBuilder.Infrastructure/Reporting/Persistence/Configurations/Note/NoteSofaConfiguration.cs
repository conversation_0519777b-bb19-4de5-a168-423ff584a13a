using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.Note
{
    public class NoteSofaConfiguration : NoteConfigurationBase<NoteSofa>
    {
        public override void Configure(EntityTypeBuilder<NoteSofa> builder)
        {
            builder.ToTable("NoteSofa", "public");

            builder.HasOne(d => d.ReportingPeriod)
                .WithMany(p => p!.NoteSofa)
                .HasForeignKey(d => new { d.ClientId, d.AccountPeriodId })
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("notesofa_fk");

            base.Configure(builder);
        }
    }
} 