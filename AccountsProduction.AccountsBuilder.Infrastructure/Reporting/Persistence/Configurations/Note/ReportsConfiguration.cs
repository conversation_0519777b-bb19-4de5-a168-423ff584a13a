﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.Note
{
    public class ReportsConfiguration : NoteConfigurationBase<Reports>
    {
        public override void Configure(EntityTypeBuilder<Reports> builder)
        {
            builder.ToTable("Reports", "public");

            builder.HasOne(d => d.ReportingPeriod)
                .WithMany(p => p!.Reports)
                .HasForeignKey(d => new { d.ClientId, d.AccountPeriodId })
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("reports_fk");

            base.Configure(builder);
        }
    }
}
