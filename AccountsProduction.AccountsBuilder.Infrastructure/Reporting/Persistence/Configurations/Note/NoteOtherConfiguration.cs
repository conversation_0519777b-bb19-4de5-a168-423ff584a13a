﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.Note
{
    public class NoteOtherConfiguration : NoteConfigurationBase<NoteOther>
    {
        public override void Configure(EntityTypeBuilder<NoteOther> builder)
        {
            builder.ToTable("NoteOther", "public");

            builder.HasOne(d => d.ReportingPeriod)
                .WithMany(p => p!.NoteOther)
                .HasForeignKey(d => new { d.ClientId, d.AccountPeriodId })
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("noteother_fk");

            base.Configure(builder);
        }
    }
}
