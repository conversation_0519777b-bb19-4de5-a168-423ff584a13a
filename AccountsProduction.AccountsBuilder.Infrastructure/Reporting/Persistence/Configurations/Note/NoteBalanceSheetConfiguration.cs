﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.Note
{
    public class NoteBalanceSheetConfiguration : NoteConfigurationBase<NoteBalanceSheet>
    {
        public override void Configure(EntityTypeBuilder<NoteBalanceSheet> builder)
        {
            builder.ToTable("NoteBalanceSheet", "public");

            builder.HasOne(d => d.ReportingPeriod)
                .WithMany(p => p!.NoteBalanceSheet)
                .HasForeignKey(d => new { d.ClientId, d.AccountPeriodId })
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("notebalancesheet_fk");

            base.Configure(builder);
        }
    }
}
