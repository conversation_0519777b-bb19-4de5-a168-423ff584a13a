﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.Note
{
    public class NoteAccountingPoliciesConfiguration : NoteConfigurationBase<NoteAccountingPolicies>
    {
        public override void Configure(EntityTypeBuilder<NoteAccountingPolicies> builder)
        {
            builder.ToTable("NoteAcctgPols", "public");

            builder.HasOne(d => d.ReportingPeriod)
                .WithMany(p => p!.NoteAccountingPolicies)
                .HasForeignKey(d => new { d.ClientId, d.AccountPeriodId })
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("noteprofitandloss_fk");

            base.Configure(builder);
        }
    }
}
