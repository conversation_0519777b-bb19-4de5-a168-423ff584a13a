﻿using Amazon.RDS.Util;
using Microsoft.Extensions.Configuration;
using Npgsql;
using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence
{
    /// <summary>
    /// Database connection string factory
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class DbConnectionStringFactory
    {
        /// <summary>
        /// Generates a database connection string
        /// </summary>
        /// <param name="configuration">Service configuration</param>
        /// <returns>The connection string</returns>
        public static string ConnectionString(IConfiguration configuration)
        {
            var connectionString = configuration["DB_CONNECTION"];

            if (string.IsNullOrEmpty(connectionString))
            {
                var host = configuration["ACCOUNTS_PRODUCTION_REPORTING_DB_HOST"];
                var port = int.Parse(configuration["ACCOUNTS_PRODUCTION_REPORTING_DB_PORT"]);
                var username = configuration["ACCOUNTS_PRODUCTION_REPORTING_DB_USERNAME"];
                var database = configuration["ACCOUNTS_PRODUCTION_REPORTING_DB_DATABASE_NAME"];

                return $"Server={host};Port={port};User Id={username};Database={database};SSL Mode=Require;Trust Server Certificate=true;Pooling=true;";
            }

            return connectionString;
        }

        /// <summary>
        /// Generate a password provider callback function that generates an RDS IAM authentication token
        /// </summary>
        /// <param name="configuration">Service configuration</param>
        /// <returns>A password provider callback function</returns>
        public static ProvidePasswordCallback PasswordCallback(IConfiguration configuration)
        {
            return (host, port, database, username) =>
            {
                return RDSAuthTokenGenerator.GenerateAuthToken(host, port, username);
            };
        }
    }
}
