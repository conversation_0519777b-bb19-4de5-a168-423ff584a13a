﻿using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Services
{
    [ExcludeFromCodeCoverage]
    public class ConfigurationSettings : IConfigurationSettings
    {
        public string GetConfigurationFor(string key)
        {
            var value = Environment.GetEnvironmentVariable(key);

            return value ?? throw new InvalidOperationException($"Environment variable '{key}' is not set.");
        }

    }
}
