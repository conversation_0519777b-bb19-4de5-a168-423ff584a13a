﻿using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Infrastructure.Services
{
    public class DomainEventService : IDomainEventService
    {
        private readonly IMediator _mediator;
        private readonly ILogger<DomainEventService> _logger;

        public DomainEventService(IMediator mediator,
            ILogger<DomainEventService> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        public async Task Publish(IDomainEventNotification domainEventNotification, CancellationToken cancellationToken)
        {
            _logger.LogInformation($"Publishing message of type: {domainEventNotification.EventType}");
            await _mediator.Publish(domainEventNotification, cancellationToken);
        }
    }
}
