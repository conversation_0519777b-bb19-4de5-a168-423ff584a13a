﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AccountsProduction.AccountsBuilder.Infrastructure.Services
{
    public class AccountsProductionService : IAccountsProductionService
    {
        private readonly HttpClient _httpClient;
        private readonly UserContext _userContext;
        private readonly ILogger<AccountsProductionService> _logger;
        private readonly IEnvVariableProvider _variableProvider;

        public AccountsProductionService(IEnvVariableProvider variableProvider,
            HttpClient httpClient,
            UserContext userContext,
            ILogger<AccountsProductionService> logger)
        {
            _userContext = userContext;
            _logger = logger;
            _variableProvider = variableProvider;
            _httpClient = httpClient;
            _httpClient.SetupIrisClient(_variableProvider.AccountsProductionApiKey, _variableProvider.AccountsProductionApiId, _variableProvider.AccountsProductionApiScheme, _variableProvider.AccountsProductionApiHost);

        }

        public async Task<IEnumerable<InvolvementDto>> GetInvolvements(Guid clientId, bool excludeDeleted = false)
        {
            var involvementUrl = string.Concat(_httpClient.BaseAddress!.ToString(), $"clients/{clientId}/involvements/awsiam");

            var request = _httpClient.CreateGetSignedRequest(involvementUrl, _variableProvider);
            request.AddRequestHeaders(_userContext);

            _logger.LogInformation("Involvement request");
            var response = await _httpClient.SendAsync(request);
            _logger.LogInformation("Involvement response received");

            response.EnsureSuccessStatusCode();
            await using var responseStream = await response.Content.ReadAsStreamAsync();

            var responseDto = await JsonSerializer.DeserializeAsync<List<InvolvementDto>>(responseStream,
                new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return responseDto!;
        }

    }
}
