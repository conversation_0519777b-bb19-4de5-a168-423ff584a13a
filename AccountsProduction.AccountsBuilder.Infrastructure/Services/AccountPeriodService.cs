﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.RoundingOptions;
using AccountsProduction.AccountsBuilder.Application.Common.Helper;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using Flurl;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AccountsProduction.AccountsBuilder.Infrastructure.Services
{
    public class AccountPeriodService : IAccountPeriodService
    {
        private readonly HttpClient _httpClient;
        private readonly IEnvVariableProvider _envVariableProvider;
        private readonly UserContext _userContext;
        private readonly ILogger<AccountPeriodService> _logger;


        public AccountPeriodService(IEnvVariableProvider envVariableProvider,
            HttpClient httpClient,
            UserContext userContext,
            ILogger<AccountPeriodService> logger)
        {
            _envVariableProvider = envVariableProvider;
            _httpClient = httpClient;
            _userContext = userContext;
            _logger = logger;
            _httpClient.SetupIrisClient(_envVariableProvider.AccountPeriodApiKey, _envVariableProvider.AccountPeriodApiId, _envVariableProvider.AccountPeriodApiScheme, _envVariableProvider.AccountPeriodApiHost);

        }

        public async Task<ClientResponse> GetClientAsync(Guid clientId)
        {
            var fullUrl = GetFullUrl(string.Format(Constants.AccountPeriodsApi.ClientEndpoint, clientId));

            var httpRequestMessage = _httpClient.CreateGetSignedRequest(fullUrl, _envVariableProvider);
            httpRequestMessage.AddRequestHeaders(_userContext);


            var response = await _httpClient.SendAsync(httpRequestMessage);

            response.EnsureSuccessStatusCode();

            var contentString = await response.Content.ReadAsStringAsync();

            var clientResponse = JsonSerializer.Deserialize<ClientResponse>(contentString, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return clientResponse;
        }

        public async Task<EntitySetupDto> GetEntitySetupAsync(Guid clientId, Guid accountPeriodId)
        {
            var entitySetupFullUrl = GetFullUrl(string.Format(Constants.AccountPeriodsApi.EntitySetupEndpoint, clientId, accountPeriodId));

            var httpRequestMessage = _httpClient.CreateGetSignedRequest(entitySetupFullUrl, _envVariableProvider);
            httpRequestMessage.AddRequestHeaders(_userContext);


            var response = await _httpClient.SendAsync(httpRequestMessage);

            response.EnsureSuccessStatusCode();

            var contentString = await response.Content.ReadAsStringAsync();

            _logger.LogInformation("EntitySetup response received");

            var entitySetupDto = JsonSerializer.Deserialize<EntitySetupDto>(contentString, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return entitySetupDto;
        }

        public async Task<bool> IsClientOnboarded(Guid clientId)
        {
            var clientOnboardedFullUrl = GetFullUrl(string.Format(Constants.AccountPeriodsApi.ClientOnboardedEndpoint, clientId));

            var httpRequestMessage = _httpClient.CreateGetSignedRequest(clientOnboardedFullUrl, _envVariableProvider);
            httpRequestMessage.AddRequestHeaders(_userContext);


            var response = await _httpClient.SendAsync(httpRequestMessage);

            response.EnsureSuccessStatusCode();

            var contentString = await response.Content.ReadAsStringAsync();

            _logger.LogInformation("ClientOnboarded response received");

            var clientOnboardedDto = JsonSerializer.Deserialize<ClientOnboardedDto>(contentString, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return clientOnboardedDto.IsOnboarded;
        }

        public async Task<RoundingOptionsResponse> GetRoundingOptionsAsync(Guid clientId, Guid accountPeriodId)
        {
            var roundingOptionsFullUrl = GetFullUrl(string.Format(Constants.AccountPeriodsApi.RoundingOptionsEndpoint, clientId, accountPeriodId)) + "?defaultifnotexists=true&includepreviousperiods=true";

            var httpRequestMessage = _httpClient.CreateGetSignedRequest(roundingOptionsFullUrl, _envVariableProvider);
            httpRequestMessage.AddRequestHeaders(_userContext);

            var response = await _httpClient.SendAsync(httpRequestMessage);

            response.EnsureSuccessStatusCode();

            var contentString = await response.Content.ReadAsStringAsync();

            _logger.LogInformation("RoundingOptions response received");

            var roundingOptionsDto = JsonSerializer.Deserialize<RoundingOptionsResponse>(contentString, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return roundingOptionsDto;
        }

        private string GetFullUrl(string path)
        {
            return _httpClient.BaseAddress!.ToString().AppendPathSegment(path).ToString();
        }
    }
}