﻿using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.Infrastructure
{
    [ExcludeFromCodeCoverage]
    public class EnvVariableProvider : IEnvVariableProvider
    {
        public string AccountsProductionAccountPeriodRequestTopic => Environment.GetEnvironmentVariable("ACCOUNTSPRODUCTION_ACCOUNTPERIOD_REQUEST_TOPIC");

        public string AccountsProductionAccountsBuilderInputTopic => Environment.GetEnvironmentVariable("ACCOUNTSPRODUCTION_ACCOUNTSBUILDER_INPUT_TOPIC");

        public string ReportingTrialBalanceRequestTopic => Environment.GetEnvironmentVariable("REPORTING_TRIALBALANCE_REQUEST_TOPIC");

        public string AccountsProductionInvolvementsQueue => Environment.GetEnvironmentVariable("ACCOUNTS_PRODUCTION_INVOLVEMENTS_QUEUE");

        public string ClientApiKey => Environment.GetEnvironmentVariable("CLIENT_API_KEY");
        public string ClientApiId => Environment.GetEnvironmentVariable("CLIENT_API_ID");

        public string AwsRegion => Environment.GetEnvironmentVariable("AWS_REGION");

        public string AwsDefaultRegion => Environment.GetEnvironmentVariable("AWS_DEFAULT_REGION");

        public string AwsAccessKey => Environment.GetEnvironmentVariable("AWS_ACCESS_KEY_ID");

        public string AwsSecretKey => Environment.GetEnvironmentVariable("AWS_SECRET_ACCESS_KEY");

        public string AwsSessionToken => Environment.GetEnvironmentVariable("AWS_SESSION_TOKEN");

        public string AwsPrivateAccessKey => Environment.GetEnvironmentVariable("AWS_PRIVATE_ACCESS_KEY_ID");

        public string AwsPrivateSecretKey => Environment.GetEnvironmentVariable("AWS_PRIVATE_SECRET_ACCESS_KEY");

        public string AwsPrivateSessionToken => Environment.GetEnvironmentVariable("AWS_PRIVATE_SESSION_TOKEN");

        public string ClientApiScheme => Environment.GetEnvironmentVariable("CLIENT_API_SCHEME");

        public string ClientApiHost => Environment.GetEnvironmentVariable("CLIENT_API_HOST");

        public string ClientAddressApiUrl => Environment.GetEnvironmentVariable("CLIENT_ADDRESS_API_URL");

        public string ClientApiUrl => Environment.GetEnvironmentVariable("CLIENT_API_URL");

        public string ClientInvolvementApiUrl => Environment.GetEnvironmentVariable("CLIENT_INVOLVEMENT_API_URL");

        public string AccountsBuilderTimeoutQueueUrl => Environment.GetEnvironmentVariable("ACCOUNTSBUILDER_TIMEOUT_QUEUE_URL");

        public string TimeoutDelaySeconds => Environment.GetEnvironmentVariable("ACCOUNTSBUILDER_TIMEOUT_DELAY_SECONDS");

        public string SubscriptionHubScheme => Environment.GetEnvironmentVariable("SUBSCRIPTION_HUB_API_SCHEME");

        public string SubscriptionHubHost => Environment.GetEnvironmentVariable("SUBSCRIPTION_HUB_API_HOST");

        public string SubscriptionHubApiKey => Environment.GetEnvironmentVariable("SUBSCRIPTION_HUB_API_KEY");

        public string SubscriptionHubApiId => Environment.GetEnvironmentVariable("SUBSCRIPTION_HUB_API_ID");

        public string Source => Environment.GetEnvironmentVariable("AWS_LAMBDA_FUNCTION_NAME");

        public string AccountPeriodApiScheme => Environment.GetEnvironmentVariable("ACCOUNT_PERIOD_API_SCHEME");

        public string AccountPeriodApiHost => Environment.GetEnvironmentVariable("ACCOUNT_PERIOD_API_HOST");

        public string AccountPeriodApiKey => Environment.GetEnvironmentVariable("ACCOUNT_PERIOD_API_KEY");

        public string AccountPeriodApiId => Environment.GetEnvironmentVariable("ACCOUNT_PERIOD_API_ID");

        public string TrialBalanceApiScheme => Environment.GetEnvironmentVariable("TRIAL_BALANCE_API_SCHEME");

        public string TrialBalanceApiHost => Environment.GetEnvironmentVariable("TRIAL_BALANCE_API_HOST");

        public string TrialBalanceApiKey => Environment.GetEnvironmentVariable("TRIAL_BALANCE_API_KEY");

        public string TrialBalanceApiId => Environment.GetEnvironmentVariable("TRIAL_BALANCE_API_ID");

        public string AccountsProductionApiScheme => Environment.GetEnvironmentVariable("ACCOUNTSPRODUCTION_API_SCHEME")!;
        public string AccountsProductionApiHost => Environment.GetEnvironmentVariable("ACCOUNTSPRODUCTION_API_HOST")!;
        public string AccountsProductionApiId => Environment.GetEnvironmentVariable("ACCOUNTSPRODUCTION_API_ID")!;
        public string AccountsProductionApiKey => Environment.GetEnvironmentVariable("ACCOUNTSPRODUCTION_API_KEY")!;

        public string AccountsBuilderApiScheme => Environment.GetEnvironmentVariable("ACCOUNTSBUILDER_API_SCHEME")!;
        public string AccountsBuilderApiHost => Environment.GetEnvironmentVariable("ACCOUNTSBUILDER_API_HOST")!;
        public string AccountsBuilderApiId => Environment.GetEnvironmentVariable("ACCOUNTSBUILDER_API_ID")!;
        public string AccountsBuilderApiKey => Environment.GetEnvironmentVariable("ACCOUNTSBUILDER_API_KEY")!;

        public bool SendDplInboundViaEventbus => bool.Parse(Environment.GetEnvironmentVariable("SEND_DPL_INBOUND_VIA_EVENTBUS") ?? bool.FalseString);

        public bool DisableDplMessageForCharities => bool.Parse(Environment.GetEnvironmentVariable("DISABLE_DPL_MESSAGE_FOR_CHARITIES_TEMP") ?? bool.FalseString);
    }
}
