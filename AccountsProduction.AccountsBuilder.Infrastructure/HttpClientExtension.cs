﻿using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using System.Net.Http.Headers;

namespace AccountsProduction.AccountsBuilder.Infrastructure
{
    public static class HttpClientExtension
    {
        private static readonly TimeSpan HttpClientTimeOut = new TimeSpan(0, 0, 15);

        public static HttpClient SetupIrisClient(this HttpClient client, string apiKey, string apiId, string apiScheme, string apiHost)
        {
            client.Timeout = HttpClientTimeOut;
            client.DefaultRequestHeaders.Add("x-api-key", apiKey);
            client.DefaultRequestHeaders.Add("x-apigw-api-id", apiId);
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            client.BaseAddress = BuildDependencyServiceUrl(apiScheme, apiHost);
            return client;
        }

        public static HttpRequestMessage CreateGetSignedRequest(this HttpClient httpClient, string uri, IEnvVariableProvider envVariableProvider)
        {
            return AwsHelper.AwsSigningHelper.GenerateAwsSignedGetRequest(uri,
                             envVariableProvider.AwsRegion,
                             envVariableProvider.AwsAccessKey,
                             envVariableProvider.AwsSecretKey,
                             envVariableProvider.AwsSessionToken
                             );
        }

        public static HttpRequestMessage CreatePostSignedRequest(this HttpClient httpClient, HttpContent content, string uri, IEnvVariableProvider envVariableProvider)
        {
            return AwsHelper.AwsSigningHelper.GenerateAwsSignedPostRequest(uri,
                             content,
                             envVariableProvider.AwsRegion,
                             envVariableProvider.AwsAccessKey,
                             envVariableProvider.AwsSecretKey,
                             envVariableProvider.AwsSessionToken
                             );
        }

        public static void AddRequestHeaders(this HttpRequestMessage request, UserContext userContext)
        {
            request.Headers.Add("PlatformTenantId", userContext.TenantId);
            request.Headers.Add("CorrelationId", userContext.CorrelationId);
            request.Headers.Add("PlatformUser", !string.IsNullOrEmpty(userContext.UserId) ? userContext.UserId : userContext.TenantId);
        }

        private static Uri BuildDependencyServiceUrl(string scheme, string host) => new Uri($"{scheme}://{host}/");
    }
}
