﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Dpl;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.RoundingOptions;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Application.Common.Helper;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Application.IntegrationEvents;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Iris.Platform.Eventbus.Client.Dotnet.Client;
using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.StreamContent;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;

namespace AccountsProduction.AccountsBuilder.Infrastructure.IntegrationEventHandler;
public class TrialBalanceChangeNotificationEventHandler(
    ILogger<TrialBalanceChangeNotificationEventHandler> logger, I<PERSON>apper mapper, UserContext userContext, ISnsServiceClient _snsServiceClient, IEventBusClient eventBusClient, IEnvVariableProvider envVariableProvider, IAccountPeriodService accountPeriodService) : INotificationHandler<NotificationTrialBalanceChanged>
{
    private const string SyncInboundCalculationDplTopic = "dataprocessinglayer:sync-inbound-calculation";
    protected readonly ILogger<TrialBalanceChangeNotificationEventHandler> _logger = logger;
    protected readonly IMapper _mapper = mapper;
    protected readonly UserContext _userContext = userContext;
    protected readonly IEnvVariableProvider _envVariableProvider = envVariableProvider;
    protected readonly IAccountPeriodService _accountPeriodService = accountPeriodService;

    public async Task Handle(NotificationTrialBalanceChanged notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("START event type {eventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}.", EventTypes.DataProcessingLayerStartedEvent, notification.ClientId, notification.PeriodId, DateTime.UtcNow);

        try
        {
            var roundingOptionsData = await _accountPeriodService.GetRoundingOptionsAsync(notification.ClientId, notification.PeriodId);
            var roundingOptionsDplData = await AlignRoundingOptionsWithTrialBalanceYears(roundingOptionsData, notification.Data.TrialBalances);

            var requestMessage = new DplRequestMessage
            {
                Data = [.. notification.Data.TrialBalances.Select(x => _mapper.Map<DplRequestDataDto>(x, opts => { opts.Items[nameof(TrialBalanceDto.SectorsInformation)] = notification.Data.SectorsInformation; }))],
                RoundingOptions = roundingOptionsDplData,
                ClientId = notification.ClientId,
                AccountChartIdentifier = notification.Data.AccountsChartIdentifier,
                GroupStructureIdentifier = notification.Data.GroupStructureCode,
                SectorsInformation = notification.Data.SectorsInformation
            };

            var reportingTrialBalanceEventType = DplMappingHelper.ReportingTrialBalanceEventType(notification.ReportingStandardType);

            var tenantId = Guid.Parse(_userContext.TenantId);
            Guid? correlationId = !string.IsNullOrEmpty(_userContext.CorrelationId) ? Guid.Parse(_userContext.CorrelationId) : null;

            var dplMessageMetadata = new DplMessageMeta
            {
                TenantId = tenantId,
                ClientId = notification.ClientId,
                PeriodId = notification.PeriodId,
                ProcessId = notification.ProcessId,
                CorrelationId = correlationId,
                CreatedAtUtc = DateTime.UtcNow,
                CreatedByUserId = _userContext.UserId,
                Type = reportingTrialBalanceEventType
            };

            if (IsCharity(notification.Data.AccountsChartIdentifier) && _envVariableProvider.DisableDplMessageForCharities)
            {
                throw new InvalidOperationException($"DPL message for charities is disabled. ClientId: {notification.ClientId}, PeriodId: {notification.PeriodId}, ProcessId: {notification.ProcessId}");
            }

            if (_envVariableProvider.SendDplInboundViaEventbus)
            {
                var elementsMessage = new DplEventbusMessage
                {
                    Message = new EventbusStreamContent()
                    {
                        Name = $"tb-data-{notification.ProcessId}.json",
                        Stream = new MemoryStream(Encoding.UTF8.GetBytes(JsonSerializer.Serialize(requestMessage)))
                    },
                    Meta = dplMessageMetadata
                };

                await eventBusClient.Publish(SyncInboundCalculationDplTopic, elementsMessage);
            }
            else
            {
                var elementsMessage = new DplMessage
                {
                    Message = requestMessage,
                    Meta = dplMessageMetadata
                };

                var topicArn = _envVariableProvider.ReportingTrialBalanceRequestTopic;

                var message = JsonSerializer.Serialize(elementsMessage);
                var messageAttributes = new Dictionary<string, string>()
                {
                    {"ClientId", notification.ClientId.ToString()},
                    {"PeriodId", notification.PeriodId.ToString()},
                    {"ProcessId", notification.ProcessId.ToString()}
                };

                await _snsServiceClient.PublishMessage(topicArn, message, reportingTrialBalanceEventType, cancellationToken, messageAttributes);
            }

            _logger.LogInformation("Sent publish request to DataProcessingLayer with processId {processId}, clientId {clientId} and periodId {periodId}", notification.ProcessId, notification.ClientId, notification.PeriodId);

        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to send request to data processing layer for processId {processId}, clientId {clientId} and periodId {periodId}", notification.ProcessId, notification.ClientId, notification.PeriodId);
            throw;
        }

        _logger.LogInformation("FINISHED event type {eventType} for processId {processId}, clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}.", EventTypes.DataProcessingLayerStartedEvent, notification.ProcessId, notification.ClientId, notification.PeriodId, DateTime.UtcNow);
    }

    private static bool IsCharity(string accountsChartIdentifier) => accountsChartIdentifier switch
    {
        "ICHA" or "UCHA" => true,
        _ => false,
    };

    private static Task<List<DplRequestDataRoundingOptionsDto>> AlignRoundingOptionsWithTrialBalanceYears(RoundingOptionsResponse roundingOptions, List<PeriodTrialBalanceDto> trialBalances)
    {
        var periodIdsAndYears = trialBalances.Select(x => (x.PeriodId, x.Year)).Distinct().ToList();
        var flattendRoundingOptions = RoundingOptionsResponse.Flatten(roundingOptions)
            .Where(ro => periodIdsAndYears.Exists(py => py.PeriodId == ro.AccountPeriodId)).ToList();

        var result = flattendRoundingOptions.Select(x => new DplRequestDataRoundingOptionsDto
        {
            Year = periodIdsAndYears.Find(y => y.PeriodId == x.AccountPeriodId).Year,
            ProfitLossRoundingAccount = x.ProfitLossRoundingAccount,
            ProfitLossRoundingSubAccount = x.ProfitLossRoundingSubAccount,
            ProfitLossRoundingAccountDescription = x.ProfitLossRoundingAccountDescription,
            BalanceSheetRoundingAccount = x.BalanceSheetRoundingAccount,
            BalanceSheetRoundingSubAccount = x.BalanceSheetRoundingSubAccount,
            BalanceSheetRoundingAccountDescription = x.BalanceSheetRoundingAccountDescription,
            UseAdvancedRounding = x.UseAdvancedRounding
        }).ToList();
        return Task.FromResult(result);
    }
}