# accountsproduction accountsbuilder func

[![Quality Gate Status](https://sonarcloud.io/api/project_badges/measure?project=IrisWebServices_AccountsProduction_Builds_AccountsproductionAccountsbuilderFunc&metric=alert_status&token=e52144d7cbe72b279bef0981921189efae75ac3d)](https://sonarcloud.io/dashboard?id=IrisWebServices_AccountsProduction_Builds_AccountsproductionAccountsbuilderFunc)
[![Lines of Code](https://sonarcloud.io/api/project_badges/measure?project=IrisWebServices_AccountsProduction_Builds_AccountsproductionAccountsbuilderFunc&metric=ncloc&token=e52144d7cbe72b279bef0981921189efae75ac3d)](https://sonarcloud.io/dashboard?id=IrisWebServices_AccountsProduction_Builds_AccountsproductionAccountsbuilderFunc)
[![Coverage](https://sonarcloud.io/api/project_badges/measure?project=IrisWebServices_AccountsProduction_Builds_AccountsproductionAccountsbuilderFunc&metric=coverage&token=e52144d7cbe72b279bef0981921189efae75ac3d)](https://sonarcloud.io/dashboard?id=IrisWebServices_AccountsProduction_Builds_AccountsproductionAccountsbuilderFunc)
[![Security Rating](https://sonarcloud.io/api/project_badges/measure?project=IrisWebServices_AccountsProduction_Builds_AccountsproductionAccountsbuilderFunc&metric=security_rating&token=e52144d7cbe72b279bef0981921189efae75ac3d)](https://sonarcloud.io/dashboard?id=IrisWebServices_AccountsProduction_Builds_AccountsproductionAccountsbuilderFunc)

Accounts Production accounts builder function

## Author

<EMAIL>

### Next steps

Please complete the below sections.Think about adding testing details etc.

## Environment variables

Describe the environment variables required.

## API Gateway Configuration

Describe the API Gateway configuration if required

## Service Dependencies

List the service dependencies

