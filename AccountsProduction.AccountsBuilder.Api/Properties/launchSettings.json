{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:13022/", "sslPort": 44317}}, "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "AccountsProduction.AccountsBuilder.Api": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "Logging__LogLevel__Default": "Debug", "ACCOUNTSPRODUCTION_ACCOUNTPERIOD_REQUEST_TOPIC": "ACCOUNTSPRODUCTION_ACCOUNTPERIOD_REQUEST_TOPIC", "AWS__Region": "eu-west-2", "AWS_DEFAULT_REGION": "eu-west-2", "AWS_REGION": "eu-west-2", "AWS_ACCOUNT_ID": "AWS_ACCOUNT_ID", "AWS_ACCESS_KEY_ID": "AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY": "AWS_SECRET_ACCESS_KEY", "AWS_SESSION_TOKEN": "AWS_SESSION_TOKEN", "ACCOUNTSPRODUCTION_ACCOUNTSBUILDER_INPUT_TOPIC": "ACCOUNTSPRODUCTION_ACCOUNTSBUILDER_INPUT_TOPIC", "ACCOUNTS_PRODUCTION_DB_HOST": "ACCOUNTS_PRODUCTION_DB_HOST", "ACCOUNTS_PRODUCTION_DB_PORT": "ACCOUNTS_PRODUCTION_DB_PORT", "ACCOUNTS_PRODUCTION_DB_USERNAME": "ACCOUNTS_PRODUCTION_DB_USERNAME", "ACCOUNTS_PRODUCTION_DB_DATABASE_NAME": "ACCOUNTS_PRODUCTION_DB_DATABASE_NAME", "ACCOUNTS_PRODUCTION_DB_PASSWORD": "ACCOUNTS_PRODUCTION_DB_PASSWORD", "AWS_SVC_ROLE_ARN": "AWS_SVC_ROLE_ARN", "AWS_Profile": "Development", "AWS__Profile": "Development", "ACCOUNT_PERIOD_API_SCHEME": "ACCOUNT_PERIOD_API_SCHEME", "ACCOUNT_PERIOD_API_HOST": "ACCOUNT_PERIOD_API_HOST", "ACCOUNT_PERIOD_API_KEY": "ACCOUNT_PERIOD_API_KEY", "ACCOUNT_PERIOD_API_ID": "ACCOUNT_PERIOD_API_ID", "TRIAL_BALANCE_API_SCHEME": "TRIAL_BALANCE_API_SCHEME", "TRIAL_BALANCE_API_HOST": "TRIAL_BALANCE_API_HOST", "TRIAL_BALANCE_API_KEY": "TRIAL_BALANCE_API_KEY", "TRIAL_BALANCE_API_ID": "TRIAL_BALANCE_API_ID", "ACCOUNTSBUILDER_API_SCHEME": "ACCOUNTSBUILDER_API_SCHEME", "ACCOUNTSBUILDER_API_HOST": "ACCOUNTSBUILDER_API_HOST", "ACCOUNTSBUILDER_API_KEY": "ACCOUNTSBUILDER_API_KEY", "ACCOUNTSBUILDER_API_ID": "ACCOUNTSBUILDER_API_ID", "ACCOUNTSPRODUCTION_API_SCHEME": "ACCOUNTSPRODUCTION_API_SCHEME", "ACCOUNTSPRODUCTION_API_HOST": "ACCOUNTSPRODUCTION_API_HOST", "ACCOUNTSPRODUCTION_API_KEY": "ACCOUNTSPRODUCTION_API_KEY", "ACCOUNTSPRODUCTION_API_ID": "ACCOUNTSPRODUCTION_API_ID", "CLIENT_API_SCHEME": "CLIENT_API_SCHEME", "CLIENT_API_HOST": "CLIENT_API_HOST", "CLIENT_API_KEY": "CLIENT_API_KEY", "CLIENT_API_ID": "CLIENT_API_ID", "CLIENT_ADDRESS_API_URL": "CLIENT_ADDRESS_API_URL", "CLIENT_API_URL": "CLIENT_API_URL", "EVENTBUS_AWS_ACCOUNT_ID": "EVENTBUS_AWS_ACCOUNT_ID", "EVENT_BUS_MAIN_QUEUE_URL": "EVENT_BUS_MAIN_QUEUE_URL", "REPORTING_TRIALBALANCE_REQUEST_TOPIC": "REPORTING_TRIALBALANCE_REQUEST_TOPIC"}, "applicationUrl": "https://localhost:5001;http://localhost:5000"}, "Mock Lambda Test Tool": {"commandName": "Executable", "executablePath": "%USERPROFILE%\\.dotnet\\tools\\dotnet-lambda-test-tool-8.0.exe", "commandLineArgs": "--port 5050", "workingDirectory": ".\\bin\\$(Configuration)\\net8.0"}}}