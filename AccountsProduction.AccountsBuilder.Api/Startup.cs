﻿using AccountsProduction.AccountsBuilder.Api.Filters;
using AccountsProduction.AccountsBuilder.Api.Middleware;
using AccountsProduction.AccountsBuilder.Application;
using AccountsProduction.AccountsBuilder.Infrastructure;
using AccountsProduction.AccountsBuilder.Reporting.Application;
using AccountsProduction.AccountsBuilder.Reporting.Infrastructure;
using FluentValidation.AspNetCore;
using Iris.AccountsProduction.Common.Toolkit.ExtensionMethods;
using Iris.AccountsProduction.Common.Toolkit.Filters;
using Iris.Elements.Http.Api.HealthChecks.Extensions;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Serilog;
using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.Api
{
    [ExcludeFromCodeCoverage]
    public class Startup
    {
        private const string FunctionName = "accountsbuilder-func";

        public IConfiguration Configuration { get; }

        public Startup(IWebHostEnvironment env)
        {
            var builder = new ConfigurationBuilder().SetBasePath(env.ContentRootPath).AddJsonFile("appsettings.json", true, true).AddEnvironmentVariables();

            Configuration = builder.Build();
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers(options =>
            {
                options.EnableEndpointRouting = false;
                options.Filters.Add<ModelStateActionFilter>(int.MinValue);
                options.Filters.Add<ExceptionFilter>(int.MinValue);
                options.Filters.Add<ResponseMessageFilter>(int.MinValue);
            }).AddNewtonsoftJson(options =>
            {
                options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
                options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;
                options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
            });

            services.AddMvc();
            services.AddFluentValidationAutoValidation().AddFluentValidationClientsideAdapters();

            services.AddHttpContextAccessor();

            services.AddLogging(loggingBuilder => loggingBuilder.AddConfiguration(Configuration.GetSection("Logging")));
            services.AddApplication();
            services.AddReportingApplication();

            services.AddInfrastructure(Configuration, FunctionName);
            services.AddReportingInfrastructure(Configuration);

            services.AddScoped<UserContext>();

            services.AddFeatureServiceClient();

            services.Configure<RouteOptions>(options => { options.LowercaseUrls = true; });

            services.AddSwaggerGen(options => { options.SwaggerDoc("v1", new OpenApiInfo { Title = "AccountsProduction AccountsBuilder API", Version = "v1" }); });

            services.AddHealthChecks();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory loggerFactory)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();

                app.UseSwagger();

                app.UseSwaggerUI(c => { c.SwaggerEndpoint("/swagger/v1/swagger.json", "AccountsProduction AccountsBuilder API v1"); });
            }
            else
            {
                app.UseHsts();
            }

            app.UseSerilogRequestLogging();

            app.UseMiddleware<ResponseHeadersMiddleware>();
            app.UseMiddleware<EnableRequestBufferingMiddleware>();
            app.UseMiddleware<UserContextMiddleware>();

            app.UseHttpsRedirection();

            app.UseRouting();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapGet("/", async context => { await context.Response.WriteAsync("Welcome!"); });
                endpoints.MapHealthChecks("healthcheck", new HealthCheckOptions
                {
                    ResponseWriter = HealthCheckExtensions.WriteResponse
                });
            });

            app.UseStatusCodePages();
        }
    }
}