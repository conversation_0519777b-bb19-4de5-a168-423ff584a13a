﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder;
using AccountsProduction.AccountsBuilder.Application.Queries.AccountsBuilder;
using Iris.AccountsProduction.Common.Toolkit.Filters;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using static Iris.AccountsProduction.Common.Toolkit.Utils.Constants;

namespace AccountsProduction.AccountsBuilder.Api.Controllers
{
    [Route("clients/{clientId}/[controller]")]
    [ApiController]
    [Feature(FeatureToggle.AccountsProduction.Services)]
    [APLicensed]
	[ConfidentialClientAccess (ClientUuidParamName = "clientId")]
	public class AccountsBuilderController : ControllerBase
    {
        private readonly IMediator _mediator;

        public AccountsBuilderController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// Get accounts builder resource for a client and a period
        /// </summary>
        /// <param name="clientId">Client uuid</param>
        /// <param name="periodId">Period uuid</param>
        /// <returns></returns>
        [HttpGet("{periodId}")]
        [HttpGet("{periodId}/awsiam")]
        [ProducesResponseType(typeof(AccountsBuilderFullDto), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetFullReportByPeriodId([FromRoute] Guid clientId, [FromRoute] Guid periodId)
        {
            var response = await _mediator.Send(new GetFullReportQuery
            {
                ClientId = clientId,
                PeriodId = periodId
            });

            if (response == null)
            {
                return NotFound();
            }

            return Ok(response);
        }
    }
}
