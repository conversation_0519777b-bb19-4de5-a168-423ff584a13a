﻿<Project Sdk="Microsoft.NET.Sdk.Web">
	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<RootNamespace>AccountsProduction.AccountsBuilder.Api</RootNamespace>
		<AssemblyName>AccountsProduction.AccountsBuilder.Api</AssemblyName>
		<GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
		<AWSProjectType>Lambda</AWSProjectType>
		<SonarQubeTestProject>false</SonarQubeTestProject>
		<PublishReadyToRun>true</PublishReadyToRun>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Amazon.Lambda.AspNetCoreServer" Version="9.0.3" />
		<PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
		<PackageReference Include="Iris.Elements.Http.Api.HealthChecks" Version="1.0.0.5" />
		<PackageReference Include="Iris.Elements.Logging.Serilog.AspNet" Version="2.0.0.23" />
		<PackageReference Include="Iris.Elements.Http.Api.Models" Version="1.0.0.28" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.11" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="7.2.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="7.2.0" />
		<PackageReference Include="System.Security.Cryptography.Xml" Version="9.0.1" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\AccountsProduction.AccountsBuilder.Application\AccountsProduction.AccountsBuilder.Application.csproj" />
		<ProjectReference Include="..\AccountsProduction.AccountsBuilder.Infrastructure\AccountsProduction.AccountsBuilder.Infrastructure.csproj" />
	</ItemGroup>
	<ItemGroup>
		<SonarQubeSetting Include="sonar.coverage.exclusions">
			<Value>**/Startup.cs</Value>
		</SonarQubeSetting>
	</ItemGroup>
</Project>
