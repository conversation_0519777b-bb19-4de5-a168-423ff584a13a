﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <SonarQubeTestProject>false</SonarQubeTestProject>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Aggregate\EventHandlers\ReportDataEvents\AggregatePracticeDetailsRequestEventStrategy.cs" />
    <Compile Remove="Aggregate\EventHandlers\ReportDataEvents\InvolvementsResponseEventStrategy.cs" />
    <Compile Remove="Aggregate\EventHandlers\ReportDataEvents\NonFinancialDataRequestEventStrategy.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
    <PackageReference Include="Iris.AccountsProduction.AccountsBuilder.Messages" Version="1.0.0.305" />
    <PackageReference Include="Iris.AccountsProduction.Common.Toolkit" Version="2.0.0.259" />
    <PackageReference Include="Iris.Elements.Messaging" Version="1.0.0.33" />
    <PackageReference Include="Iris.Platform.Eventbus.Client.Dotnet" Version="5.0.0.475" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AccountsProduction.AccountsBuilder.Application.Common\AccountsProduction.AccountsBuilder.Application.Common.csproj" />
    <ProjectReference Include="..\AccountsProduction.AccountsBuilder.Domain\AccountsProduction.AccountsBuilder.Domain.csproj" />
    <ProjectReference Include="..\AccountsProduction.AccountsBuilder.Infrastructure\AccountsProduction.AccountsBuilder.Infrastructure.csproj" />
  </ItemGroup>

</Project>
