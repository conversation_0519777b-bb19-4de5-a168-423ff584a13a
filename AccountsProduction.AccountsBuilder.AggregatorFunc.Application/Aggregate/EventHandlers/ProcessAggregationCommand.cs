﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers;
using AccountsProduction.AccountsBuilder.Application.Common;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands
{
    public class ProcessAggregationCommand : IRequest<Unit>
    {
        public string Message { get; set; } = null!;
        public string MessageType { get; set; } = null!;
        public Guid ClientId { get; set; }
        public Guid TenantId { get; set; }
        public Guid PeriodId { get; set; }
        public Guid ProcessId { get; set; }
    }

    public class ProcessAggregationCommandHandler : IRequestHandler<ProcessAggregationCommand, Unit>
    {
        private readonly ILogger<ProcessAggregationCommandHandler> _logger;
        private readonly IEnumerable<IAggregationStrategy> _aggregationStrategies;

        public ProcessAggregationCommandHandler(ILogger<ProcessAggregationCommandHandler> logger, IEnumerable<IAggregationStrategy> aggregationStrategies)
        {
            _logger = logger;
            _aggregationStrategies = aggregationStrategies;
        }

        public async Task<Unit> Handle(ProcessAggregationCommand request, CancellationToken cancellationToken)
        {
            await Retry.RunAndHandleConcurrency(async () => await ProcessRequest(request, cancellationToken), _logger);

            return Unit.Value;
        }

        private async Task ProcessRequest(ProcessAggregationCommand request, CancellationToken cancellationToken)
        {
            var aggregator = _aggregationStrategies.FirstOrDefault(x => x.IsMatch(request.MessageType));
            if (aggregator != null)
            {
                await aggregator.Process(request.Message, request.TenantId, request.ClientId, request.PeriodId, request.ProcessId, cancellationToken);
            }
            else
            {
                _logger.LogWarning($"Event with message type {request.MessageType} was not recognized!");
            }
        }
    }
}