﻿using AccountsProduction.AccountsBuilder.Application.Common.Helper;
using System.Text.Json;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers
{
    public abstract class BaseAggregationStrategy : IAggregationStrategy
    {
        public abstract string Name { get; }

        public virtual bool IsMatch(string messageType)
        {
            return Name == messageType;
        }

        public abstract Task Process(string requestMessage, Guid tenantId, Guid clientId, Guid periodId, Guid processId, CancellationToken cancellationToken);

        protected static T Deserialize<T>(string json)
        {
            var obj = JsonSerializer.Deserialize<T>(json, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            return obj;
        }

        public static T DeserializeWithObject<T>(string json)
        {
            var obj = JsonSerializer.Deserialize<T>(json, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Converters = { new ObjectToPrimitiveConverter() }
            });

            return obj;
        }
    }
}
