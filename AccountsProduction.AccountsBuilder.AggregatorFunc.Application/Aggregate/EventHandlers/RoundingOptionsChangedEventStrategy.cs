﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Application.IntegrationEvents;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers
{
    public sealed class RoundingOptionsChangedEventStrategy : BaseAggregationStrategy
    {
        public override string Name => EventTypes.RoundingOptionsChangedEvent;

        private readonly IAccountsBuilderRepository _repository;
        private readonly ILogger<RoundingOptionsChangedEventStrategy> _logger;
        private readonly IMapper _mapper;
        private readonly UserContext _userContext;
        private readonly IDomainEventService _domainEventService;

        public RoundingOptionsChangedEventStrategy(ILogger<RoundingOptionsChangedEventStrategy> logger, IMapper mapper,
            IAccountsBuilderRepository repository, UserContext userContext, IDomainEventService domainEventService)
        {
            _repository = repository;

            _logger = logger;
            _mapper = mapper;
            _userContext = userContext;
            _domainEventService = domainEventService;
        }

        public override async Task Process(string requestMessage, Guid tenantId, Guid clientId, Guid periodId, Guid processId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("START event type {eventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, DateTime.UtcNow);

            var accountsBuilder = await _repository.Get(clientId, periodId, cancellationToken);

            if (accountsBuilder == null || accountsBuilder.TenantId.ToString() != _userContext.TenantId)
            {
                _logger.LogWarning($"No accounts builder entity found for clientId {clientId} and periodId {periodId}.");
                return;
            }

            accountsBuilder.SetDirty();
            accountsBuilder.SetProcessId(processId);
            accountsBuilder.FinancialData.ResetRetry();

            await _repository.Save(accountsBuilder, cancellationToken);

            var trialBalances = _mapper.Map<TrialBalanceDto>(accountsBuilder.TrialBalance);

            if (trialBalances?.TrialBalances?.Any() == true)
            {
                var notificationTrialBalanceChanged = new NotificationTrialBalanceChanged(accountsBuilder.ClientId, accountsBuilder.PeriodId, accountsBuilder.ProcessId, accountsBuilder.EntitySetup.ReportingStandard, trialBalances);

                await _domainEventService.Publish(notificationTrialBalanceChanged, cancellationToken);
            }

            _logger.LogInformation("FINISHED event type {eventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, DateTime.UtcNow);
        }
    }
}