﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents
{
    public class DataProcessingLayerFinishedEventStrategy(IAccountsBuilderRepository _repository,
        ILogger<DataProcessingLayerFinishedEventStrategy> _logger, IMapper _mapper, IDomainEventService _domainEventService)
        : BaseAggregationStrategy, IDataProcessingLayerEventFinishedEventSharedBase
    {
        public override string Name => EventTypes.DataProcessingLayerFinishedEvent;

        public override async Task Process(string requestMessage, Guid tenantId, Guid clientId, Guid periodId, Guid processId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("START event type {eventType} for processId {processId}, clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}.", Name, processId, clientId, periodId, DateTime.UtcNow);

            var financialDataDto = Deserialize<FinancialDataDto>(requestMessage);

            var accountsBuilder = await _repository.Get(processId, cancellationToken);

            if (accountsBuilder is null)
            {
                _logger.LogWarning("No accounts builder entity found for processId {processId}.", processId);
                return;
            }

            if (financialDataDto.Status == FinancialDataStatusDto.Error && accountsBuilder.FinancialData.Retry < 3)
            {
                await (this as IDataProcessingLayerEventFinishedEventSharedBase).PublishDplRetry(accountsBuilder, _domainEventService, _repository, _mapper, cancellationToken);
            }
            else
            {
                await (this as IDataProcessingLayerEventFinishedEventSharedBase).StoreFinancialData(financialDataDto, accountsBuilder, _logger, _repository, _mapper, Name, processId, cancellationToken);
            }
        }
    }
}