﻿using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents
{
    public class NotesAccountingPoliciesDataEventStrategy : BaseAggregationStrategy
    {
        public override string Name => EventTypes.AccountingPoliciesNotesDataEvent;

        private readonly ILogger<NotesAccountingPoliciesDataEventStrategy> _logger;
        private readonly IMapper _mapper;
        private readonly IAccountsBuilderRepository _accountsBuilderRepository;
        private readonly UserContext _userContext;

        public NotesAccountingPoliciesDataEventStrategy(ILogger<NotesAccountingPoliciesDataEventStrategy> logger, IMapper mapper,
            IAccountsBuilderRepository accountsBuilderRepository, UserContext userContext)
        {
            _logger = logger;
            _mapper = mapper;
            _accountsBuilderRepository = accountsBuilderRepository;
            _userContext = userContext;
        }

        public override async Task Process(string requestMessage, Guid tenantId, Guid clientId, Guid periodId, Guid processId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("START event type {eventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, DateTime.UtcNow);

            var accountingPoliciesResponseMessage = Deserialize<AccountingPoliciesResponseMessage>(requestMessage);

            var accountsBuilder = await _accountsBuilderRepository.Get(clientId, periodId, cancellationToken);

            if (accountsBuilder == null || accountsBuilder.TenantId.ToString() != _userContext.TenantId)
            {
                _logger.LogWarning("No accounts builder entity found for clientId {clientId} and periodId {periodId}.", clientId, periodId);
                return;
            }

            var accountingPolicies = _mapper.Map<AccountingPolicies>(accountingPoliciesResponseMessage, opt => opt.Items["ReportType"] = accountsBuilder.ReportingStandard.Type);

            accountsBuilder.AddAccountingPolicies(accountingPolicies);

            await _accountsBuilderRepository.Save(accountsBuilder, cancellationToken);

            _logger.LogInformation("FINISHED event type {eventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, DateTime.UtcNow);
        }
    }
}
