﻿using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Iris.AccountsProduction.AccountsBuilder.Messages.AccountPeriod;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents
{
    public class AccountPeriodDataEventStrategy : BaseAggregationStrategy
    {
        public override string Name => EventTypes.AccountPeriodDataEvent;

        private readonly IAccountsBuilderRepository _repository;
        private readonly ILogger<AccountPeriodDataEventStrategy> _logger;
        private readonly IMapper _mapper;

        public AccountPeriodDataEventStrategy(IAccountsBuilderRepository repository,
            ILogger<AccountPeriodDataEventStrategy> logger, 
            IMapper mapper)
        {
            _repository = repository;
            _logger = logger;
            _mapper = mapper;
        }

        public override async Task Process(string requestMessage, Guid tenantId, Guid clientId, Guid periodId, Guid processId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("START event type {eventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, DateTime.UtcNow);

            var accountPeriodDto = Deserialize<AccountPeriodMessage>(requestMessage);

            var accountsBuilder = await _repository.Get(clientId, periodId, cancellationToken);

            if (accountsBuilder == null)
            {
                _logger.LogWarning("No accounts builder entity found for clientId {clientId} and periodId {periodId}.", clientId, periodId);
                return;
            }

            var accountPeriod = _mapper.Map<AccountPeriod>(accountPeriodDto);

            accountsBuilder.UpdateAccountPeriod(accountPeriod);

            await _repository.Save(accountsBuilder, cancellationToken);

            _logger.LogInformation("Updated accounts builder with new AccountPeriod data for client id {clientId} and period id {periodId}.", clientId, periodId);

            _logger.LogInformation("FINISHED event type {eventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, DateTime.UtcNow);
        }
    }
}
