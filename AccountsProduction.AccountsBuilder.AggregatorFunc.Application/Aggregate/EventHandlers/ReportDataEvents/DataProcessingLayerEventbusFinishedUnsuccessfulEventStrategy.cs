﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.Invocation;
using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.Message;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents
{
    public class DataProcessingLayerEventbusFinishedUnsuccessfulEventStrategy(IAccountsBuilderRepository _repository,
        ILogger<DataProcessingLayerEventbusFinishedUnsuccessfulEventStrategy> _logger, IMapper _mapper, IDomainEventService _domainEventService)
        : DataProcessingLayerEventbusFinishedEventbusBaseStrategy<ErrorFinancialDataEventbusDto>()
    {
        public override string TopicName => "dataprocessinglayer:error-generation";

        public override async Task<ExecutionResult> ExecuteAsync(EventBusMessage<ErrorFinancialDataEventbusDto> message)
        {
            _logger.LogInformation("START event type {eventType} for processId {processId} at eventTimestamp {eventTimestamp}.", TopicName, message.Payload.ProcessId, DateTime.UtcNow);

            var accountsBuilder = await _repository.Get(message.Payload.ProcessId, CancellationToken.None);

            if (accountsBuilder is null)
            {
                _logger.LogWarning("No accounts builder entity found for processId {processId}.", message.Payload.ProcessId);
                return new ExecutionResult()
                {
                    Payload = null,
                    Status = System.Net.HttpStatusCode.NotFound
                };
            }

            if (accountsBuilder.FinancialData.Retry < 3)
            {
                await (this as IDataProcessingLayerEventFinishedEventSharedBase).PublishDplRetry(accountsBuilder, _domainEventService, _repository, _mapper, CancellationToken.None);
            }
            else
            {
                var financialDataDto = _mapper.Map<FinancialDataDto>(message.Payload, opt =>
                {
                    opt.Items[nameof(FinancialDataDto.ClientId)] = accountsBuilder.ClientId;
                    opt.Items[nameof(FinancialDataDto.PeriodId)] = accountsBuilder.PeriodId;
                });

                await (this as IDataProcessingLayerEventFinishedEventSharedBase).StoreFinancialData(financialDataDto, accountsBuilder, _logger, _repository, _mapper, TopicName, message.Payload.ProcessId, CancellationToken.None);
            }

            return new ExecutionResult()
            {
                Payload = null,
                Status = System.Net.HttpStatusCode.OK
            };
        }
    }
}