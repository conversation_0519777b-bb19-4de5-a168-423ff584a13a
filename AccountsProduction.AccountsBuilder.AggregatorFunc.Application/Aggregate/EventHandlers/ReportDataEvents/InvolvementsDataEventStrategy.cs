﻿using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Iris.AccountsProduction.AccountsBuilder.Messages.Involvements;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents
{
    public class InvolvementsDataEventStrategy : BaseAggregationStrategy
    {
        public override string Name => EventTypes.InvolvementsDataEvent;

        private readonly IAccountsBuilderRepository _repository;
        private readonly ILogger<InvolvementsDataEventStrategy> _logger;
        private readonly IMapper _mapper;

        public InvolvementsDataEventStrategy(IAccountsBuilderRepository repository,
            ILogger<InvolvementsDataEventStrategy> logger, IMapper mapper)
        {
            _repository = repository;
            _logger = logger;
            _mapper = mapper;
        }

        public override async Task Process(string requestMessage, Guid tenantId, Guid clientId, Guid periodId, Guid processId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("START event type {eventType} for clientId {clientId} at eventTimestamp {eventTimestamp}.", Name, clientId, DateTime.UtcNow);

            var involvementMessage = Deserialize<IEnumerable<InvolvementMessage>>(requestMessage);
            var involvements = _mapper.Map<List<Involvement>>(involvementMessage);
            var involvementsData = new InvolvementsData
            {
                Involvements = involvements,
                IsDataCompleted = true
            };

            var accountsBuilders = (await _repository.GetAll(clientId, cancellationToken))
                                    .OrderByDescending(o => o.EntityModificationTime)
                                    .ToList();

            await Task.WhenAll(accountsBuilders.Select(s => Save(s, involvementsData, cancellationToken)));

            _logger.LogInformation("FINISHED event type {eventType} for clientId {clientId} at eventTimestamp {eventTimestamp}.", Name, clientId, DateTime.UtcNow);
        }

        private async Task Save(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder, InvolvementsData involvementsData, CancellationToken cancellationToken)
        {
            try
            {
                await Retry.RunAndHandleConcurrency(
                    async () =>
                    {
                        accountsBuilder.AddInvolvements(involvementsData);

                        await _repository.Save(accountsBuilder, cancellationToken);
                    },
                    async () =>
                    {
                        accountsBuilder = await _repository.Get(accountsBuilder.ClientId, accountsBuilder.PeriodId, cancellationToken);
                    },
                    _logger);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Failed to process event with message type {EventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}. ", Name, accountsBuilder.ClientId, accountsBuilder.PeriodId, DateTime.UtcNow);
            }
        }
    }
}