﻿using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents
{
    public class NonFinancialDataEventStrategy : BaseAggregationStrategy
    {
        public override string Name => EventTypes.ClientDataEvent;

        private readonly IAccountsBuilderRepository _repository;
        private readonly ILogger<NonFinancialDataEventStrategy> _logger;

        public NonFinancialDataEventStrategy(IAccountsBuilderRepository repository,
            ILogger<NonFinancialDataEventStrategy> logger, IMapper mapper)
        {
            _repository = repository;
            _logger = logger;
        }

        public override async Task Process(string requestMessage, Guid tenantId, Guid clientId, Guid periodId, Guid processId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("START event type {eventType} for clientId {clientId} at eventTimestamp {eventTimestamp}.", Name, clientId, DateTime.UtcNow);

            var clientDto = JsonConvert.DeserializeObject<JObject>(requestMessage)!;

            var accountsBuilders = (await _repository.GetAll(clientId, cancellationToken))
                                    .OrderByDescending(o => o.EntityModificationTime)
                                    .ToList();

            await Task.WhenAll(accountsBuilders.Select(s => Save(s, clientDto, cancellationToken)));

            _logger.LogInformation("FINISHED event type {eventType} for clientId {clientId}  at eventTimestamp {eventTimestamp}.", Name, clientId, DateTime.UtcNow);
        }

        private async Task Save(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder, JObject clientDto, CancellationToken cancellationToken)
        {
            try
            {
                await Retry.RunAndHandleConcurrency(
                    async () =>
                    {
                        accountsBuilder.UpdateNonFinancialData(clientDto);

                        await _repository.Save(accountsBuilder, cancellationToken);
                    },
                    async () =>
                    {
                        accountsBuilder = await _repository.Get(accountsBuilder.ClientId, accountsBuilder.PeriodId, cancellationToken);
                    },
                    _logger);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Failed to process event with message type {EventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}. ", Name, accountsBuilder.ClientId, accountsBuilder.PeriodId, DateTime.UtcNow);
            }
        }
    }
}