﻿using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.DataScreens;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Iris.AccountsProduction.AccountsBuilder.Messages.ScreenValues;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents
{
    public class DataScreenValueDataEventStrategy : BaseAggregationStrategy
    {
        public override string Name => EventTypes.DatascreensDataEvent;

        private readonly ILogger<NotesFinancialDataEventStrategy> _logger;
        private readonly IMapper _mapper;
        private readonly IAccountsBuilderRepository _repository;
        private readonly UserContext _userContext;

        public DataScreenValueDataEventStrategy(ILogger<NotesFinancialDataEventStrategy> logger, IMapper mapper,
            IAccountsBuilderRepository repository, UserContext userContext)
        {
            _logger = logger;
            _mapper = mapper;
            _repository = repository;
            _userContext = userContext;
        }

        public override async Task Process(string requestMessage, Guid tenantId, Guid clientId, Guid periodId, Guid processId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("START event type {eventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, DateTime.UtcNow);

            var screenValuesDto = DeserializeWithObject<ScreenValuesResponseMessage>(requestMessage);

            var accountsBuilder = await _repository.Get(clientId, periodId, cancellationToken);

            if (accountsBuilder == null || accountsBuilder.TenantId.ToString() != _userContext.TenantId)
            {
                _logger.LogWarning("No accounts builder entity found for clientId {clientId} and periodId {periodId}.", clientId, periodId);
                return;
            }

            var dataScreenValue = _mapper.Map<DataScreenValue>(screenValuesDto);

            _logger.LogInformation($"DataScreenValue received for clientId {clientId} and periodId {periodId}.");

            accountsBuilder.AddDataScreenValue(dataScreenValue);

            await _repository.Save(accountsBuilder, cancellationToken);

            _logger.LogInformation("FINISHED event type {eventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, DateTime.UtcNow);
        }
    }
}