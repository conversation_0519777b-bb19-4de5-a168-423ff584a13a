﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Infrastructure;
using FluentValidation;
using Iris.Elements.Logging.Serilog;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application
{
    [ExcludeFromCodeCoverage]
    public static class DependencyInjection
    {
        public static IServiceCollection AddApplication(this IServiceCollection services, string appName)
        {
            IConfiguration configuration = new ConfigurationBuilder().AddEnvironmentVariables().Build();

            AddInfrastructure(services, configuration, appName);

            var configuredAutoMapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            services.TryAddSingleton(configuredAutoMapper);

            services.AddMediatR(cfg => cfg.RegisterServicesFromAssemblies(Assembly.GetExecutingAssembly(), Assembly.GetAssembly(typeof(ProcessAggregationCommand))));

            RegisterAggregationStrategies(services);

            return services;
        }


        private static void AddInfrastructure(IServiceCollection services, IConfiguration configuration, string appName)
        {
            services.AddTransient(provider => configuration);

            services.AddSerilog(configuration, appName);

            services.AddAWSServices(configuration)
                .AddDapperConfig()
                .AddEventBusServices(configuration, appName)
                .AddGeneralServices()
                .AddHttpClient()
                .AddServices(configuration);

            //services.AddScoped<UserContext>();
        }

        private static void RegisterAggregationStrategies(IServiceCollection services)
        {
            var aggregationStrategyTypes = typeof(IAggregationStrategy).GetTypeInfo().Assembly.GetTypes().Where(p => typeof(IAggregationStrategy).IsAssignableFrom(p) && !p.IsInterface && !p.IsAbstract);

            foreach (var type in aggregationStrategyTypes)
            {
                services.AddScoped(typeof(IAggregationStrategy), type);
            }
        }
    }
}
