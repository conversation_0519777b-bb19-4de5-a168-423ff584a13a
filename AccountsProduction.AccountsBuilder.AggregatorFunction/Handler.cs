using AccountsProduction.AccountsBuilder.AggregatorFunc.Application;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands;
using AccountsProduction.AccountsBuilder.Application.Common.Helper;
using Amazon.Lambda.Core;
using Amazon.Lambda.Serialization.SystemTextJson;
using Amazon.Lambda.SNSEvents;
using Iris.Elements.Logging.Serilog;
using Iris.Elements.Logging.Serilog.Lambda;
using Iris.Elements.Messaging.Message;
using Iris.Platform.Eventbus.Client.Dotnet.InputHandlers;
using Iris.Platform.Eventbus.Client.Dotnet.Services;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;
using ServiceCollectionExtension = Iris.Platform.Eventbus.Client.Dotnet.Helpers.ServiceCollectionExtension;

[assembly: LambdaSerializer(typeof(DefaultLambdaJsonSerializer))]

namespace AccountsProduction.AccountsBuilder.AggregatorFunc
{
    [ExcludeFromCodeCoverage]
    public class Handler
    {
        private readonly UserContext _userContext;
        private readonly ILogger<Handler> _logger;
        private readonly IMediator _mediator;
        private readonly ILambdaInputHandler? _lambdaInputHandler;

        private const string FunctionName = "AccountsBuilderAggregatorFunction";

        private static readonly JsonSerializerOptions DefaultSerializerOptions = new()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };

        public Handler()
        {
            var appName = Environment.GetEnvironmentVariable("AWS_LAMBDA_FUNCTION_NAME");

            if (string.IsNullOrEmpty(appName))
            {
                appName = FunctionName;
            }

            IConfiguration configuration = new ConfigurationBuilder()
                .AddEnvironmentVariables()
                .Build();

            var services = ServiceCollectionExtension.CreateServiceCollection("accountsbuilder-aggregator-func");
            services.AddApplication(appName);
            services.AddSerilog(configuration, appName);

            services.AddTransient<ITopicHandler, DataProcessingLayerEventbusFinishedSuccessfulEventStrategy>();
            services.AddTransient<ITopicHandler, DataProcessingLayerEventbusFinishedUnsuccessfulEventStrategy>();

            var serviceProvider = services.BuildServiceProvider();

            _logger = serviceProvider.GetService<ILogger<Handler>>()!;
            _mediator = serviceProvider.GetService<IMediator>()!;
            _userContext = serviceProvider.GetService<UserContext>()!;
            _lambdaInputHandler = serviceProvider.GetService<ILambdaInputHandler>();

        }

        public Handler(UserContext userContext, ILogger<Handler> logger, IMediator mediator, ILambdaInputHandler lambdaInputHandler)
        {
            _userContext = userContext;
            _logger = logger;
            _mediator = mediator;
            _lambdaInputHandler = lambdaInputHandler;
        }

        /// <summary> Used in unit testing </summary>
        /// <param name="serviceScopeFactory"> The serviceScopeFactory. </param>
        public Handler(ServiceProvider serviceProvider)
        {
            _logger = serviceProvider.GetService<ILogger<Handler>>()!;
            _mediator = serviceProvider.GetService<IMediator>()!;
            _userContext = serviceProvider.GetService<UserContext>()!;
            _lambdaInputHandler = serviceProvider.GetService<ILambdaInputHandler>();
        }

        public async Task HandleEvent(Stream input, ILambdaContext context)
        {
            context.AddLambdaContextLogData();
            string eventSubject = string.Empty;
            try
            {
                using var reader = new StreamReader(input);
                var json = await reader.ReadToEndAsync();
                var snsEvent = JsonSerializer.Deserialize<SNSEvent>(json, DefaultSerializerOptions);

                var record = snsEvent?.Records.FirstOrDefault();
                eventSubject = snsEvent?.Records.FirstOrDefault()?.Sns.Subject ?? string.Empty;

                if (record is not null)
                {
                    switch (eventSubject)
                    {
                        case "dataprocessinglayer:successful-generation":
                        case "dataprocessinglayer:error-generation":

                            // Reset the of the MemoryStream to the beginning for eventbus topic handler processing
                            input.Position = 0;

                            await ProcessEventBus(input);
                            break;
                        default:
                            await ProcessSns(snsEvent!);
                            break;
                    }
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Failed to process event {EventSubject}", eventSubject);
                throw;
            }
        }

        public async Task ProcessSns(SNSEvent snsEvent)
        {
            var record = snsEvent.Records.FirstOrDefault();

            if (record is not null)
            {
                record.Sns.MessageAttributes.TryGetValue("ClientId", out var clientIdValue);
                record.Sns.MessageAttributes.TryGetValue("TenantId", out var tenantIdValue);
                record.Sns.MessageAttributes.TryGetValue("PeriodId", out var periodIdValue);
                record.Sns.MessageAttributes.TryGetValue("ProcessId", out var processIdValue);

                var tenantId = tenantIdValue != null ? Guid.Parse(tenantIdValue.Value) : Guid.NewGuid();
                var clientId = clientIdValue != null ? Guid.Parse(clientIdValue.Value) : Guid.NewGuid();
                var periodId = periodIdValue != null ? Guid.Parse(periodIdValue.Value) : Guid.NewGuid();
                var processId = processIdValue != null ? Guid.Parse(processIdValue.Value) : Guid.NewGuid();

                var elementsMessage = JsonSerializer.Deserialize<ElementsMessage<dynamic>>(record.Sns.Message, DefaultSerializerOptions);
                if (elementsMessage == null)
                {
                    _logger.LogWarning("Element message is null.");
                    return;
                }

                ContextDataHelper.AddLogContextData(elementsMessage.Meta?.CorrelationId, elementsMessage.Meta?.TenantId);
                ContextDataHelper.SetUserContextData(elementsMessage.Meta, _userContext);
                _logger.LogInformation("Tenant Id: {tenantId}", elementsMessage.Meta?.TenantId);

                LogContextExtensions.CreateLogContextProperty("ClientId", clientId);
                LogContextExtensions.CreateLogContextProperty("PeriodId", periodId);

                var topicType = elementsMessage.Meta?.Type!;

                var processCommand = new ProcessAggregationCommand
                {
                    TenantId = tenantId,
                    ClientId = clientId,
                    PeriodId = periodId,
                    ProcessId = processId,
                    MessageType = topicType
                };

                if (((JsonElement)elementsMessage.Message).ValueKind is JsonValueKind.String)
                {
                    processCommand.Message = elementsMessage.Message.ToString();
                }
                else
                {
                    processCommand.Message = JsonSerializer.Serialize(elementsMessage.Message, DefaultSerializerOptions);
                }

                await _mediator.Send(processCommand);
            }
        }

        public async Task ProcessEventBus(Stream stream)
        {
            if (_lambdaInputHandler is null)
            {
                _logger.LogWarning("Input handler was not found.");
                return;
            }

            await _lambdaInputHandler.Initialize(stream);
        }
    }
}