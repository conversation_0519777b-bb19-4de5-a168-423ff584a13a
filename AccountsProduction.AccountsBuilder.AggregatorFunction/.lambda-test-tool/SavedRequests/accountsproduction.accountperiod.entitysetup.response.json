{"Records": [{"EventSource": "aws:sns", "EventVersion": "1.0", "EventSubscriptionArn": "arn:{partition}:sns:EXAMPLE", "Sns": {"Type": "Notification", "MessageId": "95df01b4-ee98-5cb9-9903-4c221d41eb5e", "TopicArn": "arn:{partition}:sns:EXAMPLE", "Subject": "TestInvoke", "Message": "{\"meta\": {\"correlationId\": \"7969a68f-7bd7-473f-90b4-c665e55b3830\",\"version\": \"1.0.0\",\"createdAtUtc\": \"2024-02-08T10:50:52.5630558Z\",\"type\": \"accountsproduction.accountperiod.entitysetup.response\",\"tenantId\": \"0f79d55c-e93a-4151-b1cd-8bf37834514a\",\"source\": \"AccountsProduction\",\"createdByUserId\": \"00u6g4qvpxRqd78Qq0x7\"},\"message\": {\"dateOccurred\": \"2024-02-08T10:50:52.5592042Z\",\"eventType\": \"AccountsBuilderEntitySetupChanged\",\"clientId\":\"ba952d60-62b2-4e41-8113-7d7a08f774fe\",\"periodId\": \"b94b2d37-2113-43d8-91e7-a36c8f6b2e97\",\"entitySize\" : \"Small\",\"independentReviewType\": \"Accountants\", \"terminology\": null, \"tradingStatus\": \"Trading\", \"dormantStatus\": null, \"choiceOfStatement\": null }}", "Timestamp": "1970-01-01T00:00:00Z", "SignatureVersion": "1", "Signature": "EXAMPLE", "SigningCertUrl": "EXAMPLE", "UnsubscribeUrl": "EXAMPLE", "MessageAttributes": {"Test": {"Type": "String", "Value": "TestString"}, "TestBinary": {"Type": "Binary", "Value": "TestBinary"}, "ProcessId": {"Type": "String", "Value": "46c516df-06d7-4f6a-8f15-1a2c6d90a056"}}}}]}