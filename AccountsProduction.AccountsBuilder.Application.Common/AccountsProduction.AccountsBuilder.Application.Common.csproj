﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<SonarQubeTestProject>false</SonarQubeTestProject>
		<PublishReadyToRun>true</PublishReadyToRun>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Commands\**" />
		<Compile Remove="Exceptions\**" />
		<EmbeddedResource Remove="Commands\**" />
		<EmbeddedResource Remove="Exceptions\**" />
		<None Remove="Commands\**" />
		<None Remove="Exceptions\**" />
	</ItemGroup>

	<ItemGroup>
		<Compile Remove="AutoMapper\Mappers\ReportTypeMapper.cs" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="13.0.1" />
		<PackageReference Include="Iris.Elements.Logging.Serilog.Lambda" Version="2.0.0.22" />
		<PackageReference Include="Iris.Elements.Messaging" Version="1.0.0.33" />
		<PackageReference Include="Iris.AccountsProduction.AccountsBuilder.Messages" Version="1.0.0.305" />
		<PackageReference Include="Iris.Platform.Eventbus.Client.Dotnet" Version="5.0.0.475" />
		<PackageReference Include="MediatR" Version="12.4.1" />
		<PackageReference Include="Iris.AccountsProduction.Common.Toolkit" Version="2.0.0.259" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\AccountsProduction.AccountsBuilder.Domain\AccountsProduction.AccountsBuilder.Domain.csproj" />
	</ItemGroup>

</Project>
