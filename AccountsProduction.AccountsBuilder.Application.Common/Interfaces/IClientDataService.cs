﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Address;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.NonFinancialData;

namespace AccountsProduction.AccountsBuilder.Application.Common.Interfaces
{
    public interface IClientDataService
    {
        public Task<NonFinancialDataDto> GetNonFinancialDataFromClient(Guid clientIdentifier, CancellationToken cancellationToken = default);
        public Task<List<AddressResponseDto>> GetClientAddress(Guid clientIdentifier, CancellationToken cancellationToken = default);

        public Task<List<ClientInvolvementDto>> GetClientInvolvements(Guid clientIdentifier, CancellationToken cancellationToken = default);
    }
}
