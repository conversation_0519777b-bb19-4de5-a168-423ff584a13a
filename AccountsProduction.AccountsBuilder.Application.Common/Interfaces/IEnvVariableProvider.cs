﻿namespace AccountsProduction.AccountsBuilder.Application.Common.Interfaces
{
    public interface IEnvVariableProvider
    {
        string AccountsProductionAccountPeriodRequestTopic { get; }

        string AccountsProductionAccountsBuilderInputTopic { get; }

        string ReportingTrialBalanceRequestTopic { get; }

        string AccountsProductionInvolvementsQueue { get; }

        string ClientApiUrl { get; }

        string ClientInvolvementApiUrl { get; }

        string ClientApiKey { get; }
        string ClientApiId { get; }

        string AwsRegion { get; }
        string AwsDefaultRegion { get; }

        string AwsAccessKey { get; }

        string AwsSecretKey { get; }

        string AwsSessionToken { get; }
        string AwsPrivateAccessKey { get; }

        string AwsPrivateSecretKey { get; }

        string AwsPrivateSessionToken { get; }

        string ClientApiScheme { get; }

        string ClientApiHost { get; }

        string ClientAddressApiUrl { get; }

        string AccountsBuilderTimeoutQueueUrl { get; }

        string TimeoutDelaySeconds { get; }
        string SubscriptionHubScheme { get; }
        string SubscriptionHubHost { get; }
        string SubscriptionHubApiKey { get; }
        string SubscriptionHubApiId { get; }
        string Source { get; }

        string AccountPeriodApiScheme { get; }
        string AccountPeriodApiHost { get; }
        string AccountPeriodApiKey { get; }
        string AccountPeriodApiId { get; }

        string TrialBalanceApiScheme { get; }
        string TrialBalanceApiHost { get; }
        string TrialBalanceApiKey { get; }
        string TrialBalanceApiId { get; }

        string AccountsProductionApiScheme { get; }
        string AccountsProductionApiHost { get; }
        string AccountsProductionApiId { get; }
        string AccountsProductionApiKey { get; }

        string AccountsBuilderApiScheme { get; }
        string AccountsBuilderApiHost { get; }
        string AccountsBuilderApiId { get; }
        string AccountsBuilderApiKey { get; }

        bool SendDplInboundViaEventbus { get; }

        bool DisableDplMessageForCharities { get; }
    }
}
