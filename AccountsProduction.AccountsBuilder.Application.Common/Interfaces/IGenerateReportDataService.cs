﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.PracticeDetails;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Report;

namespace AccountsProduction.AccountsBuilder.Application.Common.Interfaces
{
    public interface IGenerateReportDataService
    {
        Task<GenerateReportDto> GetReportData(Guid clientId, Guid periodId);
        Task<PracticeDetailMessage?> GetPracticeDetails(string? practiceDetailId);
    }
}
