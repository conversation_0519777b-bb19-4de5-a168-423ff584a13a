﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;

namespace AccountsProduction.AccountsBuilder.Application.IntegrationEvents;
public class NotificationTrialBalanceChanged : IDomainEventNotification
{
    public string EventType { get; }
    public Guid ClientId { get; }
    public Guid PeriodId { get; }
    public Guid ProcessId { get; }
    public string ReportingStandardType { get; }
    public TrialBalanceDto Data { get; }
    public DateTime DateOccurred { get; set; }

    public NotificationTrialBalanceChanged(Guid clientId, Guid periodId, Guid processId, string reportingStandardType, TrialBalanceDto data)
    {
        PeriodId = periodId;
        ProcessId = processId;
        ReportingStandardType = reportingStandardType;
        Data = data;
        ClientId = clientId;
        EventType = nameof(NotificationTrialBalanceChanged);
        DateOccurred = DateTime.UtcNow;
    }
}