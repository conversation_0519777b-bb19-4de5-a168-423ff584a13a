﻿using System.Text.Json.Serialization;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.Dpl
{
    public class DplMessageMeta
    {
        [JsonPropertyName("correlationId")]
        public Guid? CorrelationId { get; set; } = null!;

        [JsonPropertyName("createdAtUtc")]
        public DateTime CreatedAtUtc { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; } = null!;

        [JsonPropertyName("tenantId")]
        public Guid TenantId { get; set; }

        [JsonPropertyName("createdByUserId")]
        public string CreatedByUserId { get; set; } = null!;

        [JsonPropertyName("clientId")]
        public Guid ClientId { get; set; }

        [JsonPropertyName("periodId")]
        public Guid PeriodId { get; set; }

        [JsonPropertyName("processId")]
        public Guid ProcessId { get; set; }
    }
}