﻿using Newtonsoft.Json;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.Dpl
{
    public class DplRequestDataDto
    {
        [JsonProperty("Description")]
        public string Description { get; set; } = null!;

        [JsonProperty("Amount")]
        public decimal Amount { get; set; }

        [JsonProperty("AccountCode")]
        public int AccountCode { get; set; }

        [JsonProperty("SubAccountCode")]
        public int? SubAccountCode { get; set; }

        [JsonProperty("Year")]
        public DateTime? Year { get; set; }

        [JsonProperty("DirectorInvolvementId")]
        public int? DirectorInvolvementId { get; set; }

        [JsonProperty("SectorId")]
        public Guid? SectorId { get; set; }

        [JsonProperty("SectorCreatedDate")]
        public DateTime? SectorCreatedDate { get; set; }

        [JsonProperty("FundId")]
        public Guid? FundId { get; set; }

        [JsonProperty("ActivityId")]
        public Guid? ActivityId { get; set; }

        [JsonProperty("GrantId")]
        public Guid? GrantId { get; set; }
    }
}