﻿using Newtonsoft.Json;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.Dpl
{
    public class DplRequestDataRoundingOptionsDto
    {
        [JsonProperty("Year")]
        public DateTime? Year { get; set; }

        [JsonProperty("ProfitLossRoundingAccount")]
        public int ProfitLossRoundingAccount { get; set; }

        [JsonProperty("ProfitLossRoundingSubAccount")]
        public int? ProfitLossRoundingSubAccount { get; set; }

        [JsonProperty("ProfitLossRoundingAccountDescription")]
        public string ProfitLossRoundingAccountDescription { get; set; } = null!;

        [JsonProperty("BalanceSheetRoundingAccount")]
        public int BalanceSheetRoundingAccount { get; set; }

        [JsonProperty("BalanceSheetRoundingSubAccount")]
        public int? BalanceSheetRoundingSubAccount { get; set; }

        [JsonProperty("BalanceSheetRoundingAccountDescription")]
        public string BalanceSheetRoundingAccountDescription { get; set; } = null!;

        [JsonProperty("UseAdvancedRounding")]
        public bool UseAdvancedRounding { get; set; }
    }
}