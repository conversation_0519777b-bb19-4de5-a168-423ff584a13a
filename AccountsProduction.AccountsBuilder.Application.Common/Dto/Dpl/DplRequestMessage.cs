﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using System.Text.Json.Serialization;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.Dpl
{
    public class DplRequestMessage
    {
        [JsonPropertyName("clientId")]
        public Guid ClientId { get; set; }

        [JsonPropertyName("data")]
        public List<DplRequestDataDto> Data { get; set; } = [];

        [JsonPropertyName("roundingOptions")]
        public List<DplRequestDataRoundingOptionsDto> RoundingOptions { get; set; } = [];

        [JsonPropertyName("accountChartIdentifier")]
        public string AccountChartIdentifier { get; set; } = null!;

        [JsonPropertyName("groupStructureIdentifier")]
        public int GroupStructureIdentifier { get; set; }

        [JsonPropertyName("sectorsInformation")]
        public List<SectorInfoDto> SectorsInformation { get; set; } = [];
    }
}