﻿using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.StreamContent;
using System.Text.Json.Serialization;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.Dpl
{
    public class DplEventbusMessage
    {
        [JsonPropertyName("meta")]
        public DplMessageMeta Meta { get; set; } = null!;
        [JsonPropertyName("message")]
        public EventbusStreamContent Message { get; set; } = null!;
    }
}