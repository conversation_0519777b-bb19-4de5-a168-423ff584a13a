﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData;
using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Domain;
using Iris.AccountsProduction.AccountsBuilder.Messages.AccountPeriod;
using Iris.AccountsProduction.AccountsBuilder.Messages.DataScreenValue;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes
{
    public abstract class BaseReportingMessage
    {
        public string? ReportType { get; set; }

        public ReportingStandardVersion? ReportVersion { get; set; }

        public Guid ClientId { get; set; }

        public Guid TenantId { get; set; }

        public Guid PeriodId { get; set; }

        public string WatermarkText { get; set; } = null!;

        public AccountPeriodMessage AccountPeriod { get; set; } = new();
        public EntitySetupDto EntitySetup { get; set; } = new();
        public PracticeDetailsDto PracticeDetails { get; set; } = null!;
        public ClientDocumentMessageDto ClientData { get; set; } = null!;
        public List<ClientInvolvementDto> Involvements { get; set; } = new();
        public List<ReportingPeriodDto> ReportingPeriods { get; set; } = new();
        public ReportingSignatureDto Signatures { get; set; } = null!;
        public List<ProfitAndLossMessage> ProfitAndLossData { get; set; } = new();
        public List<BalanceSheetMessage> BalanceSheetData { get; set; } = new();
        public ProfitShareDataDto ProfitShareData { get; set; } = new();
        public NotesResponseDataMessage Notes { get; set; } = null!;
        public DataScreenValueMessage DataScreenValue { get; set; } = new();
        public AccountingPoliciesResponseDataMessage NoteAccountingPolicies { get; set; } = null!;
        public FinancialDataDto FinancialData { get; set; } = null!;
        public List<OtherMessage> OtherData { get; set; } = new();
        public List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance> TrialBalanceData { get; set; } = new();
        public List<IFRSIncomeStatementMessage> IFRSIncomeStatementData { get; set; } = new();

        public List<IFRSStatementOfFinancialPositionMessage> IFRSStatementOfFinancialPositionData { get; set; } = new();

    }
}
