﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Address;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.Client
{
    public class ClientDocumentMessageDto
    {
        public string CompanyName { get; set; } = null!;

        public string CompanyRegistrationNumber { get; set; } = null!;

        public string CompanyType { get; set; } = null!;

        public string CompanySubType { get; set; } = null!;

        public string CompanyCategory { get; set; } = null!;

        public ClientAddressDto Addresses { get; set; } = null!;
    }
}
