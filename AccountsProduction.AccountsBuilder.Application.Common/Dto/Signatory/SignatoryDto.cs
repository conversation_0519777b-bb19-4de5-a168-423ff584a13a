﻿using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.Signatory
{
    public class SignatoryResponseDto
    {
        public Guid ClientId { get; set; }
        public Guid PeriodId { get; set; }
        public SignatureDetailResponseDto Signatory { get; set; } = null!;
        public string Error { get; set; } = null!;
        public bool? IsSuccessful { get; set; }
    }

    public class SignatureDetailResponseDto
    {
        public List<SignatureResponseDto> Signatures { get; set; } = new();
        public DateTime? AccountantSigningDate { get; set; }
        public string? AccountantSigningName { get; set; }
        public bool? IncludeAccountantsReport { get; set; }
    }

    public class SignatureResponseDto
    {
        public Guid SignatoryId { get; set; }
        public int OrderNumber { get; set; }
        public DateTime? SigningDate { get; set; }
        public SignatureType SignatureType { get; set; }
        public string InvolvementType { get; set; } = null!;
    }
}
