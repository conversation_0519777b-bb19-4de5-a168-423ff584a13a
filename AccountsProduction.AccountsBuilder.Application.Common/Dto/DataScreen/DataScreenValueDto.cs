﻿using AccountsProduction.AccountsBuilder.Domain.DataScreens;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.DataScreen
{
    public class DataScreenValueDto
    {
        public Guid TenantId { get; set; }
        public Guid ClientId { get; set; }
        public Guid PeriodId { get; set; }
        public Guid? PreviousPeriodId { get; set; }
        public Guid CorrelationId { get; set; } = Guid.NewGuid();
        public List<PeriodScreenValueDto> CurrentPeriod { get; set; } = new List<PeriodScreenValueDto>();
        public List<PeriodScreenValueDto> PreviousPeriod { get; set; } = new List<PeriodScreenValueDto>();
        public string Error { get; set; }
        public bool? IsSuccessful { get; set; }
    }
}
