﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Domain;
using System.Text.Json.Serialization;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder
{
    public class AccountsBuilderDto
    {
        public Guid ClientId { get; set; }
        public Guid PeriodId { get; set; }
        [JsonConverter(typeof(JsonCustomGuidConverter))]
        public Guid PreviousPeriodId { get; set; }
        public ValidationDataDto ValidationData { get; set; } = new ValidationDataDto();
        public bool IsDirty { get; set; }
        public DateTime? LastSuccessfullTimeUtc { get; set; }
        public Guid? LastSuccessfullProcessId { get; set; }
        public EntitySetupDto? EntitySetup { get; set; } = null!;
        public ReportingStandardDto? ReportingStandard { get; set; } = null!;
        public LicenseData LicenseData { get; set; } = null!;
        public Status Status { get; set; }
        public string? ErrorCode { get; set; } = null!;

    }
}
