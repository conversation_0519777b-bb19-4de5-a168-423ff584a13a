﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.DataScreen;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.NonFinancialData;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Domain;
using Iris.AccountsProduction.AccountsBuilder.Messages.AccountPeriod;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;
using Iris.AccountsProduction.AccountsBuilder.Messages.ProfitShare;
using System.Text.Json.Serialization;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder
{
    public class AccountsBuilderFullDto
    {
        public Guid ClientId { get; set; }
        public Guid PeriodId { get; set; }
        [JsonConverter(typeof(JsonCustomGuidConverter))]
        public Guid PreviousPeriodId { get; set; }
        public Guid ProcessId { get; set; }
        public Guid? LastSuccessfullProcessId { get; set; }
        public string TenantId { get; set; } = null!;
        public DateTime EntityModificationTime { get; set; }
        public AccountPeriodMessage AccountPeriod { get; set; } = null!;
        public EntitySetupDto EntitySetup { get; set; } = null!;
        public ReportingStandardDto ReportingStandard { get; set; } = null!;
        public TrialBalanceDto TrialBalance { get; set; } = null!;
        public FinancialDataDto FinancialData { get; set; } = null!;
        public NonFinancialDataDto NonFinancialData { get; set; } = null!;
        public List<ClientInvolvementDto> Involvements { get; set; } = new();
        public ValidationDataDto ValidationData { get; set; } = null!;
        public ProfitShareDataMessage ProfitShareData { get; set; } = null!;
        public NotesResponseMessage Notes { get; set; } = null!;
        public PracticeDetailsDto PracticeDetails { get; set; } = null!;
        public AccountingPoliciesResponseMessage AccountingPolicies { get; set; } = null!;
        public LicenseDataDto LicenseData { get; set; } = null!;
        public DataScreenValueDto DataScreenValue { get; set; } = null!;
        public bool IsDirty { get; set; }
        public Status Status { get; set; }
        public DateTime? LastSuccessfullTimeUtc { get; set; }
        public string ErrorCode { get; set; } = null!;
    }
}
