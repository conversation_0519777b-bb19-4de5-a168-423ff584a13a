﻿namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup
{
    public class EntitySetupDto
    {
        public string? EntitySize { get; set; }

        public string? IndependentReviewType { get; set; }

        public string? ReportingStandard { get; set; }

        public string? Terminology { get; set; }

        public string? TradingStatus { get; set; }

        public string? DormantStatus { get; set; }

        public string? ChoiceOfStatement { get; set; }

        public string? CIC34Report { get; set; }

        public string? PracticeAddress { get; set; }

        public string? CharitySize { get; set; }
    }
}
