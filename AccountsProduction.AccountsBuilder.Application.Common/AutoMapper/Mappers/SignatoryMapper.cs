﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Signatory;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class SignatoryMapper : Profile
    {
        public SignatoryMapper()
        {
            CreateMap<SignatoryResponseDto, Signatory>()
                .ForMember(s => s.Signature, o => o.MapFrom(d => d.Signatory))
                .ForMember(s => s.PeriodId, o => o.MapFrom(d => d.PeriodId))
                .ForMember(s => s.ClientId, o => o.MapFrom(d => d.ClientId))
                .ForMember(s => s.IsSuccessful, o => o.MapFrom(d => d.IsSuccessful))
                .ForMember(s => s.EntityModificationTime, o => o.Ignore())
                .ForMember(s => s.Error, o => o.MapFrom(d => d.Error))
                .ReverseMap();

            CreateMap<SignatureDetailResponseDto, SignatureDetail>()
                .ForMember(s => s.AccountantSigningDate, o => o.MapFrom(d => d.AccountantSigningDate))
                .ForMember(s => s.AccountantSigningName, o => o.MapFrom(d => d.AccountantSigningName))
                .ForMember(s => s.IncludeAccountantsReport, o => o.MapFrom(d => d.IncludeAccountantsReport))
                .ForMember(s => s.Signatures, o => o.MapFrom(d => d.Signatures))
                .ReverseMap();

            CreateMap<SignatureResponseDto, Signature>()
                .ForMember(s => s.SignatoryId, o => o.MapFrom(d => d.SignatoryId))
                .ForMember(s => s.SignatureType, o => o.MapFrom(d => d.SignatureType))
                .ReverseMap();

            CreateMap<ReportingSignatureDetailDto, Signature>()
                .ForMember(s => s.SignatoryId, o => o.MapFrom(d => d.InvolvementUUID))
                .ForMember(s => s.OrderNumber, o => o.MapFrom(d => d.DisplayOrder))
                .ForMember(s => s.SigningDate, o => o.MapFrom(d => d.SignatureDate))
                .ReverseMap();

            CreateMap<SignatureDetail, ReportingSignatureDto>()
                .ForMember(s => s.Signatures, o => o.MapFrom(d => d.Signatures))
                .ForMember(s => s.AccountantSigningDate, o => o.MapFrom(d => d.AccountantSigningDate))
                .ForMember(s => s.AccountantSigningName, o => o.MapFrom(d => d.AccountantSigningName))
                .ForMember(s => s.IncludeAccountantsReport, o => o.MapFrom(d => d.IncludeAccountantsReport))
                .ReverseMap();
        }
    }
}
