﻿using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class IFRSStatementOfFinancialPositionMapper : Profile
    {
        public IFRSStatementOfFinancialPositionMapper()
        {
            CreateMap<Financial, IFRSStatementOfFinancialPositionMessage>()
            .ForMember(s => s.PeriodId, o => o.Ignore())
            .ForMember(d => d.Goodwill, opt => opt.MapFrom(s => s.Goodwill))
            .ForMember(d => d.IntangibleAssetsOwned, opt => opt.MapFrom(s => s.IntangibleAssetsOwned))
            .ForMember(d => d.PropertyPlantAndEquipmentOwned, opt => opt.MapFrom(s => s.PropertyPlantAndEquipmentOwned))
            .ForMember(d => d.InvestmentPropertyOwned, opt => opt.MapFrom(s => s.InvestmentPropertyOwned))
            .ForMember(d => d.IntangibleAssetsROU, opt => opt.MapFrom(s => s.IntangibleAssetsROU))
            .ForMember(d => d.PropertyPlantAndEquipmentROU, opt => opt.MapFrom(s => s.PropertyPlantAndEquipmentROU))
            .ForMember(d => d.InvestmentPropertyROU, opt => opt.MapFrom(s => s.InvestmentPropertyROU))
            .ForMember(d => d.InvestmentInAssociates, opt => opt.MapFrom(s => s.InvestmentInAssociates))
            .ForMember(d => d.NCAInvestments, opt => opt.MapFrom(s => s.NCAInvestments))
            .ForMember(d => d.LoansAndOtherFinancialAssets, opt => opt.MapFrom(s => s.LoansAndOtherFinancialAssets))
            .ForMember(d => d.PensionAsset, opt => opt.MapFrom(s => s.PensionAsset))
            .ForMember(d => d.RetirementHealthcareBenefitsAsset, opt => opt.MapFrom(s => s.RetirementHealthcareBenefitsAsset))
            .ForMember(d => d.NCATradeAndOtherReceivables, opt => opt.MapFrom(s => s.NCATradeAndOtherReceivables))
            .ForMember(d => d.NCAContractAssets, opt => opt.MapFrom(s => s.NCAContractAssets))
            .ForMember(d => d.NCATaxReceivable, opt => opt.MapFrom(s => s.NCATaxReceivable))
            .ForMember(d => d.NCADeferredTax, opt => opt.MapFrom(s => s.NCADeferredTax))
            .ForMember(d => d.Inventories, opt => opt.MapFrom(s => s.Inventories))
            .ForMember(d => d.CATradeAndOtherReceivables, opt => opt.MapFrom(s => s.CATradeAndOtherReceivables))
            .ForMember(d => d.CAContractAssets, opt => opt.MapFrom(s => s.CAContractAssets))
            .ForMember(d => d.CATaxReceivable, opt => opt.MapFrom(s => s.CATaxReceivable))
            .ForMember(d => d.CAInvestments, opt => opt.MapFrom(s => s.CAInvestments))
            .ForMember(d => d.CashAndCashEquivalents, opt => opt.MapFrom(s => s.Revenue))
            .ForMember(d => d.Prepayments, opt => opt.MapFrom(s => s.Prepayments))
            .ForMember(d => d.CLTradeAndOtherPayables, opt => opt.MapFrom(s => s.CLTradeAndOtherPayables))
            .ForMember(d => d.CLContractLiabilities, opt => opt.MapFrom(s => s.CLContractLiabilities))
            .ForMember(d => d.CLFinancialLiabilitiesBorrowings, opt => opt.MapFrom(s => s.CLFinancialLiabilitiesBorrowings))
            .ForMember(d => d.CLTaxPayable, opt => opt.MapFrom(s => s.CLTaxPayable))
            .ForMember(d => d.CLProvisions, opt => opt.MapFrom(s => s.CLProvisions))
            .ForMember(d => d.NCLTradeAndOtherPayables, opt => opt.MapFrom(s => s.NCLTradeAndOtherPayables))
            .ForMember(d => d.NCLContractLiabilities, opt => opt.MapFrom(s => s.NCLContractLiabilities))
            .ForMember(d => d.NCLFinancialLiabilitiesBorrowings, opt => opt.MapFrom(s => s.NCLFinancialLiabilitiesBorrowings))
            .ForMember(d => d.NCLTaxPayable, opt => opt.MapFrom(s => s.NCLTaxPayable))
            .ForMember(d => d.NCLDeferredTax, opt => opt.MapFrom(s => s.NCLDeferredTax))
            .ForMember(d => d.PensionLiabilities, opt => opt.MapFrom(s => s.PensionLiabilities))
            .ForMember(d => d.RetirementHealthcareBenefitsLiability, opt => opt.MapFrom(s => s.RetirementHealthcareBenefitsLiability))
            .ForMember(d => d.CalledUpShareCapital, opt => opt.MapFrom(s => s.CalledUpShareCapital))
            .ForMember(d => d.SharePremium, opt => opt.MapFrom(s => s.SharePremium))
            .ForMember(d => d.RevaluationReserve, opt => opt.MapFrom(s => s.RevaluationReserve))
            .ForMember(d => d.CapitalRedemptionReserve, opt => opt.MapFrom(s => s.CapitalRedemptionReserve))
            .ForMember(d => d.OtherReserves1, opt => opt.MapFrom(s => s.OtherReserves1))
            .ForMember(d => d.OtherReserves2, opt => opt.MapFrom(s => s.OtherReserves2))
            .ForMember(d => d.FairValueReserve, opt => opt.MapFrom(s => s.FairValueReserve))
            .ForMember(d => d.RetainedEarnings, opt => opt.MapFrom(s => s.RetainedEarnings))
            .ReverseMap();

        }

    }
}
