﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Dpl;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AutoMapper;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class DplDataMapper : Profile
    {
        public DplDataMapper()
        {
            CreateMap<PeriodTrialBalanceDto, DplRequestDataDto>()
                // Fund mappings
                .ForMember(dest => dest.FundId, opt => opt.MapFrom((src, dest, destMember, context) =>
                    GetSectorInfo(src, context, SectorType.Fund)?.Id))
                // Activity mappings
                .ForMember(dest => dest.ActivityId, opt => opt.MapFrom((src, dest, destMember, context) =>
                    GetSectorInfo(src, context, SectorType.Activity)?.Id))
                // Grant mappings
                .ForMember(dest => dest.GrantId, opt => opt.MapFrom((src, dest, destMember, context) =>
                    GetSectorInfo(src, context, SectorType.Grant)?.Id));
        }

        private static SectorInfoDto? GetSectorInfo(PeriodTrialBalanceDto src, ResolutionContext context, string type)
        {
            if (context.Items.TryGetValue(nameof(TrialBalanceDto.SectorsInformation), out object? value) && value is List<SectorInfoDto> sectorInfoList)
            {
                return sectorInfoList.Find(s => src.CharityOperationIds.Any(x => x == s.Id) && s.Type == type);
            }
            return null;
        }
    }
}