﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class TrialBalanceMapper : Profile
    {
        public TrialBalanceMapper()
        {
            CreateMap<TrialBalanceDto, TrialBalance>()
                .ForMember(s => s.TrialBalances, o => o.MapFrom(d => d.TrialBalances))
                .ForMember(s => s.ReportingPeriods, o => o.MapFrom(d => d.ReportingPeriods))
                .ForMember(s => s.AccountsChartId, o => o.MapFrom(d => d.AccountsChartId))
                .ForMember(s => s.AccountsChartIdentifier, o => o.MapFrom(d => d.AccountsChartIdentifier))
                .ForMember(s => s.GroupStructureId, o => o.MapFrom(d => d.GroupStructureId))
                .ForMember(s => s.GroupStructureCode, o => o.MapFrom(d => d.GroupStructureCode))
                .ForMember(s => s.PeriodId, o => o.MapFrom(d => d.PeriodId))
                .ForMember(s => s.ClientId, o => o.MapFrom(d => d.ClientId))
                .ForMember(s => s.IsSuccessful, o => o.MapFrom(d => d.IsSuccessful))
                .ForMember(s => s.Error, o => o.MapFrom(d => d.Error))
                .ForMember(s => s.SectorsInformation, o => o.MapFrom(d => d.SectorsInformation))
                .ReverseMap();
            CreateMap<PeriodTrialBalanceDto, PeriodTrialBalance>().ReverseMap();
            CreateMap<ReportingPeriodDto, ReportingPeriod>().ReverseMap();
            CreateMap<SectorInfoDto, SectorInfo>().ReverseMap();
        }
    }
}
