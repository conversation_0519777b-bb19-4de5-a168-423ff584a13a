﻿using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels;
using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels.TangibleFixedAsset;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class AccountingPoliciesMapper : Profile
    {
        public AccountingPoliciesMapper()
        {
            CreateMap<AccountingPolicies, AccountingPoliciesResponseMessage>()
                .ForMember(x => x.CurrentPeriodAccountingPolicies,
                    dest => dest.MapFrom(x => x.CurrentPeriodAccountingPolicies))
                .ForMember(x => x.PreviousPeriodAccountingPolicies,
                    dest => dest.MapFrom(x => x.PreviousPeriodAccountingPolicies))
                .ForMember(dest => dest.IsSuccessful, opt => opt.MapFrom(s => s.IsSuccessful)).ReverseMap();
            CreateMap<AccountingPolicies, AccountingPoliciesResponseDataMessage>();
            CreateMap<AccountingPoliciesResponseDataMessage, IFRS1021AAccountingPoliciesData>()
                .ConstructUsing(src => new FRS1021AAccountingPoliciesData())
                .ReverseMap();
            CreateMap<AccountingPoliciesResponseDataMessage, IUnincorporatedAccountingPoliciesData>()
                .ConstructUsing(src => new UnincorporatedAccountingPoliciesData())
                .ReverseMap();
            CreateMap<AccountingPoliciesResponseDataMessage, IAccountingPoliciesData>()
                .ConstructUsing(src => new AccountingPoliciesData())
                .ReverseMap();
            CreateMap<AccountingPoliciesData, IFRS1021AAccountingPoliciesData>().ReverseMap();
            CreateMap<AccountingPoliciesData, IUnincorporatedAccountingPoliciesData>().ReverseMap();
            CreateMap<AccountingPoliciesData, IAccountingPoliciesData>()
                .ReverseMap();
            CreateMap<AccountingPoliciesResponseDataMessage, AccountingPoliciesData>().ConvertUsing<AccountingPoliciesResponseDataMessageToAccountingPoliciesDataConverter>();
            CreateMap<AccountingPoliciesData, AccountingPoliciesResponseDataMessage>();
            CreateMap<ExemptionsFinancialStatements, ExemptionsFinancialStatementsMessage>().ReverseMap();
            CreateMap<IntangibleAssets, IntangibleAssetsMessage>().ReverseMap();
            CreateMap<TangibleFixedAssets, TangibleFixedAssetsMessage>().ReverseMap();
            CreateMap<PlantAndMachineries, PlantAndMachineriesMessage>().ReverseMap();
            CreateMap<LandAndBuildings, LandAndBuildingsMessage>().ReverseMap();
            CreateMap<AssetsAdjustment, AssetsAdjustmentMessage>().ReverseMap();
        }
        public class AccountingPoliciesResponseDataMessageToAccountingPoliciesDataConverter : ITypeConverter<AccountingPoliciesResponseDataMessage, AccountingPoliciesData>
        {
            public AccountingPoliciesData Convert(AccountingPoliciesResponseDataMessage source, AccountingPoliciesData destination, ResolutionContext context)
            {
                if (source != null)
                {
                    if (context.Items != null && context.Items.TryGetValue("ReportType", out var reportType))
                    {
                        switch (reportType)
                        {
                            case ReportStandardType.FRS102_1A:
                                return context.Mapper.Map<AccountingPoliciesData>(context.Mapper.Map<IFRS1021AAccountingPoliciesData>(source));
                            case ReportStandardType.UNINCORPORATED:
                                return context.Mapper.Map<AccountingPoliciesData>(context.Mapper.Map<IUnincorporatedAccountingPoliciesData>(source));
                        }
                    }

                    return context.Mapper.Map<AccountingPoliciesData>(context.Mapper.Map<IAccountingPoliciesData>(source));

                }

                return null;
            }
        }
    }
}
