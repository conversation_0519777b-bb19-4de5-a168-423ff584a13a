﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder;
using AutoMapper;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class ReportingStandardMapper : Profile
    {
        public ReportingStandardMapper()
        {
            CreateMap<Domain.ReportingStandard, ReportingStandardDto>()
                .ReverseMap();
        }
    }
}