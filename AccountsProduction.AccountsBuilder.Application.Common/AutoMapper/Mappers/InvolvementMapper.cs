﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.Involvements;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class InvolvementMapper : Profile
    {
        public InvolvementMapper()
        {
            CreateMap<ClientInvolvementDto, Involvement>().ReverseMap();
            CreateMap<InvolvementDto, Involvement>()
                .ForMember(dest => dest.InvolvementId, opt => opt.MapFrom(s => s.Id))
                .ForMember(dest => dest.InvolvementType, opt => opt.MapFrom(s => s.InvolvementType))
                .ForMember(dest => dest.InvolvementClientGuid, opt => opt.MapFrom(s => s.InvolvedClientGuid))
                .ForMember(dest => dest.InvolvedClientType, opt => opt.MapFrom(s => s.InvolvedClientType))
                .ForMember(dest => dest.InvolvementClientName, opt => opt.MapFrom(s => s.InvolvedClientName))
                .ForMember(dest => dest.InvolvementTitle, opt => opt.MapFrom(s => s.InvolvedClientTitle))
                .ForMember(dest => dest.InvolvementSurname, opt => opt.MapFrom(s => s.InvolvedClientSurname))
                .ForMember(dest => dest.InvolvementFirstName, opt => opt.MapFrom(s => s.InvolvedClientFirstName))
                .ForMember(dest => dest.InvolvedDateOfDeath, opt => opt.MapFrom(s => s.InvolvedClientDateOfDeath))
                .ForMember(dest => dest.StartDate, opt => opt.MapFrom(s => s.StartDate))
                .ForMember(dest => dest.EndDate, opt => opt.MapFrom(s => s.EndDate))
                .ForMember(dest => dest.PdoCode, opt => opt.MapFrom(s => s.AccountingOrder))
                .ForMember(dest => dest.IsDeleted, opt => opt.MapFrom(s => s.IsDeleted))
                .ReverseMap();

            CreateMap<InvolvementMessage, Involvement>()
                .ForMember(dest => dest.PdoCode, opt => opt.MapFrom(s => s.PdoCode))
                .ForMember(dest => dest.InvolvedClientType, opt => opt.MapFrom(s => s.InvolvedClientType))
                .ForMember(dest => dest.InvolvedDateOfDeath, opt => opt.MapFrom(s => s.InvolvedClientDateOfDeath))
                .ForMember(dest => dest.EndDate, opt => opt.MapFrom(s => s.EndDate))
                .ForMember(dest => dest.InvolvementClientGuid, opt => opt.MapFrom(s => s.InvolvedClientGuid))
                .ForMember(dest => dest.InvolvementClientName, opt => opt.MapFrom(s => s.InvolvedClientName))
                .ForMember(dest => dest.InvolvementFirstName, opt => opt.MapFrom(s => s.InvolvedClientFirstName))
                .ForMember(dest => dest.InvolvementSurname, opt => opt.MapFrom(s => s.InvolvedClientSurname))
                .ForMember(dest => dest.InvolvementTitle, opt => opt.MapFrom(s => s.InvolvedClientTitle))
                .ForMember(dest => dest.InvolvementType, opt => opt.MapFrom(s => s.InvolvementType))
                .ForMember(dest => dest.StartDate, opt => opt.MapFrom(s => s.StartDate))
                .ForMember(dest => dest.InvolvedClientType, opt => opt.MapFrom(s => s.InvolvedClientType))
                .ForMember(dest => dest.InvolvementId, opt => opt.MapFrom(s => s.InvolvementId))
                .ForMember(dest => dest.IsDeleted, opt => opt.MapFrom(s => s.IsDeleted));
        }
    }
}
