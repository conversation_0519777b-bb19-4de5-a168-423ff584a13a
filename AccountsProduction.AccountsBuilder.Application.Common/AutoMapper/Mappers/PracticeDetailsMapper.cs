﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.PracticeDetails;
using AutoMapper;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class PracticeDetailsMapper : Profile
    {
        public PracticeDetailsMapper()
        {
            CreateMap<PracticeDetailMessage, Domain.PracticeDetails>()
                .ForMember(s => s.ReferredType, opt => opt.MapFrom(d => d.ActingNames.ReferredType))
                .ForMember(s => s.AddressCountry, opt => opt.MapFrom(d => d.Address.Country))
                .ForMember(s => s.AddressCounty, opt => opt.MapFrom(d => d.Address.County))
                .ForMember(s => s.AddressLine1, opt => opt.MapFrom(d => d.Address.Address1))
                .ForMember(s => s.AddressLine2, opt => opt.MapFrom(d => d.Address.Address2))
                .ForMember(s => s.AddressLine3, opt => opt.MapFrom(d => d.Address.Address3))
                .ForMember(s => s.AddressPostcode, opt => opt.MapFrom(d => d.Address.Postcode))
                .ForMember(s => s.AddressTown, opt => opt.MapFrom(d => d.Address.City))
                .ForMember(s => s.Name, opt => opt.MapFrom(d => d.Name))
                .ForMember(s => s.SupervisingBody, opt => opt.MapFrom(d => d.ActingNames.SupervisoryBody));

            CreateMap<Domain.PracticeDetails, PracticeDetailsDto>();
        }
    }
}
