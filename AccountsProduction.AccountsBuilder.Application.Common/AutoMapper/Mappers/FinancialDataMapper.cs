﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData;
using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AutoMapper;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class FinancialDataMapper : Profile
    {
        public FinancialDataMapper()
        {
            CreateMap<FinancialDataEventbusDto, FinancialDataDto>()
                .ForMember(s => s.Status, o => o.MapFrom(d => FinancialDataStatusDto.Success))
                .ForMember(s => s.Data, o => o.MapFrom(d => d.Data))
                .ForMember(s => s.ClientId,
                    o => o.MapFrom((src, dest, member, context) => context.Items[nameof(FinancialDataDto.ClientId)]))
                .ForMember(s => s.PeriodId,
                    o => o.MapFrom((src, dest, member, context) => context.Items[nameof(FinancialDataDto.PeriodId)]));

            CreateMap<ErrorFinancialDataEventbusDto, FinancialDataDto>()
                .ForMember(s => s.Status, o => o.MapFrom(d => FinancialDataStatusDto.Error))
                .ForMember(s => s.Reason, o => o.MapFrom(d => d.ErrorMessage))
                .ForMember(s => s.ClientId,
                    o => o.MapFrom((src, dest, member, context) => context.Items[nameof(FinancialDataDto.ClientId)]))
                .ForMember(s => s.PeriodId,
                    o => o.MapFrom((src, dest, member, context) => context.Items[nameof(FinancialDataDto.PeriodId)]));

            CreateMap<FinancialDataDto, FinancialData>()
                .ForMember(s => s.Status, o => o.MapFrom(d => d.Status))
                .ForMember(s => s.Error, o => o.MapFrom(d => d.Reason != null ? d.Reason.Message : string.Empty))
                .ForMember(s => s.Financials, o => o.MapFrom(d => d.Data))
                .ReverseMap();

            CreateMap<Financial, FinancialDto>()
                .ReverseMap();
        }
    }
}
