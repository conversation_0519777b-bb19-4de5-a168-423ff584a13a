﻿using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class IFRSIncomeStatementMapper : Profile
    {
        public IFRSIncomeStatementMapper()
        {
            CreateMap<Financial, IFRSIncomeStatementMessage>()
             .ForMember(s => s.PeriodId, o => o.Ignore())
             .ForMember(d => d.Revenue, opt => opt.MapFrom(s => s.Revenue))
             .ForMember(d => d.CostOfSales, opt => opt.MapFrom(s => s.CostOfSales))
             .ForMember(d => d.GrossProfit, opt => opt.MapFrom(s => s.GrossProfit))
             .ForMember(d => d.OtherOperatingIncome, opt => opt.MapFrom(s => s.OtherOperatingIncome))
             .ForMember(d => d.GainLossOnRevaluationOfAssets, opt => opt.MapFrom(s => s.GainLossOnRevaluationOfAssets))
             .ForMember(d => d.DistributionCosts, opt => opt.MapFrom(s => s.DistributionCosts))
             .ForMember(d => d.AdministrativeExpenses, opt => opt.MapFrom(s => s.AdministrativeExpenses))
             .ForMember(d => d.OtherOperatingExpenses, opt => opt.MapFrom(s => s.OtherOperatingExpenses))
             .ForMember(d => d.OperatingProfitBeforeExceptionalItems, opt => opt.MapFrom(s => s.OperatingProfitBeforeExceptionalItems))
             .ForMember(d => d.ExceptionalItems, opt => opt.MapFrom(s => s.ExceptionalItems))
             .ForMember(d => d.OperatingProfit, opt => opt.MapFrom(s => s.OperatingProfit))
             .ForMember(d => d.FinanceCosts, opt => opt.MapFrom(s => s.FinanceCosts))
             .ForMember(d => d.FinanceIncome, opt => opt.MapFrom(s => s.FinanceIncome))
             .ForMember(d => d.ProfitBeforeIncomeTax, opt => opt.MapFrom(s => s.ProfitBeforeIncomeTax))
             .ForMember(d => d.IncomeTax, opt => opt.MapFrom(s => s.IncomeTax))
             .ForMember(d => d.ProfitForTheFinancialYear, opt => opt.MapFrom(s => s.ProfitForTheFinancialYear))
             .ReverseMap();
        }
    }
}
