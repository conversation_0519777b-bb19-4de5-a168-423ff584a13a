﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder;
using AutoMapper;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class ValidationMapper : Profile
    {
        public ValidationMapper()
        {
            CreateMap<Domain.ValidationData, ValidationDataDto>();
            CreateMap<Domain.ValidationIssue, ValidationIssueDto>();
        }
    }
}