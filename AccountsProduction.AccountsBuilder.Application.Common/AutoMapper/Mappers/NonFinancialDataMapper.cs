﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.NonFinancialData;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class NonFinancialDataMapper : Profile
    {
        public NonFinancialDataMapper()
        {
            CreateMap<NonFinancialData, ClientResponse>()
                .ForMember(s => s.Name, o => o.MapFrom(d => d.CompanyName))
                .ForMember(s => s.Id, o => o.Ignore())
                .ReverseMap();

            CreateMap<NonFinancialData, NonFinancialDataDto>()
               .ForPath(s => s.Document.RegisteredNo, o => o.MapFrom(d => d.RegisteredNo))
               .ForPath(s => s.Document.LimitedCompanyType, o => o.MapFrom(d => d.LimitedCompanyType))
               .ForPath(s => s.Document.Name, o => o.MapFrom(d => d.CompanyName))
               .ForPath(s => s.Document.BusinessType, o => o.MapFrom(d => d.BusinessType))
               .ForPath(s => s.Document.BusinessSubType, o => o.MapFrom(d => d.BusinessSubType))
               .ForPath(s => s.Document.ClientAddresses, o => o.MapFrom(d => d.ClientAddresses))
               .ForMember(s => s.UUID, o => o.Ignore());

            CreateMap<NonFinancialData, ClientDocumentMessageDto>()
                .ForPath(s => s.CompanyRegistrationNumber, o => o.MapFrom(d => d.RegisteredNo))
                .ForPath(s => s.CompanySubType, o => o.MapFrom(d => d.LimitedCompanyType))
                .ForPath(s => s.CompanyType, o => o.MapFrom(d => d.BusinessType))
                .ForPath(s => s.CompanyCategory, o => o.MapFrom(d => d.BusinessSubType))
                .ForPath(s => s.Addresses, o => o.MapFrom(d => d.ClientAddresses));
        }
    }
}
