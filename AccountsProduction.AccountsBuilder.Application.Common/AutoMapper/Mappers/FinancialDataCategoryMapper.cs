﻿using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class FinancialDataCategoryMapper : Profile
    {
        public FinancialDataCategoryMapper()
        {
            CreateMap<FinancialDataCategory, FinancialDataCategoryMessage>()
                .ReverseMap();
        }
    }
}
