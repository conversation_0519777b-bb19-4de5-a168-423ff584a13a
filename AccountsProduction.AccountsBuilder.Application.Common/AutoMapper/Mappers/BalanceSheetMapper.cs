﻿using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class BalanceSheetMapper : Profile
    {
        public BalanceSheetMapper()
        {
            CreateMap<Financial, BalanceSheetMessage>()
                .ForMember(s => s.PeriodId, dest => dest.Ignore())
                .ForMember(d => d.HealthcareObligatons, opt => opt.MapFrom(s => s.HealthcareObligations))
                .ForMember(d => d.ProfitAndLossReserve, opt => opt.MapFrom(s => s.ProfitLossReserve))
                .ForMember(d => d.NonControllingInterests, opt => opt.MapFrom(s => s.NonControllingInterestsBS));
        }
    }
}
