﻿using AccountsProduction.AccountsBuilder.Domain.NoteModels;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class NotesMapper : Profile
    {
        public NotesMapper()
        {
            CreateMap<NotesResponseMessage, Notes>()
                .ForMember(x => x.CurrentPeriod, dest => dest.MapFrom(x => x.CurrentPeriodNotes))
                .ForMember(x => x.PreviousPeriod, dest => dest.MapFrom(x => x.PreviousPeriodNotes))
                .ForMember(from => from.EntityModificationTime, o => o.Ignore())
                .ReverseMap();

            CreateMap<OperatingProfitLossMessage, OperatingProfitLoss>().ReverseMap();
            CreateMap<OperatingProfitLossItemMessage, OperatingProfitLossItem>().ReverseMap();

            CreateMap<AdvancesCreditAndGuaranteesGrantedToDirectorsMessage, AdvancesCreditAndGuaranteesGrantedToDirectors>().ReverseMap();
            CreateMap<AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage, AdvancesCreditAndGuaranteesGrantedToDirectorItem>().ReverseMap();

            CreateMap<TangibleFixedAssetsNotesMessage, TangibleFixedAssetsNotes>().ReverseMap();
            CreateMap<ValuationInCurrentReportingPeriodMessage, ValuationInCurrentReportingPeriod>().ReverseMap();
            CreateMap<HistoricalCostBreakdownMessage, HistoricalCostBreakdown>().ReverseMap();
            CreateMap<AnalysisOfCostOrValuationMessage, AnalysisOfCostOrValuation>().ReverseMap();
            CreateMap<AnalysisOfCostOrValuationItemMessage, AnalysisOfCostOrValuationItem>().ReverseMap();

            CreateMap<AverageNumberOfEmployeesMessage, AverageNumberOfEmployees>().ReverseMap();

            CreateMap<MembersLiabilityTextMessage, MembersLiabilityText>().ReverseMap();
            CreateMap<AdditionalNoteMessage, AdditionalNote>().ReverseMap();

            CreateMap<NotesResponseDataMessage, IFRS105NotesData>()
                .ConstructUsing(src => new FRS105NotesData())
                .ReverseMap();
            CreateMap<NotesResponseDataMessage, IBaseNotesData>()
                .ConstructUsing(src => new FRS1021ANotesData())
                .ReverseMap();
            CreateMap<NotesResponseDataMessage, IUnincorporatedNotesData>()
                .ConstructUsing(src => new UnincorporatedNotesData())
                .ReverseMap();
            CreateMap<NotesResponseDataMessage, INotesData>()
                .ConstructUsing(src => new NotesData())
                .ReverseMap();
            CreateMap<NotesData, IFRS105NotesData>().ReverseMap();
            CreateMap<NotesData, IBaseNotesData>().ReverseMap();
            CreateMap<NotesData, IUnincorporatedNotesData>()
                .ConstructUsing(src => new UnincorporatedNotesData())
                .ReverseMap();
            CreateMap<NotesData, INotesData>()
                .ReverseMap();
            CreateMap<NotesResponseDataMessage, NotesData>().ConvertUsing<NotesResponseDataMessageToNotesDataConverter>();
            CreateMap<NotesData, NotesResponseDataMessage>();

            CreateMap<Notes, NotesResponseDataMessage>(MemberList.None);
        }
        public class NotesResponseDataMessageToNotesDataConverter : ITypeConverter<NotesResponseDataMessage, NotesData>
        {
            public NotesData Convert(NotesResponseDataMessage source, NotesData destination, ResolutionContext context)
            {
                if (source != null)
                {
                    if (context.Items != null && context.Items.TryGetValue("ReportType", out var reportType))
                    {
                        switch (reportType)
                        {
                            case ReportStandardType.FRS105:
                                return context.Mapper.Map<NotesData>(context.Mapper.Map<IFRS105NotesData>(source));
                            case ReportStandardType.FRS102_1A:
                                return context.Mapper.Map<NotesData>(context.Mapper.Map<IFRS1021ANotesData>(source));
                            case ReportStandardType.FRS102:
                                return context.Mapper.Map<NotesData>(context.Mapper.Map<IFRS102NotesData>(source));
                            case ReportStandardType.UNINCORPORATED:
                                return context.Mapper.Map<NotesData>(context.Mapper.Map<IUnincorporatedNotesData>(source));
                        }
                    }

                    return context.Mapper.Map<NotesData>(context.Mapper.Map<INotesData>(source));
                }

                return null;
            }
        }
    }
}