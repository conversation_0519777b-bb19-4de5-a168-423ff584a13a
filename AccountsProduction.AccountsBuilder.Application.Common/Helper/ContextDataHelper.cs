﻿using Iris.Elements.Logging.Serilog.Lambda;
using Iris.Elements.Messaging.Message;
using Iris.Platform.WebApi.Infrastructure.Middleware;

namespace AccountsProduction.AccountsBuilder.Application.Common.Helper
{
    public static class ContextDataHelper
    {
        public static void SetUserContextData(ElementsMessageMeta? meta, UserContext userContext)
        {
            if (meta == null) return;

            userContext.CorrelationId = meta.CorrelationId;
            userContext.TenantId = meta.TenantId;
            userContext.UserId = meta.CreatedByUserId;
        }

        public static void SetUserContextData(string tenantId, string createdByUserId, string correlationId, UserContext userContext)
        {
            userContext.CorrelationId = correlationId;
            userContext.TenantId = tenantId;
            userContext.UserId = createdByUserId;
        }

        public static void AddLogContextData(string? correlationId, string? tenantId)
        {
            LogContextExtensions.CreateLogContextProperty("CorrelationId", correlationId);
            LogContextExtensions.CreateLogContextProperty("PlatformTenantId", tenantId);
        }
    }
}
