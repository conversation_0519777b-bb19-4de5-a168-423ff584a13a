﻿
namespace AccountsProduction.AccountsBuilder.Application.Common.Helper
{
    public static class BreadcrumbSectionHelper
    {
        private static readonly Dictionary<string, string> ReportingTrialBalanceEventTypeDictionary = new Dictionary<string, string>
        {
            { ReportStandardType.FRS102_1A, BreadcrumbSection.Frs1021A},
            { ReportStandardType.FRS105, BreadcrumbSection.Frs105 },
            { ReportStandardType.FRS102, BreadcrumbSection.Frs102},
        };

        public static string ReportingBreadcrumbSection(string reportingStandardType)
        {
            return ReportingTrialBalanceEventTypeDictionary[reportingStandardType];
        }
    }
}
