﻿namespace AccountsProduction.AccountsBuilder.Application.Common.Helper;

public static class DplMappingHelper
{
    private static readonly Dictionary<string, string> ReportingTrialBalanceEventTypeDictionary = new Dictionary<string, string>
    {
        { ReportStandardType.FRS102_1A, Common.ReportingTrialBalanceEventType.FRS1021A },
        { ReportStandardType.FRS102, Common.ReportingTrialBalanceEventType.FRS102 },
        { ReportStandardType.UNINCORPORATED, Common.ReportingTrialBalanceEventType.UNINCORPORATED },
        { ReportStandardType.FRS105, Common.ReportingTrialBalanceEventType.FRS105 },
        { ReportStandardType.IFRS, Common.ReportingTrialBalanceEventType.IFRS },
        { ReportStandardType.CHARITY_SORP_FRS102, Common.ReportingTrialBalanceEventType.CHARITY_SORP_FRS102 }
    };

    public static string ReportingTrialBalanceEventType(string reportingStandardType)
    {
        return ReportingTrialBalanceEventTypeDictionary[reportingStandardType];
    }
}